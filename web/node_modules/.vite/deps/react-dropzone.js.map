{"version": 3, "file": "react-dropzone.js", "names": ["Fragment", "ReactPropTypesSecret", "printWarning", "ReactPropTypesSecret", "has", "checkPropTypes", "ReactIs", "throwOnDirectAccess", "i", "checker", "file", "_toConsumableArray", "_nonIterableSpread", "_iterableToArray", "_arrayWithoutHoles", "ownKeys", "_objectSpread", "_defineProperty", "_slicedToArray", "_nonIterableRest", "_unsupportedIterableToArray", "_arrayLikeToArray", "_iterableToArrayLimit", "_arrayWithHoles", "_accepts", "getInvalidTypeRejectionErr", "getTooLargeRejectionErr", "getTooSmallRejectionErr", "Fragment", "PropTypes", "onWindowFocus", "onDocumentDrop", "<PERSON><PERSON><PERSON><PERSON>", "composeKeyboardHandler", "composeDragHandler", "stopPropagation", "onDragEnter", "onDragOver", "onDragLeave", "onDrop"], "sources": ["../../prop-types/node_modules/react-is/cjs/react-is.development.js", "../../prop-types/node_modules/react-is/index.js", "../../object-assign/index.js", "../../prop-types/lib/ReactPropTypesSecret.js", "../../prop-types/lib/has.js", "../../prop-types/checkPropTypes.js", "../../prop-types/factoryWithTypeCheckers.js", "../../prop-types/index.js", "../../file-selector/dist/es2015/file.js", "../../file-selector/dist/es2015/file-selector.js", "../../attr-accept/dist/es/index.js", "../../react-dropzone/dist/es/utils/index.js", "../../react-dropzone/dist/es/index.js"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "export const COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\nexport function toFileWithPath(file, path, h) {\n    const f = withMimeType(file);\n    const { webkitRelativePath } = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\nfunction withMimeType(file) {\n    const { name } = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop().toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\nfunction setObjProp(f, key, value) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    });\n}\n//# sourceMappingURL=file.js.map", "import { __awaiter } from \"tslib\";\nimport { toFileWithPath } from './file';\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport function fromEvent(evt) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n            return getDataTransferFiles(evt.dataTransfer, evt.type);\n        }\n        else if (isChangeEvt(evt)) {\n            return getInputFiles(evt);\n        }\n        else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n            return getFsHandleFiles(evt);\n        }\n        return [];\n    });\n}\nfunction isDataTransfer(value) {\n    return isObject(value);\n}\nfunction isChangeEvt(value) {\n    return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n    return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n    return fromList(evt.target.files).map(file => toFileWithPath(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const files = yield Promise.all(handles.map(h => h.getFile()));\n        return files.map(file => toFileWithPath(file));\n    });\n}\nfunction getDataTransferFiles(dt, type) {\n    return __awaiter(this, void 0, void 0, function* () {\n        // IE11 does not support dataTransfer.items\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n        if (dt.items) {\n            const items = fromList(dt.items)\n                .filter(item => item.kind === 'file');\n            // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n            // only 'dragstart' and 'drop' has access to the data (source node)\n            if (type !== 'drop') {\n                return items;\n            }\n            const files = yield Promise.all(items.map(toFilePromises));\n            return noIgnoredFiles(flatten(files));\n        }\n        return noIgnoredFiles(fromList(dt.files)\n            .map(file => toFileWithPath(file)));\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    if (items === null) {\n        return [];\n    }\n    const files = [];\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n    const entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\nfunction fromDataTransferItem(item, entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n        // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n        //\n        // See:\n        // - https://issues.chromium.org/issues/40186242\n        // - https://github.com/react-dropzone/react-dropzone/issues/1397\n        if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n            const h = yield item.getAsFileSystemHandle();\n            if (h === null) {\n                throw new Error(`${item} is not a File`);\n            }\n            // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n            // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n            if (h !== undefined) {\n                const file = yield h.getFile();\n                file.handle = h;\n                return toFileWithPath(file);\n            }\n        }\n        const file = item.getAsFile();\n        if (!file) {\n            throw new Error(`${item} is not a File`);\n        }\n        const fwp = toFileWithPath(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n        return fwp;\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    const reader = entry.createReader();\n    return new Promise((resolve, reject) => {\n        const entries = [];\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries((batch) => __awaiter(this, void 0, void 0, function* () {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = yield Promise.all(entries);\n                        resolve(files);\n                    }\n                    catch (err) {\n                        reject(err);\n                    }\n                }\n                else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n                    // Continue reading\n                    readEntries();\n                }\n            }), (err) => {\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            entry.file((file) => {\n                const fwp = toFileWithPath(file, entry.fullPath);\n                resolve(fwp);\n            }, (err) => {\n                reject(err);\n            });\n        });\n    });\n}\n//# sourceMappingURL=file-selector.js.map", "\"use strict\";\n\nexports.__esModule = true;\n\nexports.default = function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n\n    if (acceptedFilesArray.length === 0) {\n      return true;\n    }\n\n    var fileName = file.name || '';\n    var mimeType = (file.type || '').toLowerCase();\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim().toLowerCase();\n\n      if (validType.charAt(0) === '.') {\n        return fileName.toLowerCase().endsWith(validType);\n      } else if (validType.endsWith('/*')) {\n        // This is something like a image/* mime type\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      }\n\n      return mimeType === validType;\n    });\n  }\n\n  return true;\n};", "function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport _accepts from \"attr-accept\";\nvar accepts = typeof _accepts === \"function\" ? _accepts : _accepts.default; // Error codes\n\nexport var FILE_INVALID_TYPE = \"file-invalid-type\";\nexport var FILE_TOO_LARGE = \"file-too-large\";\nexport var FILE_TOO_SMALL = \"file-too-small\";\nexport var TOO_MANY_FILES = \"too-many-files\";\nexport var ErrorCode = {\n  FileInvalidType: FILE_INVALID_TYPE,\n  FileTooLarge: FILE_TOO_LARGE,\n  FileTooSmall: FILE_TOO_SMALL,\n  TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */\n\nexport var getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n  var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var acceptArr = accept.split(\",\");\n  var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n  return {\n    code: FILE_INVALID_TYPE,\n    message: \"File type must be \".concat(msg)\n  };\n};\nexport var getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n  return {\n    code: FILE_TOO_LARGE,\n    message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n  return {\n    code: FILE_TOO_SMALL,\n    message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var TOO_MANY_FILES_REJECTION = {\n  code: TOO_MANY_FILES,\n  message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */\n\nexport function fileAccepted(file, accept) {\n  var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n  return [isAcceptable, isAcceptable ? null : getInvalidTypeRejectionErr(accept)];\n}\nexport function fileMatchSize(file, minSize, maxSize) {\n  if (isDefined(file.size)) {\n    if (isDefined(minSize) && isDefined(maxSize)) {\n      if (file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n      if (file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];\n    } else if (isDefined(minSize) && file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];else if (isDefined(maxSize) && file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n  }\n\n  return [true, null];\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */\n\n\nexport function allFilesAccepted(_ref) {\n  var files = _ref.files,\n      accept = _ref.accept,\n      minSize = _ref.minSize,\n      maxSize = _ref.maxSize,\n      multiple = _ref.multiple,\n      maxFiles = _ref.maxFiles,\n      validator = _ref.validator;\n\n  if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n    return false;\n  }\n\n  return files.every(function (file) {\n    var _fileAccepted = fileAccepted(file, accept),\n        _fileAccepted2 = _slicedToArray(_fileAccepted, 1),\n        accepted = _fileAccepted2[0];\n\n    var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n        _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1),\n        sizeMatch = _fileMatchSize2[0];\n\n    var customErrors = validator ? validator(file) : null;\n    return accepted && sizeMatch && !customErrors;\n  });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\n\nexport function isPropagationStopped(event) {\n  if (typeof event.isPropagationStopped === \"function\") {\n    return event.isPropagationStopped();\n  } else if (typeof event.cancelBubble !== \"undefined\") {\n    return event.cancelBubble;\n  }\n\n  return false;\n}\nexport function isEvtWithFiles(event) {\n  if (!event.dataTransfer) {\n    return !!event.target && !!event.target.files;\n  } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n\n\n  return Array.prototype.some.call(event.dataTransfer.types, function (type) {\n    return type === \"Files\" || type === \"application/x-moz-file\";\n  });\n}\nexport function isKindFile(item) {\n  return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\n\nexport function onDocumentDragOver(event) {\n  event.preventDefault();\n}\n\nfunction isIe(userAgent) {\n  return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\n\nfunction isEdge(userAgent) {\n  return userAgent.indexOf(\"Edge/\") !== -1;\n}\n\nexport function isIeOrEdge() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n  return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */\n\nexport function composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    return fns.some(function (fn) {\n      if (!isPropagationStopped(event) && fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n\n      return isPropagationStopped(event);\n    });\n  };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */\n\nexport function canUseFileSystemAccessAPI() {\n  return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */\n\nexport function pickerOptionsFromAccept(accept) {\n  if (isDefined(accept)) {\n    var acceptForPicker = Object.entries(accept).filter(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n          mimeType = _ref3[0],\n          ext = _ref3[1];\n\n      var ok = true;\n\n      if (!isMIMEType(mimeType)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.\"));\n        ok = false;\n      }\n\n      if (!Array.isArray(ext) || !ext.every(isExt)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because an invalid file extension was provided.\"));\n        ok = false;\n      }\n\n      return ok;\n    }).reduce(function (agg, _ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n          mimeType = _ref5[0],\n          ext = _ref5[1];\n\n      return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n    }, {});\n    return [{\n      // description is required due to https://crbug.com/1264708\n      description: \"Files\",\n      accept: acceptForPicker\n    }];\n  }\n\n  return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */\n\nexport function acceptPropAsAcceptAttr(accept) {\n  if (isDefined(accept)) {\n    return Object.entries(accept).reduce(function (a, _ref6) {\n      var _ref7 = _slicedToArray(_ref6, 2),\n          mimeType = _ref7[0],\n          ext = _ref7[1];\n\n      return [].concat(_toConsumableArray(a), [mimeType], _toConsumableArray(ext));\n    }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n    .filter(function (v) {\n      return isMIMEType(v) || isExt(v);\n    }).join(\",\");\n  }\n\n  return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */\n\nexport function isAbort(v) {\n  return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */\n\nexport function isSecurityError(v) {\n  return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */\n\nexport function isMIMEType(v) {\n  return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */\n\nexport function isExt(v) {\n  return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * @typedef {Object.<string, string[]>} AcceptProp\n */\n\n/**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */\n\n/**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n */", "var _excluded = [\"children\"],\n    _excluded2 = [\"open\"],\n    _excluded3 = [\"refKey\", \"role\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onClick\", \"onDragEnter\", \"onDragOver\", \"onDragLeave\", \"onDrop\"],\n    _excluded4 = [\"refKey\", \"onChange\", \"onClick\"];\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n/* eslint prefer-template: 0 */\nimport React, { forwardRef, Fragment, useCallback, useEffect, useImperativeHandle, useMemo, useReducer, useRef } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { fromEvent } from \"file-selector\";\nimport { acceptPropAsAcceptAttr, allFilesAccepted, composeEventHandlers, fileAccepted, fileMatchSize, canUseFileSystemAccessAPI, isAbort, isEvtWithFiles, isIeOrEdge, isPropagationStopped, isSecurityError, onDocumentDragOver, pickerOptionsFromAccept, TOO_MANY_FILES_REJECTION } from \"./utils/index.js\";\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */\n\nvar Dropzone = /*#__PURE__*/forwardRef(function (_ref, ref) {\n  var children = _ref.children,\n      params = _objectWithoutProperties(_ref, _excluded);\n\n  var _useDropzone = useDropzone(params),\n      open = _useDropzone.open,\n      props = _objectWithoutProperties(_useDropzone, _excluded2);\n\n  useImperativeHandle(ref, function () {\n    return {\n      open: open\n    };\n  }, [open]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n\n  return /*#__PURE__*/React.createElement(Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n    open: open\n  })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\n\nvar defaultProps = {\n  disabled: false,\n  getFilesFromEvent: fromEvent,\n  maxSize: Infinity,\n  minSize: 0,\n  multiple: true,\n  maxFiles: 0,\n  preventDropOnDocument: true,\n  noClick: false,\n  noKeyboard: false,\n  noDrag: false,\n  noDragEventsBubbling: false,\n  validator: null,\n  useFsAccessApi: false,\n  autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n  /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */\n  children: PropTypes.func,\n\n  /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */\n  accept: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),\n\n  /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */\n  multiple: PropTypes.bool,\n\n  /**\n   * If false, allow dropped items to take over the current browser window\n   */\n  preventDropOnDocument: PropTypes.bool,\n\n  /**\n   * If true, disables click to open the native file selection dialog\n   */\n  noClick: PropTypes.bool,\n\n  /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */\n  noKeyboard: PropTypes.bool,\n\n  /**\n   * If true, disables drag 'n' drop\n   */\n  noDrag: PropTypes.bool,\n\n  /**\n   * If true, stops drag event propagation to parents\n   */\n  noDragEventsBubbling: PropTypes.bool,\n\n  /**\n   * Minimum file size (in bytes)\n   */\n  minSize: PropTypes.number,\n\n  /**\n   * Maximum file size (in bytes)\n   */\n  maxSize: PropTypes.number,\n\n  /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */\n  maxFiles: PropTypes.number,\n\n  /**\n   * Enable/disable the dropzone\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  getFilesFromEvent: PropTypes.func,\n\n  /**\n   * Cb for when closing the file dialog with no selection\n   */\n  onFileDialogCancel: PropTypes.func,\n\n  /**\n   * Cb for when opening the file dialog\n   */\n  onFileDialogOpen: PropTypes.func,\n\n  /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */\n  useFsAccessApi: PropTypes.bool,\n\n  /**\n   * Set to true to focus the root element on render\n   */\n  autoFocus: PropTypes.bool,\n\n  /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */\n  onDragEnter: PropTypes.func,\n\n  /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragLeave: PropTypes.func,\n\n  /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragOver: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  onDrop: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */\n  onDropAccepted: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */\n  onDropRejected: PropTypes.func,\n\n  /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */\n  onError: PropTypes.func,\n\n  /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */\n  validator: PropTypes.func\n};\nexport default Dropzone;\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */\n\n/**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */\n\n/**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */\n\nvar initialState = {\n  isFocused: false,\n  isFileDialogActive: false,\n  isDragActive: false,\n  isDragAccept: false,\n  isDragReject: false,\n  acceptedFiles: [],\n  fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */\n\nexport function useDropzone() {\n  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n      accept = _defaultProps$props.accept,\n      disabled = _defaultProps$props.disabled,\n      getFilesFromEvent = _defaultProps$props.getFilesFromEvent,\n      maxSize = _defaultProps$props.maxSize,\n      minSize = _defaultProps$props.minSize,\n      multiple = _defaultProps$props.multiple,\n      maxFiles = _defaultProps$props.maxFiles,\n      onDragEnter = _defaultProps$props.onDragEnter,\n      onDragLeave = _defaultProps$props.onDragLeave,\n      onDragOver = _defaultProps$props.onDragOver,\n      onDrop = _defaultProps$props.onDrop,\n      onDropAccepted = _defaultProps$props.onDropAccepted,\n      onDropRejected = _defaultProps$props.onDropRejected,\n      onFileDialogCancel = _defaultProps$props.onFileDialogCancel,\n      onFileDialogOpen = _defaultProps$props.onFileDialogOpen,\n      useFsAccessApi = _defaultProps$props.useFsAccessApi,\n      autoFocus = _defaultProps$props.autoFocus,\n      preventDropOnDocument = _defaultProps$props.preventDropOnDocument,\n      noClick = _defaultProps$props.noClick,\n      noKeyboard = _defaultProps$props.noKeyboard,\n      noDrag = _defaultProps$props.noDrag,\n      noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling,\n      onError = _defaultProps$props.onError,\n      validator = _defaultProps$props.validator;\n\n  var acceptAttr = useMemo(function () {\n    return acceptPropAsAcceptAttr(accept);\n  }, [accept]);\n  var pickerTypes = useMemo(function () {\n    return pickerOptionsFromAccept(accept);\n  }, [accept]);\n  var onFileDialogOpenCb = useMemo(function () {\n    return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n  }, [onFileDialogOpen]);\n  var onFileDialogCancelCb = useMemo(function () {\n    return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n  }, [onFileDialogCancel]);\n  /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */\n\n  var rootRef = useRef(null);\n  var inputRef = useRef(null);\n\n  var _useReducer = useReducer(reducer, initialState),\n      _useReducer2 = _slicedToArray(_useReducer, 2),\n      state = _useReducer2[0],\n      dispatch = _useReducer2[1];\n\n  var isFocused = state.isFocused,\n      isFileDialogActive = state.isFileDialogActive;\n  var fsAccessApiWorksRef = useRef(typeof window !== \"undefined\" && window.isSecureContext && useFsAccessApi && canUseFileSystemAccessAPI()); // Update file dialog active state when the window is focused on\n\n  var onWindowFocus = function onWindowFocus() {\n    // Execute the timeout only if the file dialog is opened in the browser\n    if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n      setTimeout(function () {\n        if (inputRef.current) {\n          var files = inputRef.current.files;\n\n          if (!files.length) {\n            dispatch({\n              type: \"closeDialog\"\n            });\n            onFileDialogCancelCb();\n          }\n        }\n      }, 300);\n    }\n  };\n\n  useEffect(function () {\n    window.addEventListener(\"focus\", onWindowFocus, false);\n    return function () {\n      window.removeEventListener(\"focus\", onWindowFocus, false);\n    };\n  }, [inputRef, isFileDialogActive, onFileDialogCancelCb, fsAccessApiWorksRef]);\n  var dragTargetsRef = useRef([]);\n\n  var onDocumentDrop = function onDocumentDrop(event) {\n    if (rootRef.current && rootRef.current.contains(event.target)) {\n      // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n      return;\n    }\n\n    event.preventDefault();\n    dragTargetsRef.current = [];\n  };\n\n  useEffect(function () {\n    if (preventDropOnDocument) {\n      document.addEventListener(\"dragover\", onDocumentDragOver, false);\n      document.addEventListener(\"drop\", onDocumentDrop, false);\n    }\n\n    return function () {\n      if (preventDropOnDocument) {\n        document.removeEventListener(\"dragover\", onDocumentDragOver);\n        document.removeEventListener(\"drop\", onDocumentDrop);\n      }\n    };\n  }, [rootRef, preventDropOnDocument]); // Auto focus the root when autoFocus is true\n\n  useEffect(function () {\n    if (!disabled && autoFocus && rootRef.current) {\n      rootRef.current.focus();\n    }\n\n    return function () {};\n  }, [rootRef, autoFocus, disabled]);\n  var onErrCb = useCallback(function (e) {\n    if (onError) {\n      onError(e);\n    } else {\n      // Let the user know something's gone wrong if they haven't provided the onError cb.\n      console.error(e);\n    }\n  }, [onError]);\n  var onDragEnterCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [event.target]);\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        var fileCount = files.length;\n        var isDragAccept = fileCount > 0 && allFilesAccepted({\n          files: files,\n          accept: acceptAttr,\n          minSize: minSize,\n          maxSize: maxSize,\n          multiple: multiple,\n          maxFiles: maxFiles,\n          validator: validator\n        });\n        var isDragReject = fileCount > 0 && !isDragAccept;\n        dispatch({\n          isDragAccept: isDragAccept,\n          isDragReject: isDragReject,\n          isDragActive: true,\n          type: \"setDraggedFiles\"\n        });\n\n        if (onDragEnter) {\n          onDragEnter(event);\n        }\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n  }, [getFilesFromEvent, onDragEnter, onErrCb, noDragEventsBubbling, acceptAttr, minSize, maxSize, multiple, maxFiles, validator]);\n  var onDragOverCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event);\n    var hasFiles = isEvtWithFiles(event);\n\n    if (hasFiles && event.dataTransfer) {\n      try {\n        event.dataTransfer.dropEffect = \"copy\";\n      } catch (_unused) {}\n      /* eslint-disable-line no-empty */\n\n    }\n\n    if (hasFiles && onDragOver) {\n      onDragOver(event);\n    }\n\n    return false;\n  }, [onDragOver, noDragEventsBubbling]);\n  var onDragLeaveCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n\n    var targets = dragTargetsRef.current.filter(function (target) {\n      return rootRef.current && rootRef.current.contains(target);\n    }); // Make sure to remove a target present multiple times only once\n    // (Firefox may fire dragenter/dragleave multiple times on the same element)\n\n    var targetIdx = targets.indexOf(event.target);\n\n    if (targetIdx !== -1) {\n      targets.splice(targetIdx, 1);\n    }\n\n    dragTargetsRef.current = targets;\n\n    if (targets.length > 0) {\n      return;\n    }\n\n    dispatch({\n      type: \"setDraggedFiles\",\n      isDragActive: false,\n      isDragAccept: false,\n      isDragReject: false\n    });\n\n    if (isEvtWithFiles(event) && onDragLeave) {\n      onDragLeave(event);\n    }\n  }, [rootRef, onDragLeave, noDragEventsBubbling]);\n  var setFiles = useCallback(function (files, event) {\n    var acceptedFiles = [];\n    var fileRejections = [];\n    files.forEach(function (file) {\n      var _fileAccepted = fileAccepted(file, acceptAttr),\n          _fileAccepted2 = _slicedToArray(_fileAccepted, 2),\n          accepted = _fileAccepted2[0],\n          acceptError = _fileAccepted2[1];\n\n      var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n          _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2),\n          sizeMatch = _fileMatchSize2[0],\n          sizeError = _fileMatchSize2[1];\n\n      var customErrors = validator ? validator(file) : null;\n\n      if (accepted && sizeMatch && !customErrors) {\n        acceptedFiles.push(file);\n      } else {\n        var errors = [acceptError, sizeError];\n\n        if (customErrors) {\n          errors = errors.concat(customErrors);\n        }\n\n        fileRejections.push({\n          file: file,\n          errors: errors.filter(function (e) {\n            return e;\n          })\n        });\n      }\n    });\n\n    if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n      // Reject everything and empty accepted files\n      acceptedFiles.forEach(function (file) {\n        fileRejections.push({\n          file: file,\n          errors: [TOO_MANY_FILES_REJECTION]\n        });\n      });\n      acceptedFiles.splice(0);\n    }\n\n    dispatch({\n      acceptedFiles: acceptedFiles,\n      fileRejections: fileRejections,\n      isDragReject: fileRejections.length > 0,\n      type: \"setFiles\"\n    });\n\n    if (onDrop) {\n      onDrop(acceptedFiles, fileRejections, event);\n    }\n\n    if (fileRejections.length > 0 && onDropRejected) {\n      onDropRejected(fileRejections, event);\n    }\n\n    if (acceptedFiles.length > 0 && onDropAccepted) {\n      onDropAccepted(acceptedFiles, event);\n    }\n  }, [dispatch, multiple, acceptAttr, minSize, maxSize, maxFiles, onDrop, onDropAccepted, onDropRejected, validator]);\n  var onDropCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [];\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        setFiles(files, event);\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n\n    dispatch({\n      type: \"reset\"\n    });\n  }, [getFilesFromEvent, setFiles, onErrCb, noDragEventsBubbling]); // Fn for opening the file dialog programmatically\n\n  var openFileDialog = useCallback(function () {\n    // No point to use FS access APIs if context is not secure\n    // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n    if (fsAccessApiWorksRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n\n      var opts = {\n        multiple: multiple,\n        types: pickerTypes\n      };\n      window.showOpenFilePicker(opts).then(function (handles) {\n        return getFilesFromEvent(handles);\n      }).then(function (files) {\n        setFiles(files, null);\n        dispatch({\n          type: \"closeDialog\"\n        });\n      }).catch(function (e) {\n        // AbortError means the user canceled\n        if (isAbort(e)) {\n          onFileDialogCancelCb(e);\n          dispatch({\n            type: \"closeDialog\"\n          });\n        } else if (isSecurityError(e)) {\n          fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n          // Try using the input\n\n          if (inputRef.current) {\n            inputRef.current.value = null;\n            inputRef.current.click();\n          } else {\n            onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n          }\n        } else {\n          onErrCb(e);\n        }\n      });\n      return;\n    }\n\n    if (inputRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb();\n      inputRef.current.value = null;\n      inputRef.current.click();\n    }\n  }, [dispatch, onFileDialogOpenCb, onFileDialogCancelCb, useFsAccessApi, setFiles, onErrCb, pickerTypes, multiple]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n\n  var onKeyDownCb = useCallback(function (event) {\n    // Ignore keyboard events bubbling up the DOM tree\n    if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n      return;\n    }\n\n    if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n      event.preventDefault();\n      openFileDialog();\n    }\n  }, [rootRef, openFileDialog]); // Update focus state for the dropzone\n\n  var onFocusCb = useCallback(function () {\n    dispatch({\n      type: \"focus\"\n    });\n  }, []);\n  var onBlurCb = useCallback(function () {\n    dispatch({\n      type: \"blur\"\n    });\n  }, []); // Cb to open the file dialog when click occurs on the dropzone\n\n  var onClickCb = useCallback(function () {\n    if (noClick) {\n      return;\n    } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n    // to ensure React can handle state changes\n    // See: https://github.com/react-dropzone/react-dropzone/issues/450\n\n\n    if (isIeOrEdge()) {\n      setTimeout(openFileDialog, 0);\n    } else {\n      openFileDialog();\n    }\n  }, [noClick, openFileDialog]);\n\n  var composeHandler = function composeHandler(fn) {\n    return disabled ? null : fn;\n  };\n\n  var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n    return noKeyboard ? null : composeHandler(fn);\n  };\n\n  var composeDragHandler = function composeDragHandler(fn) {\n    return noDrag ? null : composeHandler(fn);\n  };\n\n  var stopPropagation = function stopPropagation(event) {\n    if (noDragEventsBubbling) {\n      event.stopPropagation();\n    }\n  };\n\n  var getRootProps = useMemo(function () {\n    return function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref2$refKey = _ref2.refKey,\n          refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey,\n          role = _ref2.role,\n          onKeyDown = _ref2.onKeyDown,\n          onFocus = _ref2.onFocus,\n          onBlur = _ref2.onBlur,\n          onClick = _ref2.onClick,\n          onDragEnter = _ref2.onDragEnter,\n          onDragOver = _ref2.onDragOver,\n          onDragLeave = _ref2.onDragLeave,\n          onDrop = _ref2.onDrop,\n          rest = _objectWithoutProperties(_ref2, _excluded3);\n\n      return _objectSpread(_objectSpread(_defineProperty({\n        onKeyDown: composeKeyboardHandler(composeEventHandlers(onKeyDown, onKeyDownCb)),\n        onFocus: composeKeyboardHandler(composeEventHandlers(onFocus, onFocusCb)),\n        onBlur: composeKeyboardHandler(composeEventHandlers(onBlur, onBlurCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onClickCb)),\n        onDragEnter: composeDragHandler(composeEventHandlers(onDragEnter, onDragEnterCb)),\n        onDragOver: composeDragHandler(composeEventHandlers(onDragOver, onDragOverCb)),\n        onDragLeave: composeDragHandler(composeEventHandlers(onDragLeave, onDragLeaveCb)),\n        onDrop: composeDragHandler(composeEventHandlers(onDrop, onDropCb)),\n        role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n      }, refKey, rootRef), !disabled && !noKeyboard ? {\n        tabIndex: 0\n      } : {}), rest);\n    };\n  }, [rootRef, onKeyDownCb, onFocusCb, onBlurCb, onClickCb, onDragEnterCb, onDragOverCb, onDragLeaveCb, onDropCb, noKeyboard, noDrag, disabled]);\n  var onInputElementClick = useCallback(function (event) {\n    event.stopPropagation();\n  }, []);\n  var getInputProps = useMemo(function () {\n    return function () {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref3$refKey = _ref3.refKey,\n          refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey,\n          onChange = _ref3.onChange,\n          onClick = _ref3.onClick,\n          rest = _objectWithoutProperties(_ref3, _excluded4);\n\n      var inputProps = _defineProperty({\n        accept: acceptAttr,\n        multiple: multiple,\n        type: \"file\",\n        style: {\n          border: 0,\n          clip: \"rect(0, 0, 0, 0)\",\n          clipPath: \"inset(50%)\",\n          height: \"1px\",\n          margin: \"0 -1px -1px 0\",\n          overflow: \"hidden\",\n          padding: 0,\n          position: \"absolute\",\n          width: \"1px\",\n          whiteSpace: \"nowrap\"\n        },\n        onChange: composeHandler(composeEventHandlers(onChange, onDropCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onInputElementClick)),\n        tabIndex: -1\n      }, refKey, inputRef);\n\n      return _objectSpread(_objectSpread({}, inputProps), rest);\n    };\n  }, [inputRef, accept, multiple, onDropCb, disabled]);\n  return _objectSpread(_objectSpread({}, state), {}, {\n    isFocused: isFocused && !disabled,\n    getRootProps: getRootProps,\n    getInputProps: getInputProps,\n    rootRef: rootRef,\n    inputRef: inputRef,\n    open: composeHandler(openFileDialog)\n  });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */\n\nfunction reducer(state, action) {\n  /* istanbul ignore next */\n  switch (action.type) {\n    case \"focus\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: true\n      });\n\n    case \"blur\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: false\n      });\n\n    case \"openDialog\":\n      return _objectSpread(_objectSpread({}, initialState), {}, {\n        isFileDialogActive: true\n      });\n\n    case \"closeDialog\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFileDialogActive: false\n      });\n\n    case \"setDraggedFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isDragActive: action.isDragActive,\n        isDragAccept: action.isDragAccept,\n        isDragReject: action.isDragReject\n      });\n\n    case \"setFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        acceptedFiles: action.acceptedFiles,\n        fileRejections: action.fileRejections,\n        isDragReject: action.isDragReject\n      });\n\n    case \"reset\":\n      return _objectSpread({}, initialState);\n\n    default:\n      return state;\n  }\n}\n\nfunction noop() {}\n\nexport { ErrorCode } from \"./utils/index.js\";"], "x_google_ignoreList": [0, 1], "mappings": ";;;;;;AAcE,EAAC,WAAW;AACd;EAIA,IAAI,mBAAmB,WAAW,cAAc,OAAO;EACvD,IAAI,qBAAqB,YAAY,OAAO,IAAI,gBAAgB,GAAG;EACnE,IAAI,oBAAoB,YAAY,OAAO,IAAI,eAAe,GAAG;EACjE,IAAI,sBAAsB,YAAY,OAAO,IAAI,iBAAiB,GAAG;EACrE,IAAI,yBAAyB,YAAY,OAAO,IAAI,oBAAoB,GAAG;EAC3E,IAAI,sBAAsB,YAAY,OAAO,IAAI,iBAAiB,GAAG;EACrE,IAAI,sBAAsB,YAAY,OAAO,IAAI,iBAAiB,GAAG;EACrE,IAAI,qBAAqB,YAAY,OAAO,IAAI,gBAAgB,GAAG;EAGnE,IAAI,wBAAwB,YAAY,OAAO,IAAI,mBAAmB,GAAG;EACzE,IAAI,6BAA6B,YAAY,OAAO,IAAI,wBAAwB,GAAG;EACnF,IAAI,yBAAyB,YAAY,OAAO,IAAI,oBAAoB,GAAG;EAC3E,IAAI,sBAAsB,YAAY,OAAO,IAAI,iBAAiB,GAAG;EACrE,IAAI,2BAA2B,YAAY,OAAO,IAAI,sBAAsB,GAAG;EAC/E,IAAI,kBAAkB,YAAY,OAAO,IAAI,aAAa,GAAG;EAC7D,IAAI,kBAAkB,YAAY,OAAO,IAAI,aAAa,GAAG;EAC7D,IAAI,mBAAmB,YAAY,OAAO,IAAI,cAAc,GAAG;EAC/D,IAAI,yBAAyB,YAAY,OAAO,IAAI,oBAAoB,GAAG;EAC3E,IAAI,uBAAuB,YAAY,OAAO,IAAI,kBAAkB,GAAG;EACvE,IAAI,mBAAmB,YAAY,OAAO,IAAI,cAAc,GAAG;EAE/D,SAAS,mBAAmB,MAAM;AAChC,iBAAc,SAAS,mBAAmB,SAAS,cACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,mCAAmC,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;EACnlB;EAED,SAAS,OAAO,QAAQ;AACtB,cAAW,WAAW,YAAY,WAAW,MAAM;IACjD,IAAI,WAAW,OAAO;AAEtB,YAAQ,UAAR;KACE,KAAK;MACH,IAAI,OAAO,OAAO;AAElB,cAAQ,MAAR;OACE,KAAK;OACL,KAAK;OACL,KAAK;OACL,KAAK;OACL,KAAK;OACL,KAAK,oBACH,QAAO;OAET;QACE,IAAI,eAAe,QAAQ,KAAK;AAEhC,gBAAQ,cAAR;SACE,KAAK;SACL,KAAK;SACL,KAAK;SACL,KAAK;SACL,KAAK,oBACH,QAAO;SAET,QACE,QAAO;QACV;MAEJ;KAEH,KAAK,kBACH,QAAO;IACV;GACF;AAED;EACD;EAED,IAAI,YAAY;EAChB,IAAI,iBAAiB;EACrB,IAAI,kBAAkB;EACtB,IAAI,kBAAkB;EACtB,IAAI,UAAU;EACd,IAAI,aAAa;EACjB,IAAIA,aAAW;EACf,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,SAAS;EACb,IAAI,WAAW;EACf,IAAI,aAAa;EACjB,IAAI,WAAW;EACf,IAAI,sCAAsC;EAE1C,SAAS,YAAY,QAAQ;AAEzB,QAAK,qCAAqC;AACxC,0CAAsC;AAEtC,YAAQ,QAAQ,gLAA0L;GAC3M;AAGH,UAAO,iBAAiB,OAAO,IAAI,OAAO,OAAO,KAAK;EACvD;EACD,SAAS,iBAAiB,QAAQ;AAChC,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,kBAAkB,QAAQ;AACjC,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,kBAAkB,QAAQ;AACjC,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,UAAU,QAAQ;AACzB,iBAAc,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;EAC7E;EACD,SAAS,aAAa,QAAQ;AAC5B,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,WAAW,QAAQ;AAC1B,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,OAAO,QAAQ;AACtB,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,OAAO,QAAQ;AACtB,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,SAAS,QAAQ;AACxB,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,WAAW,QAAQ;AAC1B,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,aAAa,QAAQ;AAC5B,UAAO,OAAO,OAAO,KAAK;EAC3B;EACD,SAAS,WAAW,QAAQ;AAC1B,UAAO,OAAO,OAAO,KAAK;EAC3B;AAED,UAAQ,YAAY;AACpB,UAAQ,iBAAiB;AACzB,UAAQ,kBAAkB;AAC1B,UAAQ,kBAAkB;AAC1B,UAAQ,UAAU;AAClB,UAAQ,aAAa;AACrB,UAAQ,WAAWA;AACnB,UAAQ,OAAO;AACf,UAAQ,OAAO;AACf,UAAQ,SAAS;AACjB,UAAQ,WAAW;AACnB,UAAQ,aAAa;AACrB,UAAQ,WAAW;AACnB,UAAQ,cAAc;AACtB,UAAQ,mBAAmB;AAC3B,UAAQ,oBAAoB;AAC5B,UAAQ,oBAAoB;AAC5B,UAAQ,YAAY;AACpB,UAAQ,eAAe;AACvB,UAAQ,aAAa;AACrB,UAAQ,SAAS;AACjB,UAAQ,SAAS;AACjB,UAAQ,WAAW;AACnB,UAAQ,aAAa;AACrB,UAAQ,eAAe;AACvB,UAAQ,aAAa;AACrB,UAAQ,qBAAqB;AAC7B,UAAQ,SAAS;CACd,IAAG;;;;;;AC9KJ,QAAO;;;;;;CCGT,IAAI,wBAAwB,OAAO;CACnC,IAAI,iBAAiB,OAAO,UAAU;CACtC,IAAI,mBAAmB,OAAO,UAAU;CAExC,SAAS,SAAS,KAAK;AACtB,MAAI,QAAQ,QAAQ,eACnB,OAAM,IAAI,UAAU;AAGrB,SAAO,OAAO,IAAI;CAClB;CAED,SAAS,kBAAkB;AAC1B,MAAI;AACH,QAAK,OAAO,OACX,QAAO;GAMR,IAAI,wBAAQ,IAAI,OAAO;AACvB,SAAM,KAAK;AACX,OAAI,OAAO,oBAAoB,MAAM,CAAC,OAAO,IAC5C,QAAO;GAIR,IAAI,QAAQ,CAAE;AACd,QAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IACvB,OAAM,MAAM,OAAO,aAAa,EAAE,IAAI;GAEvC,IAAI,SAAS,OAAO,oBAAoB,MAAM,CAAC,IAAI,SAAU,GAAG;AAC/D,WAAO,MAAM;GACb,EAAC;AACF,OAAI,OAAO,KAAK,GAAG,KAAK,aACvB,QAAO;GAIR,IAAI,QAAQ,CAAE;AACd,0BAAuB,MAAM,GAAG,CAAC,QAAQ,SAAU,QAAQ;AAC1D,UAAM,UAAU;GAChB,EAAC;AACF,OAAI,OAAO,KAAK,OAAO,OAAO,CAAE,GAAE,MAAM,CAAC,CAAC,KAAK,GAAG,KAChD,uBACD,QAAO;AAGR,UAAO;EACP,SAAQ,KAAK;AAEb,UAAO;EACP;CACD;AAED,QAAO,UAAU,iBAAiB,GAAG,OAAO,SAAS,SAAU,QAAQ,QAAQ;EAC9E,IAAI;EACJ,IAAI,KAAK,SAAS,OAAO;EACzB,IAAI;AAEJ,OAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,UAAO,OAAO,UAAU,GAAG;AAE3B,QAAK,IAAI,OAAO,KACf,KAAI,eAAe,KAAK,MAAM,IAAI,CACjC,IAAG,OAAO,KAAK;AAIjB,OAAI,uBAAuB;AAC1B,cAAU,sBAAsB,KAAK;AACrC,SAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ,IACnC,KAAI,iBAAiB,KAAK,MAAM,QAAQ,GAAG,CAC1C,IAAG,QAAQ,MAAM,KAAK,QAAQ;GAGhC;EACD;AAED,SAAO;CACP;;;;;;CChFD,IAAIC,yBAAuB;AAE3B,QAAO,UAAUA;;;;;;ACXjB,QAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,eAAe;;;;;;CCSpE,IAAIC,iBAAe,WAAW,CAAE;CAEW;EACzC,IAAIC;EACJ,IAAI,qBAAqB,CAAE;EAC3B,IAAIC;AAEJ,mBAAe,SAAS,MAAM;GAC5B,IAAI,UAAU,cAAc;AAC5B,cAAW,YAAY,YACrB,SAAQ,MAAM,QAAQ;AAExB,OAAI;AAIF,UAAM,IAAI,MAAM;GACjB,SAAQ,GAAG,CAAQ;EACrB;CACF;;;;;;;;;;;;CAaD,SAASC,iBAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAE1E,OAAK,IAAI,gBAAgB,UACvB,KAAI,MAAI,WAAW,aAAa,EAAE;GAChC,IAAI;AAIJ,OAAI;AAGF,eAAW,UAAU,kBAAkB,YAAY;KACjD,IAAI,MAAM,OACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,sGACQ,UAAU,gBAAgB,kGAEnH;AACD,SAAI,OAAO;AACX,WAAM;IACP;AACD,YAAQ,UAAU,cAAc,QAAQ,cAAc,eAAe,UAAU,MAAMF,uBAAqB;GAC3G,SAAQ,IAAI;AACX,YAAQ;GACT;AACD,OAAI,WAAW,iBAAiB,OAC9B,iBACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,oGACoC,QAAQ,iKAI9E;AAEH,OAAI,iBAAiB,WAAW,MAAM,WAAW,qBAAqB;AAGpE,uBAAmB,MAAM,WAAW;IAEpC,IAAI,QAAQ,WAAW,UAAU,GAAG;AAEpC,mBACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ,IAC7E;GACF;EACF;CAGN;;;;;;AAOD,kBAAe,oBAAoB,WAAW;AAE1C,uBAAqB,CAAE;CAE1B;AAED,QAAO,UAAUE;;;;;;CC7FjB,IAAIC;CACJ,IAAI;CAEJ,IAAI;CACJ,IAAI;CACJ,IAAI;CAEJ,IAAI,eAAe,WAAW,CAAE;AAG9B,gBAAe,SAAS,MAAM;EAC5B,IAAI,UAAU,cAAc;AAC5B,aAAW,YAAY,YACrB,SAAQ,MAAM,QAAQ;AAExB,MAAI;AAIF,SAAM,IAAI,MAAM;EACjB,SAAQ,GAAG,CAAE;CACf;CAGH,SAAS,+BAA+B;AACtC,SAAO;CACR;AAED,QAAO,UAAU,SAAS,gBAAgBC,uBAAqB;EAE7D,IAAI,yBAAyB,WAAW,cAAc,OAAO;EAC7D,IAAI,uBAAuB;;;;;;;;;;;;;;;EAgB3B,SAAS,cAAc,eAAe;GACpC,IAAI,aAAa,kBAAkB,mBAAmB,cAAc,oBAAoB,cAAc;AACtG,cAAW,eAAe,WACxB,QAAO;EAEV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiDD,IAAI,YAAY;EAIhB,IAAI,iBAAiB;GACnB,OAAO,2BAA2B,QAAQ;GAC1C,QAAQ,2BAA2B,SAAS;GAC5C,MAAM,2BAA2B,UAAU;GAC3C,MAAM,2BAA2B,WAAW;GAC5C,QAAQ,2BAA2B,SAAS;GAC5C,QAAQ,2BAA2B,SAAS;GAC5C,QAAQ,2BAA2B,SAAS;GAC5C,QAAQ,2BAA2B,SAAS;GAE5C,KAAK,sBAAsB;GAC3B,SAAS;GACT,SAAS,0BAA0B;GACnC,aAAa,8BAA8B;GAC3C,YAAY;GACZ,MAAM,mBAAmB;GACzB,UAAU;GACV,OAAO;GACP,WAAW;GACX,OAAO;GACP,OAAO;EACR;;;;;EAOD,SAAS,GAAG,GAAG,GAAG;AAEhB,OAAI,MAAM,EAGR,QAAO,MAAM,KAAK,IAAI,MAAM,IAAI;OAGhC,QAAO,MAAM,KAAK,MAAM;EAE3B;;;;;;;;EAUD,SAAS,cAAc,SAAS,MAAM;AACpC,QAAK,UAAU;AACf,QAAK,OAAO,eAAe,SAAS,WAAW,OAAM,CAAE;AACvD,QAAK,QAAQ;EACd;AAED,gBAAc,YAAY,MAAM;EAEhC,SAAS,2BAA2B,UAAU;GACD;IACzC,IAAI,0BAA0B,CAAE;IAChC,IAAI,6BAA6B;GAClC;GACD,SAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,oBAAgB,iBAAiB;AACjC,mBAAe,gBAAgB;AAE/B,QAAI,WAAW,sBACb;SAAIA,uBAAqB;MAEvB,IAAI,sBAAM,IAAI,MACZ;AAIF,UAAI,OAAO;AACX,YAAM;KACP,kBAA0D,YAAY,aAAa;MAElF,IAAI,WAAW,gBAAgB,MAAM;AACrC,WACG,wBAAwB,aAEzB,6BAA6B,GAC7B;AACA,oBACE,6EACuB,eAAe,gBAAgB,gBAAgB,uNAIvE;AACD,+BAAwB,YAAY;AACpC;MACD;KACF;;AAEH,QAAI,MAAM,aAAa,MAAM;AAC3B,SAAI,YAAY;AACd,UAAI,MAAM,cAAc,KACtB,QAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB;AAE5H,aAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB;KAC3H;AACD,YAAO;IACR,MACC,QAAO,SAAS,OAAO,UAAU,eAAe,UAAU,aAAa;GAE1E;GAED,IAAI,mBAAmB,UAAU,KAAK,MAAM,MAAM;AAClD,oBAAiB,aAAa,UAAU,KAAK,MAAM,KAAK;AAExD,UAAO;EACR;EAED,SAAS,2BAA2B,cAAc;GAChD,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;IAChF,IAAI,YAAY,MAAM;IACtB,IAAI,WAAW,YAAY,UAAU;AACrC,QAAI,aAAa,cAAc;KAI7B,IAAI,cAAc,eAAe,UAAU;AAE3C,YAAO,IAAI,cACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe,OAC9J,EAAe,aAAa;IAE/B;AACD,WAAO;GACR;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,uBAAuB;AAC9B,UAAO,2BAA2B,6BAA6B;EAChE;EAED,SAAS,yBAAyB,aAAa;GAC7C,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,eAAW,gBAAgB,WACzB,QAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB;IAE9F,IAAI,YAAY,MAAM;AACtB,SAAK,MAAM,QAAQ,UAAU,EAAE;KAC7B,IAAI,WAAW,YAAY,UAAU;AACrC,YAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB;IAC7I;AACD,SAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;KACzC,IAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,qBAAqB;AAClH,SAAI,iBAAiB,MACnB,QAAO;IAEV;AACD,WAAO;GACR;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,2BAA2B;GAClC,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;IACxE,IAAI,YAAY,MAAM;AACtB,SAAK,eAAe,UAAU,EAAE;KAC9B,IAAI,WAAW,YAAY,UAAU;AACrC,YAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB;IAC7I;AACD,WAAO;GACR;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,+BAA+B;GACtC,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;IACxE,IAAI,YAAY,MAAM;AACtB,SAAK,UAAQ,mBAAmB,UAAU,EAAE;KAC1C,IAAI,WAAW,YAAY,UAAU;AACrC,YAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB;IAC7I;AACD,WAAO;GACR;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,0BAA0B,eAAe;GAChD,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,UAAM,MAAM,qBAAqB,gBAAgB;KAC/C,IAAI,oBAAoB,cAAc,QAAQ;KAC9C,IAAI,kBAAkB,aAAa,MAAM,UAAU;AACnD,YAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB;IAC7M;AACD,WAAO;GACR;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,sBAAsB,gBAAgB;AAC7C,QAAK,MAAM,QAAQ,eAAe,EAAE;AAEhC,QAAI,UAAU,SAAS,EACrB,cACE,iEAAiE,UAAU,SAAS,uFAErF;QAED,cAAa,yDAAyD;AAG1E,WAAO;GACR;GAED,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;IACxE,IAAI,YAAY,MAAM;AACtB,SAAK,IAAI,IAAI,GAAG,IAAI,eAAe,QAAQ,IACzC,KAAI,GAAG,WAAW,eAAe,GAAG,CAClC,QAAO;IAIX,IAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;KAC9E,IAAI,OAAO,eAAe,MAAM;AAChC,SAAI,SAAS,SACX,QAAO,OAAO,MAAM;AAEtB,YAAO;IACR,EAAC;AACF,WAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,UAAU,GAAG,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe;GAC9L;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,0BAA0B,aAAa;GAC9C,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,eAAW,gBAAgB,WACzB,QAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB;IAE9F,IAAI,YAAY,MAAM;IACtB,IAAI,WAAW,YAAY,UAAU;AACrC,QAAI,aAAa,SACf,QAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB;AAE9I,SAAK,IAAI,OAAO,UACd,KAAI,IAAI,WAAW,IAAI,EAAE;KACvB,IAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,qBAAqB;AAChH,SAAI,iBAAiB,MACnB,QAAO;IAEV;AAEH,WAAO;GACR;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,uBAAuB,qBAAqB;AACnD,QAAK,MAAM,QAAQ,oBAAoB,EAAE;AACvC,IAAwC,aAAa,yEAAyE;AAC9H,WAAO;GACR;AAED,QAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;IACnD,IAAI,UAAU,oBAAoB;AAClC,eAAW,YAAY,YAAY;AACjC,kBACE,gGACc,yBAAyB,QAAQ,GAAG,eAAe,IAAI,IACtE;AACD,YAAO;IACR;GACF;GAED,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;IACxE,IAAI,gBAAgB,CAAE;AACtB,SAAK,IAAIC,MAAI,GAAGA,MAAI,oBAAoB,QAAQA,OAAK;KACnD,IAAIC,YAAU,oBAAoBD;KAClC,IAAI,gBAAgB,UAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,qBAAqB;AACzG,SAAI,iBAAiB,KACnB,QAAO;AAET,SAAI,cAAc,QAAQ,IAAI,cAAc,MAAM,eAAe,CAC/D,eAAc,KAAK,cAAc,KAAK,aAAa;IAEtD;IACD,IAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,KAAK,GAAG,MAAK;AACrH,WAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB;GAC/I;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,oBAAoB;GAC3B,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,SAAK,OAAO,MAAM,UAAU,CAC1B,QAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB;AAEnH,WAAO;GACR;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,UAAO,IAAI,eACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;EAE3F;EAED,SAAS,uBAAuB,YAAY;GAC1C,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;IACxE,IAAI,YAAY,MAAM;IACtB,IAAI,WAAW,YAAY,UAAU;AACrC,QAAI,aAAa,SACf,QAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB;AAE9I,SAAK,IAAI,OAAO,YAAY;KAC1B,IAAI,UAAU,WAAW;AACzB,gBAAW,YAAY,WACrB,QAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,QAAQ,CAAC;KAEnG,IAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,qBAAqB;AAC5G,SAAI,MACF,QAAO;IAEV;AACD,WAAO;GACR;AACD,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,6BAA6B,YAAY;GAChD,SAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;IACxE,IAAI,YAAY,MAAM;IACtB,IAAI,WAAW,YAAY,UAAU;AACrC,QAAI,aAAa,SACf,QAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB;IAG9I,IAAI,UAAU,OAAO,CAAE,GAAE,MAAM,WAAW,WAAW;AACrD,SAAK,IAAI,OAAO,SAAS;KACvB,IAAI,UAAU,WAAW;AACzB,SAAI,IAAI,YAAY,IAAI,WAAW,YAAY,WAC7C,QAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,QAAQ,CAAC;AAEnG,UAAK,QACH,QAAO,IAAI,cACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,WAAW,MAAM,KAAK,GAC9D,mBAAmB,KAAK,UAAU,OAAO,KAAK,WAAW,EAAE,MAAM,KAAK;KAG1E,IAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,qBAAqB;AAC5G,SAAI,MACF,QAAO;IAEV;AACD,WAAO;GACR;AAED,UAAO,2BAA2B,SAAS;EAC5C;EAED,SAAS,OAAO,WAAW;AACzB,kBAAe,WAAf;IACE,KAAK;IACL,KAAK;IACL,KAAK,YACH,QAAO;IACT,KAAK,UACH,SAAQ;IACV,KAAK;AACH,SAAI,MAAM,QAAQ,UAAU,CAC1B,QAAO,UAAU,MAAM,OAAO;AAEhC,SAAI,cAAc,QAAQ,eAAe,UAAU,CACjD,QAAO;KAGT,IAAI,aAAa,cAAc,UAAU;AACzC,SAAI,YAAY;MACd,IAAI,WAAW,WAAW,KAAK,UAAU;MACzC,IAAI;AACJ,UAAI,eAAe,UAAU,SAC3B;gBAAS,OAAO,SAAS,MAAM,EAAE,KAC/B,MAAK,OAAO,KAAK,MAAM,CACrB,QAAO;MAEV,MAGD,UAAS,OAAO,SAAS,MAAM,EAAE,MAAM;OACrC,IAAI,QAAQ,KAAK;AACjB,WAAI,OACF;aAAK,OAAO,MAAM,GAAG,CACnB,QAAO;OACR;MAEJ;KAEJ,MACC,QAAO;AAGT,YAAO;IACT,QACE,QAAO;GACV;EACF;EAED,SAAS,SAAS,UAAU,WAAW;AAErC,OAAI,aAAa,SACf,QAAO;AAIT,QAAK,UACH,QAAO;AAIT,OAAI,UAAU,qBAAqB,SACjC,QAAO;AAIT,cAAW,WAAW,cAAc,qBAAqB,OACvD,QAAO;AAGT,UAAO;EACR;EAGD,SAAS,YAAY,WAAW;GAC9B,IAAI,kBAAkB;AACtB,OAAI,MAAM,QAAQ,UAAU,CAC1B,QAAO;AAET,OAAI,qBAAqB,OAIvB,QAAO;AAET,OAAI,SAAS,UAAU,UAAU,CAC/B,QAAO;AAET,UAAO;EACR;EAID,SAAS,eAAe,WAAW;AACjC,cAAW,cAAc,eAAe,cAAc,KACpD,QAAO,KAAK;GAEd,IAAI,WAAW,YAAY,UAAU;AACrC,OAAI,aAAa,UACf;QAAI,qBAAqB,KACvB,QAAO;aACE,qBAAqB,OAC9B,QAAO;GACR;AAEH,UAAO;EACR;EAID,SAAS,yBAAyB,OAAO;GACvC,IAAI,OAAO,eAAe,MAAM;AAChC,WAAQ,MAAR;IACE,KAAK;IACL,KAAK,SACH,QAAO,QAAQ;IACjB,KAAK;IACL,KAAK;IACL,KAAK,SACH,QAAO,OAAO;IAChB,QACE,QAAO;GACV;EACF;EAGD,SAAS,aAAa,WAAW;AAC/B,QAAK,UAAU,gBAAgB,UAAU,YAAY,KACnD,QAAO;AAET,UAAO,UAAU,YAAY;EAC9B;AAED,iBAAe,iBAAiB;AAChC,iBAAe,oBAAoB,eAAe;AAClD,iBAAe,YAAY;AAE3B,SAAO;CACR;;;;;;CC1lB0C;EACzC,IAAI;EAIJ,IAAI,sBAAsB;AAC1B,SAAO,UAAU,kCAAqC,QAAQ,WAAW,oBAAoB;CAC9F;;;;;ACdD,MAAa,oBAAoB,IAAI,IAAI;CAErC,CAAC,OAAO,8CAA+C;CACvD,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,WAAY;CACpB,CAAC,MAAM,6BAA8B;CACrC,CAAC,QAAQ,6BAA8B;CACvC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,uBAAwB;CAChC,CAAC,MAAM,qCAAsC;CAC7C,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,sCAAuC;CAC/C,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,0BAA2B;CACnC,CAAC,SAAS,yBAA0B;CACpC,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,4BAA6B;CACrC,CAAC,SAAS,6BAA8B;CACxC,CAAC,MAAM,iBAAkB;CACzB,CAAC,OAAO,cAAe;CACvB,CAAC,QAAQ,cAAe;CACxB,CAAC,QAAQ,cAAe;CACxB,CAAC,OAAO,6DAA8D;CACtE,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,yCAA0C;CAClD,CAAC,QAAQ,YAAa;CACtB,CAAC,YAAY,qBAAsB;CACnC,CAAC,eAAe,8BAA+B;CAC/C,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,yBAA0B;CAClC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,WAAW,yBAA0B;CACtC,CAAC,eAAe,6BAA8B;CAC9C,CAAC,WAAW,yBAA0B;CACtC,CAAC,OAAO,sCAAuC;CAC/C,CAAC,MAAM,YAAa;CACpB,CAAC,OAAO,iBAAkB;CAC1B,CAAC,QAAQ,YAAa;CACtB,CAAC,MAAM,wBAAyB;CAChC,CAAC,OAAO,uCAAwC;CAChD,CAAC,OAAO,uCAAwC;CAChD,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,0BAA2B;CACnC,CAAC,SAAS,qBAAsB;CAChC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,iCAAkC;CAC1C,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,SAAS,qBAAsB;CAChC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,QAAQ,mCAAoC;CAC7C,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,4BAA6B;CACtC,CAAC,OAAO,oCAAqC;CAC7C,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,0BAA2B;CACpC,CAAC,OAAO,qCAAsC;CAC9C,CAAC,QAAQ,gBAAiB;CAC1B,CAAC,UAAU,0BAA2B;CACtC,CAAC,MAAM,oBAAqB;CAC5B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,KAAK,UAAW;CACjB,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,+BAAgC;CACxC,CAAC,UAAU,8CAA+C;CAC1D,CAAC,UAAU,kDAAmD;CAC9D,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,mBAAoB;CAC5B,CAAC,MAAM,UAAW;CAClB,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,wBAAyB;CACjC,CAAC,SAAS,uBAAwB;CAClC,CAAC,WAAW,8BAA+B;CAC3C,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,QAAQ,sBAAuB;CAChC,CAAC,SAAS,oCAAqC;CAC/C,CAAC,SAAS,6BAA8B;CACxC,CAAC,SAAS,4BAA6B;CACvC,CAAC,SAAS,yBAA0B;CACpC,CAAC,SAAS,yBAA0B;CACpC,CAAC,SAAS,wBAAyB;CACnC,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,gBAAiB;CACzB,CAAC,SAAS,8BAA+B;CACzC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,6BAA8B;CACtC,CAAC,QAAQ,4BAA6B;CACtC,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,wDAAyD;CACjE,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,0BAA2B;CACnC,CAAC,SAAS,0BAA2B;CACrC,CAAC,QAAQ,wCAAyC;CAClD,CAAC,QAAQ,uCAAwC;CACjD,CAAC,QAAQ,wCAAyC;CAClD,CAAC,QAAQ,wCAAyC;CAClD,CAAC,QAAQ,+BAAgC;CACzC,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,6BAA8B;CACtC,CAAC,QAAQ,iBAAkB;CAC3B,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,yCAA0C;CAClD,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,yBAA0B;CAClC,CAAC,UAAU,mBAAoB;CAC/B,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,YAAa;CACtB,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,UAAW;CACnB,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,gCAAiC;CACzC,CAAC,cAAc,gCAAiC;CAChD,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,0CAA2C;CACnD,CAAC,QAAQ,iBAAkB;CAC3B,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,UAAW;CACnB,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,UAAW;CACnB,CAAC,MAAM,sBAAuB;CAC9B,CAAC,QAAQ,eAAgB;CACzB,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,UAAW;CACnB,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,4BAA6B;CACrC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,YAAY,2BAA4B;CACzC,CAAC,YAAY,0BAA2B;CACxC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,wBAAyB;CACjC,CAAC,SAAS,qBAAsB;CAChC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,YAAa;CACrB,CAAC,UAAU,0BAA2B;CACtC,CAAC,OAAO,4BAA6B;CACrC,CAAC,QAAQ,8BAA+B;CACxC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,UAAW;CACnB,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,4BAA6B;CACrC,CAAC,4BAA4B,kCAAmC;CAChE,CAAC,QAAQ,0BAA2B;CACpC,CAAC,SAAS,0BAA2B;CACrC,CAAC,OAAO,gBAAiB;CACzB,CAAC,QAAQ,gBAAiB;CAC1B,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,oBAAqB;CAC7B,CAAC,QAAQ,kDAAmD;CAC5D,CAAC,QAAQ,yEAA0E;CACnF,CAAC,OAAO,oBAAqB;CAC7B,CAAC,QAAQ,kDAAmD;CAC5D,CAAC,QAAQ,yEAA0E;CACnF,CAAC,MAAM,yBAA0B;CACjC,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,eAAgB;CACxB,CAAC,QAAQ,iBAAkB;CAC3B,CAAC,OAAO,oBAAqB;CAC7B,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,eAAgB;CACxB,CAAC,SAAS,kBAAmB;CAC7B,CAAC,QAAQ,0BAA2B;CACpC,CAAC,OAAO,oBAAqB;CAC7B,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,0BAA2B;CACnC,CAAC,aAAa,2BAA4B;CAC1C,CAAC,aAAa,2BAA4B;CAC1C,CAAC,aAAa,2BAA4B;CAC1C,CAAC,QAAQ,wBAAyB;CAClC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,8BAA+B;CACvC,CAAC,QAAQ,wBAAyB;CAClC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,gBAAiB;CACzB,CAAC,QAAQ,sBAAuB;CAChC,CAAC,aAAa,2BAA4B;CAC1C,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,wBAAyB;CACjC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,MAAM,wBAAyB;CAChC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,8BAA+B;CACvC,CAAC,MAAM,0BAA2B;CAClC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,+BAAgC;CACxC,CAAC,KAAK,gBAAiB;CACvB,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,wBAAyB;CACjC,CAAC,QAAQ,yCAA0C;CACnD,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,aAAa,wCAAyC;CACvD,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,wBAAyB;CACjC,CAAC,MAAM,kBAAmB;CAC1B,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,oBAAqB;CAC7B,CAAC,QAAQ,YAAa;CACtB,CAAC,QAAQ,cAAe;CACxB,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,cAAe;CACvB,CAAC,MAAM,4BAA6B;CACpC,CAAC,OAAO,6BAA8B;CACtC,CAAC,MAAM,6CAA8C;CACrD,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,eAAgB;CACxB,CAAC,SAAS,4BAA6B;CACvC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,qDAAsD;CAC9D,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,2BAA4B;CACpC,CAAC,QAAQ,2BAA4B;CACrC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,yBAA0B;CAClC,CAAC,MAAM,aAAc;CACrB,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,oBAAqB;CAC7B,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,eAAgB;CACxB,CAAC,QAAQ,sCAAuC;CAChD,CAAC,OAAO,yBAA0B;CAClC,CAAC,WAAW,sBAAuB;CACnC,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,yCAA0C;CAClD,CAAC,OAAO,mBAAoB;CAC5B,CAAC,QAAQ,iBAAkB;CAC3B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,YAAY,wBAAyB;CACtC,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,wBAAyB;CACjC,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,UAAU,0BAA2B;CACtC,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,iCAAkC;CAC1C,CAAC,SAAS,sBAAuB;CACjC,CAAC,OAAO,gCAAiC;CACzC,CAAC,UAAU,yCAA0C;CACrD,CAAC,WAAW,0CAA2C;CACvD,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,qCAAsC;CAC9C,CAAC,OAAO,eAAgB;CACxB,CAAC,MAAM,mBAAoB;CAC3B,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,yBAA0B;CAClC,CAAC,MAAM,kBAAmB;CAC1B,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,KAAK,UAAW;CACjB,CAAC,QAAQ,YAAa;CACtB,CAAC,QAAQ,YAAa;CACtB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,yBAA0B;CAClC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,QAAQ,YAAa;CACtB,CAAC,SAAS,qBAAsB;CAChC,CAAC,QAAQ,YAAa;CACtB,CAAC,SAAS,qBAAsB;CAChC,CAAC,QAAQ,aAAc;CACvB,CAAC,QAAQ,2BAA4B;CACrC,CAAC,MAAM,UAAW;CAClB,CAAC,SAAS,mBAAoB;CAC9B,CAAC,OAAO,oBAAqB;CAC7B,CAAC,QAAQ,yBAA0B;CACnC,CAAC,QAAQ,yBAA0B;CACnC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,kBAAmB;CAC3B,CAAC,QAAQ,4BAA6B;CACtC,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,WAAY;CACrB,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,cAAe;CACvB,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,yCAA0C;CAClD,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,4CAA6C;CACrD,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,wBAAyB;CACjC,CAAC,MAAM,YAAa;CACpB,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,uBAAwB;CAChC,CAAC,SAAS,uBAAwB;CAClC,CAAC,WAAW,oCAAqC;CACjD,CAAC,QAAQ,uCAAwC;CACjD,CAAC,SAAS,mBAAoB;CAC9B,CAAC,OAAO,wCAAyC;CACjD,CAAC,OAAO,uCAAwC;CAChD,CAAC,OAAO,yCAA0C;CAClD,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,6CAA8C;CACtD,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,kCAAmC;CAC3C,CAAC,QAAQ,WAAY;CACrB,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,0BAA2B;CACnC,CAAC,WAAW,iCAAkC;CAC9C,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,aAAc;CACtB,CAAC,QAAQ,8BAA+B;CACxC,CAAC,QAAQ,oCAAqC;CAC9C,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,WAAY;CACrB,CAAC,QAAQ,WAAY;CACrB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,WAAY;CACpB,CAAC,MAAM,wBAAyB;CAChC,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,SAAS,mBAAoB;CAC9B,CAAC,UAAU,qBAAsB;CAEjC,CAAC,SAAS,mBAAoB;CAC9B,CAAC,UAAU,yBAA0B;CACrC,CAAC,OAAO,UAAW;CACnB,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,YAAa;CACtB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,YAAa;CACtB,CAAC,QAAQ,YAAa;CACtB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,YAAa;CACrB,CAAC,UAAU,4BAA6B;CACxC,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,wBAAyB;CAClC,CAAC,OAAO,oCAAqC;CAC7C,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,sCAAuC;CAC/C,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,gCAAiC;CACzC,CAAC,QAAQ,6BAA8B;CACvC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,2BAA4B;CACpC,CAAC,UAAU,6BAA8B;CACzC,CAAC,SAAS,qBAAsB;CAChC,CAAC,OAAO,oDAAqD;CAC7D,CAAC,OAAO,yDAA0D;CAClE,CAAC,OAAO,mCAAoC;CAC5C,CAAC,QAAQ,WAAY;CACrB,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,0BAA2B;CACnC,CAAC,UAAU,oCAAqC;CAChD,CAAC,QAAQ,YAAa;CACtB,CAAC,YAAY,4BAA6B;CAC1C,CAAC,WAAW,4BAA6B;CACzC,CAAC,aAAa,mBAAoB;CAClC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,YAAa;CACrB,CAAC,WAAW,sBAAuB;CACnC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,4BAA6B;CACtC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,+BAAgC;CACzC,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,kBAAmB;CAC3B,CAAC,MAAM,yBAA0B;CACjC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,QAAQ,yBAA0B;CACnC,CAAC,OAAO,8BAA+B;CACvC,CAAC,SAAS,4BAA6B;CACvC,CAAC,OAAO,YAAa;CACrB,CAAC,YAAY,qBAAsB;CACnC,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,0BAA2B;CACnC,CAAC,YAAY,eAAgB;CAC7B,CAAC,UAAU,wBAAyB;CACpC,CAAC,MAAM,yBAA0B;CACjC,CAAC,OAAO,4BAA6B;CACrC,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,SAAS,qBAAsB;CAChC,CAAC,MAAM,eAAgB;CACvB,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,UAAW;CACnB,CAAC,MAAM,YAAa;CACpB,CAAC,QAAQ,YAAa;CACtB,CAAC,SAAS,2BAA4B;CACtC,CAAC,YAAY,0BAA2B;CACxC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,wCAAyC;CACjD,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,QAAQ,gBAAiB;CAC1B,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,WAAY;CACrB,CAAC,OAAO,wBAAyB;CACjC,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,sCAAuC;CAC/C,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,uBAAwB;CAChC,CAAC,QAAQ,gCAAiC;CAC1C,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,iBAAkB;CAC1B,CAAC,SAAS,mBAAoB;CAC9B,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,WAAY;CACrB,CAAC,QAAQ,iBAAkB;CAC3B,CAAC,QAAQ,WAAY;CACrB,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,OAAO,oCAAqC;CAC7C,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,WAAY;CACrB,CAAC,QAAQ,YAAa;CACtB,CAAC,QAAQ,qCAAsC;CAC/C,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,oCAAqC;CAC7C,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,kBAAmB;CAC3B,CAAC,QAAQ,yBAA0B;CACnC,CAAC,MAAM,YAAa;CACpB,CAAC,SAAS,oCAAqC;CAC/C,CAAC,SAAS,4BAA6B;CACvC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,6BAA8B;CACvC,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,yBAA0B;CACnC,CAAC,YAAY,wCAAyC;CACtD,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,oCAAqC;CAC7C,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,oCAAqC;CAC7C,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,UAAU,8CAA+C;CAC1D,CAAC,MAAM,SAAU;CACjB,CAAC,MAAM,yBAA0B;CACjC,CAAC,OAAO,gCAAiC;CACzC,CAAC,MAAM,sBAAuB;CAC9B,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,YAAa;CACrB,CAAC,SAAS,mCAAoC;CAC9C,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,oCAAqC;CAC7C,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,MAAM,qBAAsB;CAC7B,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,6BAA8B;CACtC,CAAC,MAAM,uBAAwB;CAC/B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,WAAW,wCAAyC;CACrD,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,wBAAyB;CACjC,CAAC,QAAQ,mCAAoC;CAC7C,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,6CAA8C;CACtD,CAAC,OAAO,0CAA2C;CACnD,CAAC,OAAO,4CAA6C;CACrD,CAAC,QAAQ,qDAAsD;CAC/D,CAAC,OAAO,6CAA8C;CACtD,CAAC,OAAO,0CAA2C;CACnD,CAAC,OAAO,gDAAiD;CACzD,CAAC,OAAO,iDAAkD;CAC1D,CAAC,OAAO,gDAAiD;CACzD,CAAC,OAAO,yCAA0C;CAClD,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,mBAAoB;CAC7B,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,iBAAkB;CAC1B,CAAC,SAAS,uBAAwB;CAClC,CAAC,UAAU,qBAAsB;CACjC,CAAC,UAAU,qBAAsB;CACjC,CAAC,UAAU,qBAAsB;CACjC,CAAC,WAAW,qBAAsB;CAClC,CAAC,OAAO,+BAAgC;CACxC,CAAC,QAAQ,aAAc;CACvB,CAAC,QAAQ,sBAAuB;CAChC,CAAC,QAAQ,WAAY;CACrB,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,wCAAyC;CACjD,CAAC,UAAU,mDAAoD;CAC/D,CAAC,OAAO,wCAAyC;CACjD,CAAC,OAAO,mDAAoD;CAC5D,CAAC,OAAO,UAAW;CACnB,CAAC,OAAO,sDAAuD;CAC/D,CAAC,OAAO,6CAA8C;CACtD,CAAC,OAAO,mDAAoD;CAC5D,CAAC,OAAO,0DAA2D;CACnE,CAAC,OAAO,yDAA0D;CAClE,CAAC,OAAO,kDAAmD;CAC3D,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,OAAO,yCAA0C;CAClD,CAAC,KAAK,eAAgB;CACtB,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,6BAA8B;CACtC,CAAC,MAAM,mBAAoB;CAC3B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,mCAAoC;CAC5C,CAAC,SAAS,oCAAqC;CAC/C,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,yBAA0B;CAClC,CAAC,QAAQ,8BAA+B;CACxC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,wBAAyB;CACjC,CAAC,SAAS,0BAA2B;CACrC,CAAC,OAAO,cAAe;CACvB,CAAC,SAAS,4BAA6B;CACvC,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,yBAA0B;CAClC,CAAC,QAAQ,yBAA0B;CACnC,CAAC,QAAQ,yBAA0B;CACnC,CAAC,QAAQ,gCAAiC;CAC1C,CAAC,SAAS,yBAA0B;CACpC,CAAC,OAAO,cAAe;CACvB,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,WAAW,0BAA2B;CACvC,CAAC,UAAU,8BAA+B;CAC1C,CAAC,MAAM,oBAAqB;CAC5B,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,MAAM,oBAAqB;CAC5B,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,yBAA0B;CAClC,CAAC,WAAW,kCAAmC;CAC/C,CAAC,OAAO,+BAAgC;CACxC,CAAC,QAAQ,4DAA6D;CACtE,CAAC,QAAQ,uEAAwE;CACjF,CAAC,OAAO,+BAAgC;CACxC,CAAC,QAAQ,qDAAsD;CAC/D,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,+BAAgC;CACxC,CAAC,QAAQ,yDAA0D;CACnE,CAAC,QAAQ,wEAAyE;CAClF,CAAC,OAAO,wBAAyB;CACjC,CAAC,QAAQ,4DAA6D;CACtE,CAAC,QAAQ,2EAA4E;CACrF,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,wBAAyB;CACjC,CAAC,SAAS,4BAA6B;CACvC,CAAC,MAAM,wBAAyB;CAChC,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,8BAA+B;CACvC,CAAC,WAAW,sBAAuB;CACnC,CAAC,OAAO,eAAgB;CACxB,CAAC,QAAQ,2BAA4B;CACrC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,uCAAwC;CAChD,CAAC,MAAM,iBAAkB;CACzB,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,mCAAoC;CAC5C,CAAC,MAAM,mBAAoB;CAC3B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,QAAQ,uBAAwB;CACjC,CAAC,QAAQ,2BAA4B;CACrC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,oBAAqB;CAC7B,CAAC,aAAa,uCAAwC;CACtD,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,iCAAkC;CAC1C,CAAC,QAAQ,6BAA8B;CACvC,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,qCAAsC;CAC9C,CAAC,MAAM,gCAAiC;CACxC,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,qCAAsC;CAC9C,CAAC,MAAM,sBAAuB;CAC9B,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,uCAAwC;CAChD,CAAC,QAAQ,kCAAmC;CAC5C,CAAC,OAAO,qCAAsC;CAC9C,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,6BAA8B;CACtC,CAAC,QAAQ,qCAAsC;CAC/C,CAAC,QAAQ,oCAAqC;CAC9C,CAAC,MAAM,0BAA2B;CAClC,CAAC,MAAM,8BAA+B;CACtC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,QAAQ,2BAA4B;CACrC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,UAAU,8BAA+B;CAC1C,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,UAAW;CACnB,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,wBAAyB;CACjC,CAAC,QAAQ,2BAA4B;CACrC,CAAC,MAAM,wBAAyB;CAChC,CAAC,KAAK,YAAa;CACnB,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,mCAAoC;CAC5C,CAAC,QAAQ,aAAc;CACvB,CAAC,QAAQ,sBAAuB;CAChC,CAAC,MAAM,sCAAuC;CAC9C,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,8BAA+B;CACvC,CAAC,QAAQ,aAAc;CACvB,CAAC,SAAS,qBAAsB;CAChC,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,sCAAuC;CAC/C,CAAC,QAAQ,iCAAkC;CAC3C,CAAC,QAAQ,iCAAkC;CAC3C,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,qCAAsC;CAC9C,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,yBAA0B;CAClC,CAAC,QAAQ,2BAA4B;CACrC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,UAAU,uBAAwB;CACnC,CAAC,WAAW,wBAAyB;CACrC,CAAC,OAAO,oCAAqC;CAC7C,CAAC,UAAU,oCAAqC;CAChD,CAAC,UAAU,yCAA0C;CACrD,CAAC,aAAa,sCAAuC;CACrD,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,4CAA6C;CACrD,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,WAAY;CACrB,CAAC,MAAM,kBAAmB;CAC1B,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,QAAQ,WAAY;CACrB,CAAC,OAAO,qBAAsB;CAC9B,CAAC,SAAS,WAAY;CACtB,CAAC,OAAO,qBAAsB;CAC9B,CAAC,SAAS,mBAAoB;CAC9B,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,iCAAkC;CAC1C,CAAC,QAAQ,iCAAkC;CAC3C,CAAC,OAAO,uBAAwB;CAChC,CAAC,QAAQ,wBAAyB;CAClC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,QAAQ,qDAAsD;CAC/D,CAAC,QAAQ,oEAAqE;CAC9E,CAAC,QAAQ,WAAY;CACrB,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,4BAA6B;CACrC,CAAC,MAAM,qCAAsC;CAC7C,CAAC,OAAO,mCAAoC;CAC5C,CAAC,OAAO,kBAAmB;CAC3B,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,OAAO,aAAc;CACtB,CAAC,SAAS,mCAAoC;CAC9C,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,wBAAyB;CACjC,CAAC,MAAM,0BAA2B;CAClC,CAAC,OAAO,kCAAmC;CAC3C,CAAC,QAAQ,WAAY;CACrB,CAAC,OAAO,oCAAqC;CAC7C,CAAC,OAAO,4BAA6B;CACrC,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,gCAAiC;CACzC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,2BAA4B;CACpC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,0BAA2B;CACnC,CAAC,MAAM,sCAAuC;CAC9C,CAAC,OAAO,uCAAwC;CAChD,CAAC,OAAO,uCAAwC;CAChD,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,0CAA2C;CACnD,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,WAAY;CACpB,CAAC,QAAQ,gBAAiB;CAC1B,CAAC,SAAS,oBAAqB;CAC/B,CAAC,QAAQ,gBAAiB;CAC1B,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,yCAA0C;CAClD,CAAC,QAAQ,aAAc;CACvB,CAAC,UAAU,aAAc;CACzB,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,8BAA+B;CACvC,CAAC,QAAQ,8BAA+B;CACxC,CAAC,WAAW,uBAAwB;CACpC,CAAC,UAAU,sBAAuB;CAClC,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,eAAgB;CACxB,CAAC,QAAQ,eAAgB;CACzB,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,oCAAqC;CAC7C,CAAC,WAAW,sBAAuB;CACnC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,uCAAwC;CAChD,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,gCAAiC;CACzC,CAAC,KAAK,YAAa;CACnB,CAAC,MAAM,0BAA2B;CAClC,CAAC,OAAO,WAAY;CACpB,CAAC,UAAU,uBAAwB;CACnC,CAAC,OAAO,2CAA4C;CACpD,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,QAAQ,4BAA6B;CACtC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,MAAM,gCAAiC;CACxC,CAAC,WAAW,+BAAgC;CAC5C,CAAC,OAAO,qBAAsB;CAC9B,CAAC,aAAa,qBAAsB;CACpC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,QAAQ,uBAAwB;CACjC,CAAC,WAAW,uBAAwB;CACpC,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,eAAgB;CACxB,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,mBAAoB;CAC5B,CAAC,QAAQ,gCAAiC;CAC1C,CAAC,OAAO,YAAa;CACrB,CAAC,QAAQ,YAAa;CACtB,CAAC,MAAM,mBAAoB;CAC3B,CAAC,OAAO,gCAAiC;CACzC,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,WAAW,0BAA2B;CACvC,CAAC,OAAO,sCAAuC;CAC/C,CAAC,OAAO,0BAA2B;CACnC,CAAC,MAAM,YAAa;CACpB,CAAC,OAAO,yBAA0B;CAClC,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,OAAO,0BAA2B;CACnC,CAAC,MAAM,YAAa;CACpB,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,UAAW;CACnB,CAAC,OAAO,aAAc;CACtB,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,oCAAqC;CAC7C,CAAC,QAAQ,oCAAqC;CAC9C,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,4BAA6B;CACrC,CAAC,OAAO,YAAa;CACrB,CAAC,SAAS,gCAAiC;CAC3C,CAAC,SAAS,wBAAyB;CACnC,CAAC,SAAS,yCAA0C;CACpD,CAAC,SAAS,gBAAiB;CAC3B,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,oBAAqB;CAC7B,CAAC,QAAQ,8BAA+B;CACxC,CAAC,OAAO,sBAAuB;CAC/B,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,wBAAyB;CACjC,CAAC,YAAY,uBAAwB;CACrC,CAAC,QAAQ,0BAA2B;CACpC,CAAC,OAAO,eAAgB;CACxB,CAAC,QAAQ,eAAgB;CACzB,CAAC,QAAQ,eAAgB;CACzB,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,SAAS,qBAAsB;CAChC,CAAC,OAAO,2BAA4B;CACpC,CAAC,MAAM,iBAAkB;CACzB,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,oBAAqB;CAC7B,CAAC,OAAO,sBAAuB;CAC/B,CAAC,QAAQ,sBAAuB;CAChC,CAAC,QAAQ,2BAA4B;CACrC,CAAC,QAAQ,2BAA4B;CACrC,CAAC,QAAQ,wBAAyB;CAClC,CAAC,QAAQ,mBAAoB;CAC7B,CAAC,QAAQ,wBAAyB;CAClC,CAAC,QAAQ,uBAAwB;CACjC,CAAC,QAAQ,mBAAoB;CAC7B,CAAC,QAAQ,mBAAoB;CAC7B,CAAC,QAAQ,+BAAgC;CACzC,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,QAAQ,sBAAuB;CAChC,CAAC,QAAQ,kCAAmC;CAC5C,CAAC,QAAQ,0BAA2B;CACpC,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,+BAAgC;CACzC,CAAC,gBAAgB,uCAAwC;CACzD,CAAC,SAAS,YAAa;CACvB,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,cAAe;CACvB,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,kBAAmB;CAC3B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,mBAAoB;CAC5B,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,sBAAuB;CAC/B,CAAC,QAAQ,+BAAgC;CACzC,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,qCAAsC;CAC9C,CAAC,OAAO,8BAA+B;CACvC,CAAC,QAAQ,YAAa;CACtB,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,UAAW;CACnB,CAAC,OAAO,eAAgB;CACxB,CAAC,QAAQ,0BAA2B;CACpC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,oBAAqB;CAC7B,CAAC,QAAQ,8BAA+B;CACxC,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,OAAO,aAAc;CACtB,CAAC,OAAO,gBAAiB;CACzB,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,uCAAwC;CAChD,CAAC,SAAS,mBAAoB;CAC9B,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,oBAAqB;CAC7B,CAAC,QAAQ,YAAa;CACtB,CAAC,UAAU,qCAAsC;CACjD,CAAC,QAAQ,YAAa;CACtB,CAAC,eAAe,2BAA4B;CAC5C,CAAC,QAAQ,YAAa;CACtB,CAAC,MAAM,4BAA6B;CACpC,CAAC,OAAO,oBAAqB;CAC7B,CAAC,OAAO,0BAA2B;CACnC,CAAC,MAAM,eAAgB;CACvB,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,kBAAmB;CAC3B,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,QAAQ,wBAAyB;CAClC,CAAC,SAAS,gCAAiC;CAC3C,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,WAAY;CACrB,CAAC,SAAS,YAAa;CACvB,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,6BAA8B;CACtC,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,YAAa;CACrB,CAAC,OAAO,qBAAsB;CAC9B,CAAC,QAAQ,sBAAuB;CAChC,CAAC,YAAY,0BAA2B;CACxC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,gBAAiB;CACzB,CAAC,OAAO,eAAgB;CACxB,CAAC,QAAQ,uBAAwB;CACjC,CAAC,SAAS,kBAAmB;CAC7B,CAAC,QAAQ,gBAAiB;CAC1B,CAAC,SAAS,gBAAiB;CAC3B,CAAC,QAAQ,eAAgB;CACzB,CAAC,OAAO,8BAA+B;CACvC,CAAC,OAAO,qCAAsC;CAC9C,CAAC,OAAO,mCAAoC;CAC5C,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,sBAAuB;CAC/B,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,uBAAwB;CACjC,CAAC,OAAO,4CAA6C;CACrD,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,2BAA4B;CACpC,CAAC,OAAO,+BAAgC;CACxC,CAAC,OAAO,+BAAgC;CACxC,CAAC,SAAS,sBAAuB;CACjC,CAAC,OAAO,qCAAsC;CAC9C,CAAC,OAAO,yBAA0B;CAClC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,iCAAkC;CAC1C,CAAC,QAAQ,4BAA6B;CACtC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,uBAAwB;CAChC,CAAC,SAAS,uBAAwB;CAClC,CAAC,SAAS,oBAAqB;CAC/B,CAAC,OAAO,gBAAiB;CACzB,CAAC,MAAM,mBAAoB;CAC3B,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,gDAAiD;CAC1D,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,0BAA2B;CACnC,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,uDAAwD;CACjE,CAAC,QAAQ,gDAAiD;CAC1D,CAAC,QAAQ,mEAAoE;CAC7E,CAAC,OAAO,0BAA2B;CACnC,CAAC,QAAQ,mDAAoD;CAC7D,CAAC,QAAQ,sEAAuE;CAChF,CAAC,OAAO,0BAA2B;CACnC,CAAC,MAAM,UAAW;CAClB,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,yBAA0B;CAClC,CAAC,MAAM,4BAA6B;CACpC,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,yBAA0B;CAClC,CAAC,OAAO,uBAAwB;CAChC,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,wBAAyB;CACjC,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,kCAAmC;CAC3C,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,iBAAkB;CAC1B,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,4BAA6B;CACrC,CAAC,QAAQ,sBAAuB;CAChC,CAAC,OAAO,iCAAkC;CAC1C,CAAC,OAAO,oBAAqB;CAC7B,CAAC,QAAQ,oBAAqB;CAC9B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,gBAAiB;CACzB,CAAC,MAAM,kBAAmB;CAC1B,CAAC,QAAQ,WAAY;CACrB,CAAC,QAAQ,kBAAmB;CAC5B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,OAAO,WAAY;CACpB,CAAC,OAAO,iBAAkB;CAC1B,CAAC,KAAK,wBAAyB;CAC/B,CAAC,MAAM,wBAAyB;CAChC,CAAC,MAAM,wBAAyB;CAChC,CAAC,MAAM,wBAAyB;CAChC,CAAC,MAAM,wBAAyB;CAChC,CAAC,MAAM,wBAAyB;CAChC,CAAC,MAAM,wBAAyB;CAChC,CAAC,MAAM,wBAAyB;CAChC,CAAC,MAAM,wBAAyB;CAChC,CAAC,OAAO,gCAAiC;CACzC,CAAC,OAAO,iBAAkB;CAC1B,CAAC,OAAO,qBAAsB;CAC9B,CAAC,QAAQ,qBAAsB;CAC/B,CAAC,OAAO,4CAA6C;CACrD,CAAC,OAAO,kBAAmB;AAC9B;AACD,SAAgB,eAAe,MAAM,MAAM,GAAG;CAC1C,MAAM,IAAI,aAAa,KAAK;CAC5B,MAAM,EAAE,oBAAoB,GAAG;CAC/B,MAAM,WAAW,SAAS,WACpB,cAIO,uBAAuB,YAAY,mBAAmB,SAAS,IAClE,sBACC,IAAI,KAAK;AACpB,YAAW,EAAE,SAAS,SAClB,YAAW,GAAG,QAAQ,EAAE;AAE5B,KAAI,aACA,QAAO,eAAe,GAAG,UAAU;EAC/B,OAAO;EACP,UAAU;EACV,cAAc;EACd,YAAY;CACf,EAAC;AAGN,YAAW,GAAG,gBAAgB,EAAE;AAChC,QAAO;AACV;AACD,SAAS,aAAa,MAAM;CACxB,MAAM,EAAE,MAAM,GAAG;CACjB,MAAM,eAAe,QAAQ,KAAK,YAAY,IAAI,KAAK;AACvD,KAAI,iBAAiB,KAAK,MAAM;EAC5B,MAAM,MAAM,KAAK,MAAM,IAAI,CACtB,KAAK,CAAC,aAAa;EACxB,MAAM,OAAO,kBAAkB,IAAI,IAAI;AACvC,MAAI,KACA,QAAO,eAAe,MAAM,QAAQ;GAChC,OAAO;GACP,UAAU;GACV,cAAc;GACd,YAAY;EACf,EAAC;CAET;AACD,QAAO;AACV;AACD,SAAS,WAAW,GAAG,KAAK,OAAO;AAC/B,QAAO,eAAe,GAAG,KAAK;EAC1B;EACA,UAAU;EACV,cAAc;EACd,YAAY;CACf,EAAC;AACL;;;;ACpuCD,MAAM,kBAAkB,CAEpB,aACA,WACH;;;;;;;;;;;AAWD,SAAgB,UAAU,KAAK;AAC3B,QAAO,UAAU,WAAW,QAAQ,GAAG,aAAa;AAChD,MAAI,SAAS,IAAI,IAAI,eAAe,IAAI,aAAa,CACjD,QAAO,qBAAqB,IAAI,cAAc,IAAI,KAAK;WAElD,YAAY,IAAI,CACrB,QAAO,cAAc,IAAI;WAEpB,MAAM,QAAQ,IAAI,IAAI,IAAI,MAAM,UAAQ,aAAa,eAAe,KAAK,YAAY,WAAW,CACrG,QAAO,iBAAiB,IAAI;AAEhC,SAAO,CAAE;CACZ,EAAC;AACL;AACD,SAAS,eAAe,OAAO;AAC3B,QAAO,SAAS,MAAM;AACzB;AACD,SAAS,YAAY,OAAO;AACxB,QAAO,SAAS,MAAM,IAAI,SAAS,MAAM,OAAO;AACnD;AACD,SAAS,SAAS,GAAG;AACjB,eAAc,MAAM,YAAY,MAAM;AACzC;AACD,SAAS,cAAc,KAAK;AACxB,QAAO,SAAS,IAAI,OAAO,MAAM,CAAC,IAAI,UAAQ,eAAe,KAAK,CAAC;AACtE;AAED,SAAS,iBAAiB,SAAS;AAC/B,QAAO,UAAU,WAAW,QAAQ,GAAG,aAAa;EAChD,MAAM,QAAQ,MAAM,QAAQ,IAAI,QAAQ,IAAI,OAAK,EAAE,SAAS,CAAC,CAAC;AAC9D,SAAO,MAAM,IAAI,UAAQ,eAAe,KAAK,CAAC;CACjD,EAAC;AACL;AACD,SAAS,qBAAqB,IAAI,MAAM;AACpC,QAAO,UAAU,WAAW,QAAQ,GAAG,aAAa;AAGhD,MAAI,GAAG,OAAO;GACV,MAAM,QAAQ,SAAS,GAAG,MAAM,CAC3B,OAAO,UAAQ,KAAK,SAAS,OAAO;AAGzC,OAAI,SAAS,OACT,QAAO;GAEX,MAAM,QAAQ,MAAM,QAAQ,IAAI,MAAM,IAAI,eAAe,CAAC;AAC1D,UAAO,eAAe,QAAQ,MAAM,CAAC;EACxC;AACD,SAAO,eAAe,SAAS,GAAG,MAAM,CACnC,IAAI,UAAQ,eAAe,KAAK,CAAC,CAAC;CAC1C,EAAC;AACL;AACD,SAAS,eAAe,OAAO;AAC3B,QAAO,MAAM,OAAO,UAAQ,gBAAgB,QAAQ,KAAK,KAAK,KAAK,GAAG;AACzE;AAKD,SAAS,SAAS,OAAO;AACrB,KAAI,UAAU,KACV,QAAO,CAAE;CAEb,MAAM,QAAQ,CAAE;AAEhB,MAAK,IAAI,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;EACnC,MAAM,OAAO,MAAM;AACnB,QAAM,KAAK,KAAK;CACnB;AACD,QAAO;AACV;AAED,SAAS,eAAe,MAAM;AAC1B,YAAW,KAAK,qBAAqB,WACjC,QAAO,qBAAqB,KAAK;CAErC,MAAM,QAAQ,KAAK,kBAAkB;AAIrC,KAAI,SAAS,MAAM,YACf,QAAO,aAAa,MAAM;AAE9B,QAAO,qBAAqB,MAAM,MAAM;AAC3C;AACD,SAAS,QAAQ,OAAO;AACpB,QAAO,MAAM,OAAO,CAAC,KAAK,UAAU,CAChC,GAAG,KACH,GAAI,MAAM,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG,CAAC,KAAM,CACtD,GAAE,CAAE,EAAC;AACT;AACD,SAAS,qBAAqB,MAAM,OAAO;AACvC,QAAO,UAAU,WAAW,QAAQ,GAAG,aAAa;EAChD,IAAI;AAOJ,MAAI,WAAW,0BAA0B,KAAK,0BAA0B,YAAY;GAChF,MAAM,IAAI,MAAM,KAAK,uBAAuB;AAC5C,OAAI,MAAM,KACN,OAAM,IAAI,SAAS,KAAK;AAI5B,OAAI,cAAiB;IACjB,MAAME,SAAO,MAAM,EAAE,SAAS;AAC9B,WAAK,SAAS;AACd,WAAO,eAAeA,OAAK;GAC9B;EACJ;EACD,MAAM,OAAO,KAAK,WAAW;AAC7B,OAAK,KACD,OAAM,IAAI,SAAS,KAAK;EAE5B,MAAM,MAAM,eAAe,OAAO,KAAK,UAAU,QAAQ,eAAe,SAAS,IAAI,MAAM,cAAc,QAAQ,YAAY,IAAI,YAAe;AAChJ,SAAO;CACV,EAAC;AACL;AAED,SAAS,UAAU,OAAO;AACtB,QAAO,UAAU,WAAW,QAAQ,GAAG,aAAa;AAChD,SAAO,MAAM,cAAc,aAAa,MAAM,GAAG,cAAc,MAAM;CACxE,EAAC;AACL;AAED,SAAS,aAAa,OAAO;CACzB,MAAM,SAAS,MAAM,cAAc;AACnC,QAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;EACpC,MAAM,UAAU,CAAE;EAClB,SAAS,cAAc;AAGnB,UAAO,YAAY,CAAC,UAAU,UAAU,WAAW,QAAQ,GAAG,aAAa;AACvE,SAAK,MAAM,OAEP,KAAI;KACA,MAAM,QAAQ,MAAM,QAAQ,IAAI,QAAQ;AACxC,aAAQ,MAAM;IACjB,SACM,KAAK;AACR,YAAO,IAAI;IACd;SAEA;KACD,MAAM,QAAQ,QAAQ,IAAI,MAAM,IAAI,UAAU,CAAC;AAC/C,aAAQ,KAAK,MAAM;AAEnB,kBAAa;IAChB;GACJ,EAAC,EAAE,CAAC,QAAQ;AACT,WAAO,IAAI;GACd,EAAC;EACL;AACD,eAAa;CAChB;AACJ;AAED,SAAS,cAAc,OAAO;AAC1B,QAAO,UAAU,WAAW,QAAQ,GAAG,aAAa;AAChD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,SAAM,KAAK,CAAC,SAAS;IACjB,MAAM,MAAM,eAAe,MAAM,MAAM,SAAS;AAChD,YAAQ,IAAI;GACf,GAAE,CAAC,QAAQ;AACR,WAAO,IAAI;GACd,EAAC;EACL;CACJ,EAAC;AACL;;;;;AC1LD,SAAQ,aAAa;AAErB,SAAQ,UAAU,SAAU,MAAM,eAAe;AAC/C,MAAI,QAAQ,eAAe;GACzB,IAAI,qBAAqB,MAAM,QAAQ,cAAc,GAAG,gBAAgB,cAAc,MAAM,IAAI;AAEhG,OAAI,mBAAmB,WAAW,EAChC,QAAO;GAGT,IAAI,WAAW,KAAK,QAAQ;GAC5B,IAAI,WAAW,CAAC,KAAK,QAAQ,IAAI,aAAa;GAC9C,IAAI,eAAe,SAAS,QAAQ,SAAS,GAAG;AAChD,UAAO,mBAAmB,KAAK,SAAU,MAAM;IAC7C,IAAI,YAAY,KAAK,MAAM,CAAC,aAAa;AAEzC,QAAI,UAAU,OAAO,EAAE,KAAK,IAC1B,QAAO,SAAS,aAAa,CAAC,SAAS,UAAU;aACxC,UAAU,SAAS,KAAK,CAEjC,QAAO,iBAAiB,UAAU,QAAQ,SAAS,GAAG;AAGxD,WAAO,aAAa;GACrB,EAAC;EACH;AAED,SAAO;CACR;;;;;;AC9BD,SAASC,qBAAmB,KAAK;AAAE,QAAO,qBAAmB,IAAI,IAAI,mBAAiB,IAAI,IAAI,8BAA4B,IAAI,IAAI,sBAAoB;AAAG;AAEzJ,SAASC,uBAAqB;AAAE,OAAM,IAAI,UAAU;AAA0I;AAE9L,SAASC,mBAAiB,MAAM;AAAE,YAAW,WAAW,eAAe,KAAK,OAAO,aAAa,QAAQ,KAAK,iBAAiB,KAAM,QAAO,MAAM,KAAK,KAAK;AAAG;AAE9J,SAASC,qBAAmB,KAAK;AAAE,KAAI,MAAM,QAAQ,IAAI,CAAE,QAAO,oBAAkB,IAAI;AAAG;AAE3F,SAASC,UAAQ,QAAQ,gBAAgB;CAAE,IAAI,OAAO,OAAO,KAAK,OAAO;AAAE,KAAI,OAAO,uBAAuB;EAAE,IAAI,UAAU,OAAO,sBAAsB,OAAO;AAAE,qBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,UAAO,OAAO,yBAAyB,QAAQ,IAAI,CAAC;EAAa,EAAC,GAAG,KAAK,KAAK,MAAM,MAAM,QAAQ;CAAG;AAAC,QAAO;AAAO;AAErV,SAASC,gBAAc,QAAQ;AAAE,MAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;EAAE,IAAI,SAAS,QAAQ,UAAU,KAAK,UAAU,KAAK,CAAE;AAAE,MAAI,IAAI,UAAQ,OAAO,OAAO,GAAG,EAAE,CAAC,QAAQ,SAAU,KAAK;AAAE,qBAAgB,QAAQ,KAAK,OAAO,KAAK;EAAG,EAAC,GAAG,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,OAAO,CAAC,GAAG,UAAQ,OAAO,OAAO,CAAC,CAAC,QAAQ,SAAU,KAAK;AAAE,UAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,IAAI,CAAC;EAAG,EAAC;CAAG;AAAC,QAAO;AAAS;AAE1f,SAASC,kBAAgB,KAAK,KAAK,OAAO;AAAE,KAAI,OAAO,IAAO,QAAO,eAAe,KAAK,KAAK;EAAS;EAAO,YAAY;EAAM,cAAc;EAAM,UAAU;CAAM,EAAC;KAAW,KAAI,OAAO;AAAS,QAAO;AAAM;AAIjN,SAASC,iBAAe,KAAK,GAAG;AAAE,QAAO,kBAAgB,IAAI,IAAI,wBAAsB,KAAK,EAAE,IAAI,8BAA4B,KAAK,EAAE,IAAI,oBAAkB;AAAG;AAE9J,SAASC,qBAAmB;AAAE,OAAM,IAAI,UAAU;AAA+I;AAEjM,SAASC,8BAA4B,GAAG,QAAQ;AAAE,MAAK,EAAG;AAAQ,YAAW,MAAM,SAAU,QAAO,oBAAkB,GAAG,OAAO;CAAE,IAAI,IAAI,OAAO,UAAU,SAAS,KAAK,EAAE,CAAC,MAAM,GAAG,GAAG;AAAE,KAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AAAM,KAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,EAAE;AAAE,KAAI,MAAM,eAAe,2CAA2C,KAAK,EAAE,CAAE,QAAO,oBAAkB,GAAG,OAAO;AAAG;AAEha,SAASC,oBAAkB,KAAK,KAAK;AAAE,KAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAAQ,MAAK,IAAI,IAAI,GAAG,OAAO,IAAI,MAAM,MAAM,IAAI,KAAK,IAAO,MAAK,KAAK,IAAI;AAAM,QAAO;AAAO;AAEvL,SAASC,wBAAsB,KAAK,GAAG;CAAE,IAAI,KAAK,OAAO,OAAO,cAAc,WAAW,eAAe,IAAI,OAAO,aAAa,IAAI;AAAe,KAAI,MAAM,KAAM;CAAQ,IAAI,OAAO,CAAE;CAAE,IAAI,KAAK;CAAM,IAAI,KAAK;CAAO,IAAI,IAAI;AAAI,KAAI;AAAE,OAAK,KAAK,GAAG,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG,MAAM,EAAE,OAAO,KAAK,MAAM;AAAE,QAAK,KAAK,GAAG,MAAM;AAAE,OAAI,KAAK,KAAK,WAAW,EAAG;EAAQ;CAAE,SAAQ,KAAK;AAAE,OAAK;AAAM,OAAK;CAAM,UAAS;AAAE,MAAI;AAAE,QAAK,MAAM,GAAG,aAAa,KAAM,IAAG,WAAW;EAAG,UAAS;AAAE,OAAI,GAAI,OAAM;EAAK;CAAE;AAAC,QAAO;AAAO;AAEjgB,SAASC,kBAAgB,KAAK;AAAE,KAAI,MAAM,QAAQ,IAAI,CAAE,QAAO;AAAM;AAGrE,IAAI,iBAAiBC,sBAAa,aAAaA,oBAAWA,kBAAS;AAEnE,IAAW,oBAAoB;AAC/B,IAAW,iBAAiB;AAC5B,IAAW,iBAAiB;AAC5B,IAAW,iBAAiB;AAC5B,IAAW,YAAY;CACrB,iBAAiB;CACjB,cAAc;CACd,cAAc;CACd,cAAc;AACf;;;;;AAMD,IAAW,6BAA6B,SAASC,+BAA6B;CAC5E,IAAI,SAAS,UAAU,SAAS,KAAK,UAAU,gBAAmB,UAAU,KAAK;CACjF,IAAI,YAAY,OAAO,MAAM,IAAI;CACjC,IAAI,MAAM,UAAU,SAAS,IAAI,UAAU,OAAO,UAAU,KAAK,KAAK,CAAC,GAAG,UAAU;AACpF,QAAO;EACL,MAAM;EACN,SAAS,qBAAqB,OAAO,IAAI;CAC1C;AACF;AACD,IAAW,0BAA0B,SAASC,0BAAwB,SAAS;AAC7E,QAAO;EACL,MAAM;EACN,SAAS,uBAAuB,OAAO,SAAS,IAAI,CAAC,OAAO,YAAY,IAAI,SAAS,QAAQ;CAC9F;AACF;AACD,IAAW,0BAA0B,SAASC,0BAAwB,SAAS;AAC7E,QAAO;EACL,MAAM;EACN,SAAS,wBAAwB,OAAO,SAAS,IAAI,CAAC,OAAO,YAAY,IAAI,SAAS,QAAQ;CAC/F;AACF;AACD,IAAW,2BAA2B;CACpC,MAAM;CACN,SAAS;AACV;;;;;;;;;;;AAYD,SAAgB,aAAa,MAAM,QAAQ;CACzC,IAAI,eAAe,KAAK,SAAS,4BAA4B,QAAQ,MAAM,OAAO;AAClF,QAAO,CAAC,cAAc,eAAe,OAAO,2BAA2B,OAAO,AAAC;AAChF;AACD,SAAgB,cAAc,MAAM,SAAS,SAAS;AACpD,KAAI,UAAU,KAAK,KAAK,EACtB;MAAI,UAAU,QAAQ,IAAI,UAAU,QAAQ,EAAE;AAC5C,OAAI,KAAK,OAAO,QAAS,QAAO,CAAC,OAAO,wBAAwB,QAAQ,AAAC;AACzE,OAAI,KAAK,OAAO,QAAS,QAAO,CAAC,OAAO,wBAAwB,QAAQ,AAAC;EAC1E,WAAU,UAAU,QAAQ,IAAI,KAAK,OAAO,QAAS,QAAO,CAAC,OAAO,wBAAwB,QAAQ,AAAC;WAAU,UAAU,QAAQ,IAAI,KAAK,OAAO,QAAS,QAAO,CAAC,OAAO,wBAAwB,QAAQ,AAAC;CAAC;AAG9M,QAAO,CAAC,MAAM,IAAK;AACpB;AAED,SAAS,UAAU,OAAO;AACxB,QAAO,oBAAuB,UAAU;AACzC;;;;;;;;;;;;;AAeD,SAAgB,iBAAiB,MAAM;CACrC,IAAI,QAAQ,KAAK,OACb,SAAS,KAAK,QACd,UAAU,KAAK,SACf,UAAU,KAAK,SACf,WAAW,KAAK,UAChB,WAAW,KAAK,UAChB,YAAY,KAAK;AAErB,MAAK,YAAY,MAAM,SAAS,KAAK,YAAY,YAAY,KAAK,MAAM,SAAS,SAC/E,QAAO;AAGT,QAAO,MAAM,MAAM,SAAU,MAAM;EACjC,IAAI,gBAAgB,aAAa,MAAM,OAAO,EAC1C,iBAAiB,iBAAe,eAAe,EAAE,EACjD,WAAW,eAAe;EAE9B,IAAI,iBAAiB,cAAc,MAAM,SAAS,QAAQ,EACtD,kBAAkB,iBAAe,gBAAgB,EAAE,EACnD,YAAY,gBAAgB;EAEhC,IAAI,eAAe,YAAY,UAAU,KAAK,GAAG;AACjD,SAAO,YAAY,cAAc;CAClC,EAAC;AACH;AAID,SAAgB,qBAAqB,OAAO;AAC1C,YAAW,MAAM,yBAAyB,WACxC,QAAO,MAAM,sBAAsB;iBACnB,MAAM,iBAAiB,YACvC,QAAO,MAAM;AAGf,QAAO;AACR;AACD,SAAgB,eAAe,OAAO;AACpC,MAAK,MAAM,aACT,UAAS,MAAM,YAAY,MAAM,OAAO;AAK1C,QAAO,MAAM,UAAU,KAAK,KAAK,MAAM,aAAa,OAAO,SAAU,MAAM;AACzE,SAAO,SAAS,WAAW,SAAS;CACrC,EAAC;AACH;AAKD,SAAgB,mBAAmB,OAAO;AACxC,OAAM,gBAAgB;AACvB;AAED,SAAS,KAAK,WAAW;AACvB,QAAO,UAAU,QAAQ,OAAO,KAAK,MAAM,UAAU,QAAQ,WAAW,KAAK;AAC9E;AAED,SAAS,OAAO,WAAW;AACzB,QAAO,UAAU,QAAQ,QAAQ,KAAK;AACvC;AAED,SAAgB,aAAa;CAC3B,IAAI,YAAY,UAAU,SAAS,KAAK,UAAU,gBAAmB,UAAU,KAAK,OAAO,UAAU;AACrG,QAAO,KAAK,UAAU,IAAI,OAAO,UAAU;AAC5C;;;;;;;;;;;AAYD,SAAgB,uBAAuB;AACrC,MAAK,IAAI,OAAO,UAAU,QAAQ,MAAM,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAC9E,KAAI,QAAQ,UAAU;AAGxB,QAAO,SAAU,OAAO;AACtB,OAAK,IAAI,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI,QAAQ,GAAG,QAAQ,OAAO,QACxG,MAAK,QAAQ,KAAK,UAAU;AAG9B,SAAO,IAAI,KAAK,SAAU,IAAI;AAC5B,QAAK,qBAAqB,MAAM,IAAI,GAClC,IAAG,WAAW,GAAG,CAAC,KAAM,EAAC,OAAO,KAAK,CAAC;AAGxC,UAAO,qBAAqB,MAAM;EACnC,EAAC;CACH;AACF;;;;;;AAOD,SAAgB,4BAA4B;AAC1C,QAAO,wBAAwB;AAChC;;;;;;;;AASD,SAAgB,wBAAwB,QAAQ;AAC9C,KAAI,UAAU,OAAO,EAAE;EACrB,IAAI,kBAAkB,OAAO,QAAQ,OAAO,CAAC,OAAO,SAAU,OAAO;GACnE,IAAI,QAAQ,iBAAe,OAAO,EAAE,EAChC,WAAW,MAAM,IACjB,MAAM,MAAM;GAEhB,IAAI,KAAK;AAET,QAAK,WAAW,SAAS,EAAE;AACzB,YAAQ,KAAK,aAAa,OAAO,UAAU,yKAAyK,CAAC;AACrN,SAAK;GACN;AAED,QAAK,MAAM,QAAQ,IAAI,KAAK,IAAI,MAAM,MAAM,EAAE;AAC5C,YAAQ,KAAK,aAAa,OAAO,UAAU,qDAAqD,CAAC;AACjG,SAAK;GACN;AAED,UAAO;EACR,EAAC,CAAC,OAAO,SAAU,KAAK,OAAO;GAC9B,IAAI,QAAQ,iBAAe,OAAO,EAAE,EAChC,WAAW,MAAM,IACjB,MAAM,MAAM;AAEhB,UAAO,gBAAc,gBAAc,CAAE,GAAE,IAAI,EAAE,CAAE,GAAE,kBAAgB,CAAE,GAAE,UAAU,IAAI,CAAC;EACrF,GAAE,CAAE,EAAC;AACN,SAAO,CAAC;GAEN,aAAa;GACb,QAAQ;EACT,CAAC;CACH;AAED,QAAO;AACR;;;;;;AAOD,SAAgB,uBAAuB,QAAQ;AAC7C,KAAI,UAAU,OAAO,CACnB,QAAO,OAAO,QAAQ,OAAO,CAAC,OAAO,SAAU,GAAG,OAAO;EACvD,IAAI,QAAQ,iBAAe,OAAO,EAAE,EAChC,WAAW,MAAM,IACjB,MAAM,MAAM;AAEhB,SAAO,CAAE,EAAC,OAAO,qBAAmB,EAAE,EAAE,CAAC,QAAS,GAAE,qBAAmB,IAAI,CAAC;CAC7E,GAAE,CAAE,EAAC,CACL,OAAO,SAAU,GAAG;AACnB,SAAO,WAAW,EAAE,IAAI,MAAM,EAAE;CACjC,EAAC,CAAC,KAAK,IAAI;AAGd;AACD;;;;;;;;AASD,SAAgB,QAAQ,GAAG;AACzB,QAAO,aAAa,iBAAiB,EAAE,SAAS,gBAAgB,EAAE,SAAS,EAAE;AAC9E;;;;;;;;AASD,SAAgB,gBAAgB,GAAG;AACjC,QAAO,aAAa,iBAAiB,EAAE,SAAS,mBAAmB,EAAE,SAAS,EAAE;AACjF;;;;;;;;AASD,SAAgB,WAAW,GAAG;AAC5B,QAAO,MAAM,aAAa,MAAM,aAAa,MAAM,aAAa,MAAM,YAAY,MAAM,mBAAmB,iBAAiB,KAAK,EAAE;AACpI;;;;;AAMD,SAAgB,MAAM,GAAG;AACvB,QAAO,cAAc,KAAK,EAAE;AAC7B;;;;;;;;;;;;;;;;ACxUD,IAAI,YAAY,CAAC,UAAW,GACxB,aAAa,CAAC,MAAO,GACrB,aAAa;CAAC;CAAU;CAAQ;CAAa;CAAW;CAAU;CAAW;CAAe;CAAc;CAAe;AAAS,GAClI,aAAa;CAAC;CAAU;CAAY;AAAU;AAElD,SAAS,mBAAmB,KAAK;AAAE,QAAO,mBAAmB,IAAI,IAAI,iBAAiB,IAAI,IAAI,4BAA4B,IAAI,IAAI,oBAAoB;AAAG;AAEzJ,SAAS,qBAAqB;AAAE,OAAM,IAAI,UAAU;AAA0I;AAE9L,SAAS,iBAAiB,MAAM;AAAE,YAAW,WAAW,eAAe,KAAK,OAAO,aAAa,QAAQ,KAAK,iBAAiB,KAAM,QAAO,MAAM,KAAK,KAAK;AAAG;AAE9J,SAAS,mBAAmB,KAAK;AAAE,KAAI,MAAM,QAAQ,IAAI,CAAE,QAAO,kBAAkB,IAAI;AAAG;AAE3F,SAAS,eAAe,KAAK,GAAG;AAAE,QAAO,gBAAgB,IAAI,IAAI,sBAAsB,KAAK,EAAE,IAAI,4BAA4B,KAAK,EAAE,IAAI,kBAAkB;AAAG;AAE9J,SAAS,mBAAmB;AAAE,OAAM,IAAI,UAAU;AAA+I;AAEjM,SAAS,4BAA4B,GAAG,QAAQ;AAAE,MAAK,EAAG;AAAQ,YAAW,MAAM,SAAU,QAAO,kBAAkB,GAAG,OAAO;CAAE,IAAI,IAAI,OAAO,UAAU,SAAS,KAAK,EAAE,CAAC,MAAM,GAAG,GAAG;AAAE,KAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AAAM,KAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,EAAE;AAAE,KAAI,MAAM,eAAe,2CAA2C,KAAK,EAAE,CAAE,QAAO,kBAAkB,GAAG,OAAO;AAAG;AAEha,SAAS,kBAAkB,KAAK,KAAK;AAAE,KAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAAQ,MAAK,IAAI,IAAI,GAAG,OAAO,IAAI,MAAM,MAAM,IAAI,KAAK,IAAO,MAAK,KAAK,IAAI;AAAM,QAAO;AAAO;AAEvL,SAAS,sBAAsB,KAAK,GAAG;CAAE,IAAI,KAAK,OAAO,OAAO,cAAc,WAAW,eAAe,IAAI,OAAO,aAAa,IAAI;AAAe,KAAI,MAAM,KAAM;CAAQ,IAAI,OAAO,CAAE;CAAE,IAAI,KAAK;CAAM,IAAI,KAAK;CAAO,IAAI,IAAI;AAAI,KAAI;AAAE,OAAK,KAAK,GAAG,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG,MAAM,EAAE,OAAO,KAAK,MAAM;AAAE,QAAK,KAAK,GAAG,MAAM;AAAE,OAAI,KAAK,KAAK,WAAW,EAAG;EAAQ;CAAE,SAAQ,KAAK;AAAE,OAAK;AAAM,OAAK;CAAM,UAAS;AAAE,MAAI;AAAE,QAAK,MAAM,GAAG,aAAa,KAAM,IAAG,WAAW;EAAG,UAAS;AAAE,OAAI,GAAI,OAAM;EAAK;CAAE;AAAC,QAAO;AAAO;AAEjgB,SAAS,gBAAgB,KAAK;AAAE,KAAI,MAAM,QAAQ,IAAI,CAAE,QAAO;AAAM;AAErE,SAAS,QAAQ,QAAQ,gBAAgB;CAAE,IAAI,OAAO,OAAO,KAAK,OAAO;AAAE,KAAI,OAAO,uBAAuB;EAAE,IAAI,UAAU,OAAO,sBAAsB,OAAO;AAAE,qBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,UAAO,OAAO,yBAAyB,QAAQ,IAAI,CAAC;EAAa,EAAC,GAAG,KAAK,KAAK,MAAM,MAAM,QAAQ;CAAG;AAAC,QAAO;AAAO;AAErV,SAAS,cAAc,QAAQ;AAAE,MAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;EAAE,IAAI,SAAS,QAAQ,UAAU,KAAK,UAAU,KAAK,CAAE;AAAE,MAAI,IAAI,QAAQ,OAAO,OAAO,GAAG,EAAE,CAAC,QAAQ,SAAU,KAAK;AAAE,mBAAgB,QAAQ,KAAK,OAAO,KAAK;EAAG,EAAC,GAAG,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,OAAO,CAAC,GAAG,QAAQ,OAAO,OAAO,CAAC,CAAC,QAAQ,SAAU,KAAK;AAAE,UAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,IAAI,CAAC;EAAG,EAAC;CAAG;AAAC,QAAO;AAAS;AAE1f,SAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,KAAI,OAAO,IAAO,QAAO,eAAe,KAAK,KAAK;EAAS;EAAO,YAAY;EAAM,cAAc;EAAM,UAAU;CAAM,EAAC;KAAW,KAAI,OAAO;AAAS,QAAO;AAAM;AAEjN,SAAS,yBAAyB,QAAQ,UAAU;AAAE,KAAI,UAAU,KAAM,QAAO,CAAE;CAAE,IAAI,SAAS,8BAA8B,QAAQ,SAAS;CAAE,IAAI,KAAK;AAAG,KAAI,OAAO,uBAAuB;EAAE,IAAI,mBAAmB,OAAO,sBAAsB,OAAO;AAAE,OAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,SAAM,iBAAiB;AAAI,OAAI,SAAS,QAAQ,IAAI,IAAI,EAAG;AAAU,QAAK,OAAO,UAAU,qBAAqB,KAAK,QAAQ,IAAI,CAAE;AAAU,UAAO,OAAO,OAAO;EAAO;CAAE;AAAC,QAAO;AAAS;AAE5e,SAAS,8BAA8B,QAAQ,UAAU;AAAE,KAAI,UAAU,KAAM,QAAO,CAAE;CAAE,IAAI,SAAS,CAAE;CAAE,IAAI,aAAa,OAAO,KAAK,OAAO;CAAE,IAAI,KAAK;AAAG,MAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,QAAM,WAAW;AAAI,MAAI,SAAS,QAAQ,IAAI,IAAI,EAAG;AAAU,SAAO,OAAO,OAAO;CAAO;AAAC,QAAO;AAAS;;;;;;;;;;;;;;;AAsBnT,IAAI,2BAAwB,6BAAW,SAAU,MAAM,KAAK;CAC1D,IAAI,WAAW,KAAK,UAChB,SAAS,yBAAyB,MAAM,UAAU;CAEtD,IAAI,eAAe,YAAY,OAAO,EAClC,OAAO,aAAa,MACpB,QAAQ,yBAAyB,cAAc,WAAW;AAE9D,uCAAoB,KAAK,WAAY;AACnC,SAAO,EACC,KACP;CACF,GAAE,CAAC,IAAK,EAAC;AAEV,wBAAoB,qBAAM,cAAcC,uBAAU,MAAM,SAAS,cAAc,cAAc,CAAE,GAAE,MAAM,EAAE,CAAE,GAAE,EACrG,KACP,EAAC,CAAC,CAAC;AACL,EAAC;AACF,SAAS,cAAc;AAEvB,IAAI,eAAe;CACjB,UAAU;CACV,mBAAmB;CACnB,SAAS;CACT,SAAS;CACT,UAAU;CACV,UAAU;CACV,uBAAuB;CACvB,SAAS;CACT,YAAY;CACZ,QAAQ;CACR,sBAAsB;CACtB,WAAW;CACX,gBAAgB;CAChB,WAAW;AACZ;AACD,SAAS,eAAe;AACxB,SAAS,YAAY;CAgBnB,UAAUC,0BAAU;CASpB,QAAQ,0BAAU,SAAS,0BAAU,QAAQA,0BAAU,OAAO,CAAC;CAK/D,UAAUA,0BAAU;CAKpB,uBAAuBA,0BAAU;CAKjC,SAASA,0BAAU;CAMnB,YAAYA,0BAAU;CAKtB,QAAQA,0BAAU;CAKlB,sBAAsBA,0BAAU;CAKhC,SAASA,0BAAU;CAKnB,SAASA,0BAAU;CAMnB,UAAUA,0BAAU;CAKpB,UAAUA,0BAAU;CAOpB,mBAAmBA,0BAAU;CAK7B,oBAAoBA,0BAAU;CAK9B,kBAAkBA,0BAAU;CAM5B,gBAAgBA,0BAAU;CAK1B,WAAWA,0BAAU;CAOrB,aAAaA,0BAAU;CAOvB,aAAaA,0BAAU;CAOvB,YAAYA,0BAAU;CAgCtB,QAAQA,0BAAU;CASlB,gBAAgBA,0BAAU;CAS1B,gBAAgBA,0BAAU;CAO1B,SAASA,0BAAU;CAOnB,WAAWA,0BAAU;AACtB;AACD,iBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEf,IAAI,eAAe;CACjB,WAAW;CACX,oBAAoB;CACpB,cAAc;CACd,cAAc;CACd,cAAc;CACd,eAAe,CAAE;CACjB,gBAAgB,CAAE;AACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8ED,SAAgB,cAAc;CAC5B,IAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,gBAAmB,UAAU,KAAK,CAAE;CAElF,IAAI,sBAAsB,cAAc,cAAc,CAAE,GAAE,aAAa,EAAE,MAAM,EAC3E,SAAS,oBAAoB,QAC7B,WAAW,oBAAoB,UAC/B,oBAAoB,oBAAoB,mBACxC,UAAU,oBAAoB,SAC9B,UAAU,oBAAoB,SAC9B,WAAW,oBAAoB,UAC/B,WAAW,oBAAoB,UAC/B,cAAc,oBAAoB,aAClC,cAAc,oBAAoB,aAClC,aAAa,oBAAoB,YACjC,SAAS,oBAAoB,QAC7B,iBAAiB,oBAAoB,gBACrC,iBAAiB,oBAAoB,gBACrC,qBAAqB,oBAAoB,oBACzC,mBAAmB,oBAAoB,kBACvC,iBAAiB,oBAAoB,gBACrC,YAAY,oBAAoB,WAChC,wBAAwB,oBAAoB,uBAC5C,UAAU,oBAAoB,SAC9B,aAAa,oBAAoB,YACjC,SAAS,oBAAoB,QAC7B,uBAAuB,oBAAoB,sBAC3C,UAAU,oBAAoB,SAC9B,YAAY,oBAAoB;CAEpC,IAAI,aAAa,0BAAQ,WAAY;AACnC,SAAO,uBAAuB,OAAO;CACtC,GAAE,CAAC,MAAO,EAAC;CACZ,IAAI,cAAc,0BAAQ,WAAY;AACpC,SAAO,wBAAwB,OAAO;CACvC,GAAE,CAAC,MAAO,EAAC;CACZ,IAAI,qBAAqB,0BAAQ,WAAY;AAC3C,gBAAc,qBAAqB,aAAa,mBAAmB;CACpE,GAAE,CAAC,gBAAiB,EAAC;CACtB,IAAI,uBAAuB,0BAAQ,WAAY;AAC7C,gBAAc,uBAAuB,aAAa,qBAAqB;CACxE,GAAE,CAAC,kBAAmB,EAAC;;;;;CAMxB,IAAI,UAAU,yBAAO,KAAK;CAC1B,IAAI,WAAW,yBAAO,KAAK;CAE3B,IAAI,cAAc,6BAAW,SAAS,aAAa,EAC/C,eAAe,eAAe,aAAa,EAAE,EAC7C,QAAQ,aAAa,IACrB,WAAW,aAAa;CAE5B,IAAI,YAAY,MAAM,WAClB,qBAAqB,MAAM;CAC/B,IAAI,sBAAsB,gCAAc,WAAW,eAAe,OAAO,mBAAmB,kBAAkB,2BAA2B,CAAC;CAE1I,IAAI,gBAAgB,SAASC,kBAAgB;AAE3C,OAAK,oBAAoB,WAAW,mBAClC,YAAW,WAAY;AACrB,OAAI,SAAS,SAAS;IACpB,IAAI,QAAQ,SAAS,QAAQ;AAE7B,SAAK,MAAM,QAAQ;AACjB,cAAS,EACP,MAAM,cACP,EAAC;AACF,2BAAsB;IACvB;GACF;EACF,GAAE,IAAI;CAEV;AAED,6BAAU,WAAY;AACpB,SAAO,iBAAiB,SAAS,eAAe,MAAM;AACtD,SAAO,WAAY;AACjB,UAAO,oBAAoB,SAAS,eAAe,MAAM;EAC1D;CACF,GAAE;EAAC;EAAU;EAAoB;EAAsB;CAAoB,EAAC;CAC7E,IAAI,iBAAiB,yBAAO,CAAE,EAAC;CAE/B,IAAI,iBAAiB,SAASC,iBAAe,OAAO;AAClD,MAAI,QAAQ,WAAW,QAAQ,QAAQ,SAAS,MAAM,OAAO,CAE3D;AAGF,QAAM,gBAAgB;AACtB,iBAAe,UAAU,CAAE;CAC5B;AAED,6BAAU,WAAY;AACpB,MAAI,uBAAuB;AACzB,YAAS,iBAAiB,YAAY,oBAAoB,MAAM;AAChE,YAAS,iBAAiB,QAAQ,gBAAgB,MAAM;EACzD;AAED,SAAO,WAAY;AACjB,OAAI,uBAAuB;AACzB,aAAS,oBAAoB,YAAY,mBAAmB;AAC5D,aAAS,oBAAoB,QAAQ,eAAe;GACrD;EACF;CACF,GAAE,CAAC,SAAS,qBAAsB,EAAC;AAEpC,6BAAU,WAAY;AACpB,OAAK,YAAY,aAAa,QAAQ,QACpC,SAAQ,QAAQ,OAAO;AAGzB,SAAO,WAAY,CAAE;CACtB,GAAE;EAAC;EAAS;EAAW;CAAS,EAAC;CAClC,IAAI,UAAU,8BAAY,SAAU,GAAG;AACrC,MAAI,QACF,SAAQ,EAAE;MAGV,SAAQ,MAAM,EAAE;CAEnB,GAAE,CAAC,OAAQ,EAAC;CACb,IAAI,gBAAgB,8BAAY,SAAU,OAAO;AAC/C,QAAM,gBAAgB;AAEtB,QAAM,SAAS;AACf,kBAAgB,MAAM;AACtB,iBAAe,UAAU,CAAE,EAAC,OAAO,mBAAmB,eAAe,QAAQ,EAAE,CAAC,MAAM,MAAO,EAAC;AAE9F,MAAI,eAAe,MAAM,CACvB,SAAQ,QAAQ,kBAAkB,MAAM,CAAC,CAAC,KAAK,SAAU,OAAO;AAC9D,OAAI,qBAAqB,MAAM,KAAK,qBAClC;GAGF,IAAI,YAAY,MAAM;GACtB,IAAI,eAAe,YAAY,KAAK,iBAAiB;IAC5C;IACP,QAAQ;IACC;IACA;IACC;IACA;IACC;GACZ,EAAC;GACF,IAAI,eAAe,YAAY,MAAM;AACrC,YAAS;IACO;IACA;IACd,cAAc;IACd,MAAM;GACP,EAAC;AAEF,OAAI,YACF,aAAY,MAAM;EAErB,EAAC,CAAC,MAAM,SAAU,GAAG;AACpB,UAAO,QAAQ,EAAE;EAClB,EAAC;CAEL,GAAE;EAAC;EAAmB;EAAa;EAAS;EAAsB;EAAY;EAAS;EAAS;EAAU;EAAU;CAAU,EAAC;CAChI,IAAI,eAAe,8BAAY,SAAU,OAAO;AAC9C,QAAM,gBAAgB;AACtB,QAAM,SAAS;AACf,kBAAgB,MAAM;EACtB,IAAI,WAAW,eAAe,MAAM;AAEpC,MAAI,YAAY,MAAM,aACpB,KAAI;AACF,SAAM,aAAa,aAAa;EACjC,SAAQ,SAAS,CAAE;AAKtB,MAAI,YAAY,WACd,YAAW,MAAM;AAGnB,SAAO;CACR,GAAE,CAAC,YAAY,oBAAqB,EAAC;CACtC,IAAI,gBAAgB,8BAAY,SAAU,OAAO;AAC/C,QAAM,gBAAgB;AACtB,QAAM,SAAS;AACf,kBAAgB,MAAM;EAEtB,IAAI,UAAU,eAAe,QAAQ,OAAO,SAAU,QAAQ;AAC5D,UAAO,QAAQ,WAAW,QAAQ,QAAQ,SAAS,OAAO;EAC3D,EAAC;EAGF,IAAI,YAAY,QAAQ,QAAQ,MAAM,OAAO;AAE7C,MAAI,cAAc,GAChB,SAAQ,OAAO,WAAW,EAAE;AAG9B,iBAAe,UAAU;AAEzB,MAAI,QAAQ,SAAS,EACnB;AAGF,WAAS;GACP,MAAM;GACN,cAAc;GACd,cAAc;GACd,cAAc;EACf,EAAC;AAEF,MAAI,eAAe,MAAM,IAAI,YAC3B,aAAY,MAAM;CAErB,GAAE;EAAC;EAAS;EAAa;CAAqB,EAAC;CAChD,IAAI,WAAW,8BAAY,SAAU,OAAO,OAAO;EACjD,IAAI,gBAAgB,CAAE;EACtB,IAAI,iBAAiB,CAAE;AACvB,QAAM,QAAQ,SAAU,MAAM;GAC5B,IAAI,gBAAgB,aAAa,MAAM,WAAW,EAC9C,iBAAiB,eAAe,eAAe,EAAE,EACjD,WAAW,eAAe,IAC1B,cAAc,eAAe;GAEjC,IAAI,iBAAiB,cAAc,MAAM,SAAS,QAAQ,EACtD,kBAAkB,eAAe,gBAAgB,EAAE,EACnD,YAAY,gBAAgB,IAC5B,YAAY,gBAAgB;GAEhC,IAAI,eAAe,YAAY,UAAU,KAAK,GAAG;AAEjD,OAAI,YAAY,cAAc,aAC5B,eAAc,KAAK,KAAK;QACnB;IACL,IAAI,SAAS,CAAC,aAAa,SAAU;AAErC,QAAI,aACF,UAAS,OAAO,OAAO,aAAa;AAGtC,mBAAe,KAAK;KACZ;KACN,QAAQ,OAAO,OAAO,SAAU,GAAG;AACjC,aAAO;KACR,EAAC;IACH,EAAC;GACH;EACF,EAAC;AAEF,OAAK,YAAY,cAAc,SAAS,KAAK,YAAY,YAAY,KAAK,cAAc,SAAS,UAAU;AAEzG,iBAAc,QAAQ,SAAU,MAAM;AACpC,mBAAe,KAAK;KACZ;KACN,QAAQ,CAAC,wBAAyB;IACnC,EAAC;GACH,EAAC;AACF,iBAAc,OAAO,EAAE;EACxB;AAED,WAAS;GACQ;GACC;GAChB,cAAc,eAAe,SAAS;GACtC,MAAM;EACP,EAAC;AAEF,MAAI,OACF,QAAO,eAAe,gBAAgB,MAAM;AAG9C,MAAI,eAAe,SAAS,KAAK,eAC/B,gBAAe,gBAAgB,MAAM;AAGvC,MAAI,cAAc,SAAS,KAAK,eAC9B,gBAAe,eAAe,MAAM;CAEvC,GAAE;EAAC;EAAU;EAAU;EAAY;EAAS;EAAS;EAAU;EAAQ;EAAgB;EAAgB;CAAU,EAAC;CACnH,IAAI,WAAW,8BAAY,SAAU,OAAO;AAC1C,QAAM,gBAAgB;AAEtB,QAAM,SAAS;AACf,kBAAgB,MAAM;AACtB,iBAAe,UAAU,CAAE;AAE3B,MAAI,eAAe,MAAM,CACvB,SAAQ,QAAQ,kBAAkB,MAAM,CAAC,CAAC,KAAK,SAAU,OAAO;AAC9D,OAAI,qBAAqB,MAAM,KAAK,qBAClC;AAGF,YAAS,OAAO,MAAM;EACvB,EAAC,CAAC,MAAM,SAAU,GAAG;AACpB,UAAO,QAAQ,EAAE;EAClB,EAAC;AAGJ,WAAS,EACP,MAAM,QACP,EAAC;CACH,GAAE;EAAC;EAAmB;EAAU;EAAS;CAAqB,EAAC;CAEhE,IAAI,iBAAiB,8BAAY,WAAY;AAG3C,MAAI,oBAAoB,SAAS;AAC/B,YAAS,EACP,MAAM,aACP,EAAC;AACF,uBAAoB;GAEpB,IAAI,OAAO;IACC;IACV,OAAO;GACR;AACD,UAAO,mBAAmB,KAAK,CAAC,KAAK,SAAU,SAAS;AACtD,WAAO,kBAAkB,QAAQ;GAClC,EAAC,CAAC,KAAK,SAAU,OAAO;AACvB,aAAS,OAAO,KAAK;AACrB,aAAS,EACP,MAAM,cACP,EAAC;GACH,EAAC,CAAC,MAAM,SAAU,GAAG;AAEpB,QAAI,QAAQ,EAAE,EAAE;AACd,0BAAqB,EAAE;AACvB,cAAS,EACP,MAAM,cACP,EAAC;IACH,WAAU,gBAAgB,EAAE,EAAE;AAC7B,yBAAoB,UAAU;AAG9B,SAAI,SAAS,SAAS;AACpB,eAAS,QAAQ,QAAQ;AACzB,eAAS,QAAQ,OAAO;KACzB,MACC,yBAAQ,IAAI,MAAM,iKAAiK;IAEtL,MACC,SAAQ,EAAE;GAEb,EAAC;AACF;EACD;AAED,MAAI,SAAS,SAAS;AACpB,YAAS,EACP,MAAM,aACP,EAAC;AACF,uBAAoB;AACpB,YAAS,QAAQ,QAAQ;AACzB,YAAS,QAAQ,OAAO;EACzB;CACF,GAAE;EAAC;EAAU;EAAoB;EAAsB;EAAgB;EAAU;EAAS;EAAa;CAAS,EAAC;CAElH,IAAI,cAAc,8BAAY,SAAU,OAAO;AAE7C,OAAK,QAAQ,YAAY,QAAQ,QAAQ,YAAY,MAAM,OAAO,CAChE;AAGF,MAAI,MAAM,QAAQ,OAAO,MAAM,QAAQ,WAAW,MAAM,YAAY,MAAM,MAAM,YAAY,IAAI;AAC9F,SAAM,gBAAgB;AACtB,mBAAgB;EACjB;CACF,GAAE,CAAC,SAAS,cAAe,EAAC;CAE7B,IAAI,YAAY,8BAAY,WAAY;AACtC,WAAS,EACP,MAAM,QACP,EAAC;CACH,GAAE,CAAE,EAAC;CACN,IAAI,WAAW,8BAAY,WAAY;AACrC,WAAS,EACP,MAAM,OACP,EAAC;CACH,GAAE,CAAE,EAAC;CAEN,IAAI,YAAY,8BAAY,WAAY;AACtC,MAAI,QACF;AAMF,MAAI,YAAY,CACd,YAAW,gBAAgB,EAAE;MAE7B,iBAAgB;CAEnB,GAAE,CAAC,SAAS,cAAe,EAAC;CAE7B,IAAI,iBAAiB,SAASC,iBAAe,IAAI;AAC/C,SAAO,WAAW,OAAO;CAC1B;CAED,IAAI,yBAAyB,SAASC,yBAAuB,IAAI;AAC/D,SAAO,aAAa,OAAO,eAAe,GAAG;CAC9C;CAED,IAAI,qBAAqB,SAASC,qBAAmB,IAAI;AACvD,SAAO,SAAS,OAAO,eAAe,GAAG;CAC1C;CAED,IAAI,kBAAkB,SAASC,kBAAgB,OAAO;AACpD,MAAI,qBACF,OAAM,iBAAiB;CAE1B;CAED,IAAI,eAAe,0BAAQ,WAAY;AACrC,SAAO,WAAY;GACjB,IAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,gBAAmB,UAAU,KAAK,CAAE,GAC9E,eAAe,MAAM,QACrB,SAAS,sBAAsB,IAAI,QAAQ,cAC3C,OAAO,MAAM,MACb,YAAY,MAAM,WAClB,UAAU,MAAM,SAChB,SAAS,MAAM,QACf,UAAU,MAAM,SAChBC,gBAAc,MAAM,aACpBC,eAAa,MAAM,YACnBC,gBAAc,MAAM,aACpBC,WAAS,MAAM,QACf,OAAO,yBAAyB,OAAO,WAAW;AAEtD,UAAO,cAAc,cAAc,gBAAgB;IACjD,WAAW,uBAAuB,qBAAqB,WAAW,YAAY,CAAC;IAC/E,SAAS,uBAAuB,qBAAqB,SAAS,UAAU,CAAC;IACzE,QAAQ,uBAAuB,qBAAqB,QAAQ,SAAS,CAAC;IACtE,SAAS,eAAe,qBAAqB,SAAS,UAAU,CAAC;IACjE,aAAa,mBAAmB,qBAAqBH,eAAa,cAAc,CAAC;IACjF,YAAY,mBAAmB,qBAAqBC,cAAY,aAAa,CAAC;IAC9E,aAAa,mBAAmB,qBAAqBC,eAAa,cAAc,CAAC;IACjF,QAAQ,mBAAmB,qBAAqBC,UAAQ,SAAS,CAAC;IAClE,aAAa,SAAS,YAAY,SAAS,KAAK,OAAO;GACxD,GAAE,QAAQ,QAAQ,GAAG,aAAa,aAAa,EAC9C,UAAU,EACX,IAAG,CAAE,EAAC,EAAE,KAAK;EACf;CACF,GAAE;EAAC;EAAS;EAAa;EAAW;EAAU;EAAW;EAAe;EAAc;EAAe;EAAU;EAAY;EAAQ;CAAS,EAAC;CAC9I,IAAI,sBAAsB,8BAAY,SAAU,OAAO;AACrD,QAAM,iBAAiB;CACxB,GAAE,CAAE,EAAC;CACN,IAAI,gBAAgB,0BAAQ,WAAY;AACtC,SAAO,WAAY;GACjB,IAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,gBAAmB,UAAU,KAAK,CAAE,GAC9E,eAAe,MAAM,QACrB,SAAS,sBAAsB,IAAI,QAAQ,cAC3C,WAAW,MAAM,UACjB,UAAU,MAAM,SAChB,OAAO,yBAAyB,OAAO,WAAW;GAEtD,IAAI,aAAa,gBAAgB;IAC/B,QAAQ;IACE;IACV,MAAM;IACN,OAAO;KACL,QAAQ;KACR,MAAM;KACN,UAAU;KACV,QAAQ;KACR,QAAQ;KACR,UAAU;KACV,SAAS;KACT,UAAU;KACV,OAAO;KACP,YAAY;IACb;IACD,UAAU,eAAe,qBAAqB,UAAU,SAAS,CAAC;IAClE,SAAS,eAAe,qBAAqB,SAAS,oBAAoB,CAAC;IAC3E,UAAU;GACX,GAAE,QAAQ,SAAS;AAEpB,UAAO,cAAc,cAAc,CAAE,GAAE,WAAW,EAAE,KAAK;EAC1D;CACF,GAAE;EAAC;EAAU;EAAQ;EAAU;EAAU;CAAS,EAAC;AACpD,QAAO,cAAc,cAAc,CAAE,GAAE,MAAM,EAAE,CAAE,GAAE;EACjD,WAAW,cAAc;EACX;EACC;EACN;EACC;EACV,MAAM,eAAe,eAAe;CACrC,EAAC;AACH;;;;;;AAOD,SAAS,QAAQ,OAAO,QAAQ;;AAE9B,SAAQ,OAAO,MAAf;EACE,KAAK,QACH,QAAO,cAAc,cAAc,CAAE,GAAE,MAAM,EAAE,CAAE,GAAE,EACjD,WAAW,KACZ,EAAC;EAEJ,KAAK,OACH,QAAO,cAAc,cAAc,CAAE,GAAE,MAAM,EAAE,CAAE,GAAE,EACjD,WAAW,MACZ,EAAC;EAEJ,KAAK,aACH,QAAO,cAAc,cAAc,CAAE,GAAE,aAAa,EAAE,CAAE,GAAE,EACxD,oBAAoB,KACrB,EAAC;EAEJ,KAAK,cACH,QAAO,cAAc,cAAc,CAAE,GAAE,MAAM,EAAE,CAAE,GAAE,EACjD,oBAAoB,MACrB,EAAC;EAEJ,KAAK,kBACH,QAAO,cAAc,cAAc,CAAE,GAAE,MAAM,EAAE,CAAE,GAAE;GACjD,cAAc,OAAO;GACrB,cAAc,OAAO;GACrB,cAAc,OAAO;EACtB,EAAC;EAEJ,KAAK,WACH,QAAO,cAAc,cAAc,CAAE,GAAE,MAAM,EAAE,CAAE,GAAE;GACjD,eAAe,OAAO;GACtB,gBAAgB,OAAO;GACvB,cAAc,OAAO;EACtB,EAAC;EAEJ,KAAK,QACH,QAAO,cAAc,CAAE,GAAE,aAAa;EAExC,QACE,QAAO;CACV;AACF;AAED,SAAS,OAAO,CAAE"}