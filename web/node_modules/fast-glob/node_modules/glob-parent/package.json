{"name": "glob-parent", "version": "5.1.2", "description": "Extract the non-magic parent path from a glob string.", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": ["<PERSON><PERSON> (https://github.com/es128)", "<PERSON> <<EMAIL>>"], "repository": "gulpjs/glob-parent", "license": "ISC", "engines": {"node": ">= 6"}, "main": "index.js", "files": ["LICENSE", "index.js"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only", "azure-pipelines": "nyc mocha --async-only --reporter xunit -O output=test.xunit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"is-glob": "^4.0.1"}, "devDependencies": {"coveralls": "^3.0.11", "eslint": "^2.13.1", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^6.0.2", "nyc": "^13.3.0"}, "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"]}