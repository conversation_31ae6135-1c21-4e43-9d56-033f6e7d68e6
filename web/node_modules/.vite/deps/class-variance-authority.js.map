{"version": 3, "file": "class-variance-authority.js", "names": ["param"], "sources": ["../../class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "mappings": ";;;AAeA,MAAM,gBAAgB,CAAC,iBAAe,UAAU,eAAe,UAAU,UAAU,IAAI,MAAM;AAC7F,MAAa,KAAK;AAClB,MAAa,MAAM,CAAC,MAAM,WAAS,CAAC,UAAQ;CACpC,IAAI;AACJ,MAAK,WAAW,QAAQ,gBAAgB,SAAS,IAAI,OAAO,aAAa,KAAM,QAAO,GAAG,MAAM,UAAU,QAAQ,eAAe,SAAS,IAAI,MAAM,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI,MAAM,UAAU;CACxN,MAAM,EAAE,UAAU,iBAAiB,GAAG;CACtC,MAAM,uBAAuB,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,YAAU;EAC9D,MAAM,cAAc,UAAU,QAAQ,eAAe,SAAS,IAAI,MAAM;EACxE,MAAM,qBAAqB,oBAAoB,QAAQ,yBAAyB,SAAS,IAAI,gBAAgB;AAC7G,MAAI,gBAAgB,KAAM,QAAO;EACjC,MAAM,aAAa,cAAc,YAAY,IAAI,cAAc,mBAAmB;AAClF,SAAO,SAAS,SAAS;CAC5B,EAAC;CACF,MAAM,wBAAwB,SAAS,OAAO,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK,UAAQ;EAC9E,IAAI,CAAC,KAAK,MAAM,GAAG;AACnB,MAAI,iBACA,QAAO;AAEX,MAAI,OAAO;AACX,SAAO;CACV,GAAE,CAAE,EAAC;CACN,MAAM,+BAA+B,WAAW,QAAQ,gBAAgB,SAAS,KAAK,2BAA2B,OAAO,sBAAsB,QAAQ,kCAAkC,SAAS,IAAI,yBAAyB,OAAO,CAAC,KAAK,UAAQ;EAC/O,IAAI,EAAE,OAAO,SAAS,WAAW,YAAa,GAAG,wBAAwB,GAAG;AAC5E,SAAO,OAAO,QAAQ,uBAAuB,CAAC,MAAM,CAACA,YAAQ;GACzD,IAAI,CAAC,KAAK,MAAM,GAAGA;AACnB,UAAO,MAAM,QAAQ,MAAM,GAAG,MAAM,SAAS;IACzC,GAAG;IACH,GAAG;GACN,EAAC,KAAK,GAAI;IACP,GAAG;IACH,GAAG;GACN,EAAE,SAAS;EACf,EAAC,GAAG;GACD,GAAG;GACH;GACA;EACH,IAAG;CACP,GAAE,CAAE,EAAC;AACN,QAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,eAAe,SAAS,IAAI,MAAM,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI,MAAM,UAAU;AAChM"}