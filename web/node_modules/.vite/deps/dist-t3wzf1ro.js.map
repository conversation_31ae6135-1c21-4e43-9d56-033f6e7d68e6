{"version": 3, "file": "dist-t3wzf1ro.js", "names": ["React", "Fragment2"], "sources": ["../../@radix-ui/react-slot/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../@radix-ui/react-slot/dist/index.mjs"], "sourcesContent": ["// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n"], "x_google_ignoreList": [0], "mappings": ";;;;;;AAEA,SAAS,OAAO,KAAK,OAAO;AAC1B,YAAW,QAAQ,WACjB,QAAO,IAAI,MAAM;UACR,QAAQ,QAAQ,aAAa,EACtC,KAAI,UAAU;AAEjB;AACD,SAAS,YAAY,GAAG,MAAM;AAC5B,QAAO,CAAC,SAAS;EACf,IAAI,aAAa;EACjB,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;GACjC,MAAM,UAAU,OAAO,KAAK,KAAK;AACjC,QAAK,qBAAqB,WAAW,WACnC,cAAa;AAEf,UAAO;EACR,EAAC;AACF,MAAI,WACF,QAAO,MAAM;AACX,QAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;IACxC,MAAM,UAAU,SAAS;AACzB,eAAW,WAAW,WACpB,UAAS;QAET,QAAO,KAAK,IAAI,KAAK;GAExB;EACF;CAEJ;AACF;;;;;;AC3BD,SAAS,WAAW,WAAW;CAC7B,MAAM,4BAA4B,gBAAgB,UAAU;CAC5D,MAAM,QAAQ,aAAM,WAAW,CAAC,OAAO,iBAAiB;EACtD,MAAM,EAAE,SAAU,GAAG,WAAW,GAAG;EACnC,MAAM,gBAAgB,aAAM,SAAS,QAAQ,SAAS;EACtD,MAAM,YAAY,cAAc,KAAK,YAAY;AACjD,MAAI,WAAW;GACb,MAAM,aAAa,UAAU,MAAM;GACnC,MAAM,cAAc,cAAc,IAAI,CAAC,UAAU;AAC/C,QAAI,UAAU,WAAW;AACvB,SAAI,aAAM,SAAS,MAAM,WAAW,GAAG,EAAG,QAAO,aAAM,SAAS,KAAK,KAAK;AAC1E,YAAO,aAAM,eAAe,WAAW,GAAG,WAAW,MAAM,WAAW;IACvE,MACC,QAAO;GAEV,EAAC;AACF,0BAAuB,4BAAI,WAAW;IAAE,GAAG;IAAW,KAAK;IAAc,UAAU,aAAM,eAAe,WAAW,GAAG,aAAM,aAAa,iBAAiB,GAAG,YAAY,GAAG;GAAM,EAAC;EACpL;AACD,yBAAuB,4BAAI,WAAW;GAAE,GAAG;GAAW,KAAK;GAAc;EAAU,EAAC;CACrF,EAAC;AACF,OAAM,iBAAiB,UAAU;AACjC,QAAO;AACR;AACD,IAAI,uBAAuB,WAAW,OAAO;;AAE7C,SAAS,gBAAgB,WAAW;CAClC,MAAM,YAAY,aAAM,WAAW,CAAC,OAAO,iBAAiB;EAC1D,MAAM,EAAE,SAAU,GAAG,WAAW,GAAG;AACnC,MAAI,aAAM,eAAe,SAAS,EAAE;GAClC,MAAM,cAAc,cAAc,SAAS;GAC3C,MAAM,SAAS,WAAW,WAAW,SAAS,MAAM;AACpD,OAAI,SAAS,SAASA,aAAM,SAC1B,QAAO,MAAM,eAAe,YAAY,cAAc,YAAY,GAAG;AAEvE,UAAO,aAAM,aAAa,UAAU,OAAO;EAC5C;AACD,SAAO,aAAM,SAAS,MAAM,SAAS,GAAG,IAAI,aAAM,SAAS,KAAK,KAAK,GAAG;CACzE,EAAC;AACF,WAAU,iBAAiB,UAAU;AACrC,QAAO;AACR;AACD,IAAI,uBAAuB,OAAO,kBAAkB;;AAEpD,SAAS,gBAAgB,WAAW;CAClC,MAAM,aAAa,CAAC,EAAE,UAAU,KAAK;AACnC,yBAAuB,4BAAIC,6BAAW,EAAE,SAAU,EAAC;CACpD;AACD,YAAW,iBAAiB,UAAU;AACtC,YAAW,YAAY;AACvB,QAAO;AACR;AACD,IAAI,4BAA4B,gBAAgB,YAAY;AAC5D,SAAS,YAAY,OAAO;AAC1B,QAAO,aAAM,eAAe,MAAM,WAAW,MAAM,SAAS,cAAc,eAAe,MAAM,QAAQ,MAAM,KAAK,cAAc;AACjI;AACD,SAAS,WAAW,WAAW,YAAY;CACzC,MAAM,gBAAgB,EAAE,GAAG,WAAY;AACvC,MAAK,MAAM,YAAY,YAAY;EACjC,MAAM,gBAAgB,UAAU;EAChC,MAAM,iBAAiB,WAAW;EAClC,MAAM,YAAY,WAAW,KAAK,SAAS;AAC3C,MAAI,WACF;OAAI,iBAAiB,eACnB,eAAc,YAAY,CAAC,GAAG,SAAS;IACrC,MAAM,SAAS,eAAe,GAAG,KAAK;AACtC,kBAAc,GAAG,KAAK;AACtB,WAAO;GACR;YACQ,cACT,eAAc,YAAY;EAC3B,WACQ,aAAa,QACtB,eAAc,YAAY;GAAE,GAAG;GAAe,GAAG;EAAgB;WACxD,aAAa,YACtB,eAAc,YAAY,CAAC,eAAe,cAAe,EAAC,OAAO,QAAQ,CAAC,KAAK,IAAI;CAEtF;AACD,QAAO;EAAE,GAAG;EAAW,GAAG;CAAe;AAC1C;AACD,SAAS,cAAc,SAAS;CAC9B,IAAI,SAAS,OAAO,yBAAyB,QAAQ,OAAO,MAAM,EAAE;CACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,KAAI,QACF,QAAO,QAAQ;AAEjB,UAAS,OAAO,yBAAyB,SAAS,MAAM,EAAE;AAC1D,WAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,KAAI,QACF,QAAO,QAAQ,MAAM;AAEvB,QAAO,QAAQ,MAAM,OAAO,QAAQ;AACrC"}