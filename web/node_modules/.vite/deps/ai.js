import { AISDKError, APICallError, EmptyResponseBodyError, InvalidArgumentError, InvalidPromptError, InvalidResponseDataError, JSONParseError, LoadAPIKeyError, NoContentGeneratedError, NoSuchModelError, TypeValidationError, UnsupportedFunctionalityError, ZodFirstPartyTypeKind, arrayType, booleanType, convertAsyncIteratorToReadableStream, convertBase64ToUint8Array, convertUint8ArrayToBase64, createEventSourceParserStream, createIdGenerator, custom, delay, generateId, getErrorMessage, getErrorMessage$1, instanceOfType, isAbortError, isJSONArray, isJSONObject, lazyType, literalType, nullType, numberType, objectType, optionalType, recordType, safeParseJSON, safeValidateTypes, stringType, unionType, unknownType, validatorSymbol } from "./types-Bq5IzgDU.js";

//#region node_modules/zod-to-json-schema/dist/esm/Options.js
const ignoreOverride = Symbol("Let zodToJsonSchema decide on which parser to use");
const defaultOptions = {
	name: void 0,
	$refStrategy: "root",
	basePath: ["#"],
	effectStrategy: "input",
	pipeStrategy: "all",
	dateStrategy: "format:date-time",
	mapStrategy: "entries",
	removeAdditionalStrategy: "passthrough",
	allowedAdditionalProperties: true,
	rejectedAdditionalProperties: false,
	definitionPath: "definitions",
	target: "jsonSchema7",
	strictUnions: false,
	definitions: {},
	errorMessages: false,
	markdownDescription: false,
	patternStrategy: "escape",
	applyRegexFlags: false,
	emailStrategy: "format:email",
	base64Strategy: "contentEncoding:base64",
	nameStrategy: "ref",
	openAiAnyTypeName: "OpenAiAnyType"
};
const getDefaultOptions = (options) => typeof options === "string" ? {
	...defaultOptions,
	name: options
} : {
	...defaultOptions,
	...options
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/Refs.js
const getRefs = (options) => {
	const _options = getDefaultOptions(options);
	const currentPath = _options.name !== void 0 ? [
		..._options.basePath,
		_options.definitionPath,
		_options.name
	] : _options.basePath;
	return {
		..._options,
		flags: { hasReferencedOpenAiAnyType: false },
		currentPath,
		propertyPath: void 0,
		seen: new Map(Object.entries(_options.definitions).map(([name$1, def]) => [def._def, {
			def: def._def,
			path: [
				..._options.basePath,
				_options.definitionPath,
				name$1
			],
			jsonSchema: void 0
		}]))
	};
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/errorMessages.js
function addErrorMessage(res, key, errorMessage, refs) {
	if (!refs?.errorMessages) return;
	if (errorMessage) res.errorMessage = {
		...res.errorMessage,
		[key]: errorMessage
	};
}
function setResponseValueAndErrors(res, key, value, errorMessage, refs) {
	res[key] = value;
	addErrorMessage(res, key, errorMessage, refs);
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/getRelativePath.js
const getRelativePath = (pathA, pathB) => {
	let i = 0;
	for (; i < pathA.length && i < pathB.length; i++) if (pathA[i] !== pathB[i]) break;
	return [(pathA.length - i).toString(), ...pathB.slice(i)].join("/");
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/any.js
function parseAnyDef(refs) {
	if (refs.target !== "openAi") return {};
	const anyDefinitionPath = [
		...refs.basePath,
		refs.definitionPath,
		refs.openAiAnyTypeName
	];
	refs.flags.hasReferencedOpenAiAnyType = true;
	return { $ref: refs.$refStrategy === "relative" ? getRelativePath(anyDefinitionPath, refs.currentPath) : anyDefinitionPath.join("/") };
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/array.js
function parseArrayDef(def, refs) {
	const res = { type: "array" };
	if (def.type?._def && def.type?._def?.typeName !== ZodFirstPartyTypeKind.ZodAny) res.items = parseDef(def.type._def, {
		...refs,
		currentPath: [...refs.currentPath, "items"]
	});
	if (def.minLength) setResponseValueAndErrors(res, "minItems", def.minLength.value, def.minLength.message, refs);
	if (def.maxLength) setResponseValueAndErrors(res, "maxItems", def.maxLength.value, def.maxLength.message, refs);
	if (def.exactLength) {
		setResponseValueAndErrors(res, "minItems", def.exactLength.value, def.exactLength.message, refs);
		setResponseValueAndErrors(res, "maxItems", def.exactLength.value, def.exactLength.message, refs);
	}
	return res;
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/bigint.js
function parseBigintDef(def, refs) {
	const res = {
		type: "integer",
		format: "int64"
	};
	if (!def.checks) return res;
	for (const check of def.checks) switch (check.kind) {
		case "min":
			if (refs.target === "jsonSchema7") if (check.inclusive) setResponseValueAndErrors(res, "minimum", check.value, check.message, refs);
			else setResponseValueAndErrors(res, "exclusiveMinimum", check.value, check.message, refs);
			else {
				if (!check.inclusive) res.exclusiveMinimum = true;
				setResponseValueAndErrors(res, "minimum", check.value, check.message, refs);
			}
			break;
		case "max":
			if (refs.target === "jsonSchema7") if (check.inclusive) setResponseValueAndErrors(res, "maximum", check.value, check.message, refs);
			else setResponseValueAndErrors(res, "exclusiveMaximum", check.value, check.message, refs);
			else {
				if (!check.inclusive) res.exclusiveMaximum = true;
				setResponseValueAndErrors(res, "maximum", check.value, check.message, refs);
			}
			break;
		case "multipleOf":
			setResponseValueAndErrors(res, "multipleOf", check.value, check.message, refs);
			break;
	}
	return res;
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/boolean.js
function parseBooleanDef() {
	return { type: "boolean" };
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/branded.js
function parseBrandedDef(_def, refs) {
	return parseDef(_def.type._def, refs);
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/catch.js
const parseCatchDef = (def, refs) => {
	return parseDef(def.innerType._def, refs);
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/date.js
function parseDateDef(def, refs, overrideDateStrategy) {
	const strategy = overrideDateStrategy ?? refs.dateStrategy;
	if (Array.isArray(strategy)) return { anyOf: strategy.map((item, i) => parseDateDef(def, refs, item)) };
	switch (strategy) {
		case "string":
		case "format:date-time": return {
			type: "string",
			format: "date-time"
		};
		case "format:date": return {
			type: "string",
			format: "date"
		};
		case "integer": return integerDateParser(def, refs);
	}
}
const integerDateParser = (def, refs) => {
	const res = {
		type: "integer",
		format: "unix-time"
	};
	if (refs.target === "openApi3") return res;
	for (const check of def.checks) switch (check.kind) {
		case "min":
			setResponseValueAndErrors(res, "minimum", check.value, check.message, refs);
			break;
		case "max":
			setResponseValueAndErrors(res, "maximum", check.value, check.message, refs);
			break;
	}
	return res;
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/default.js
function parseDefaultDef(_def, refs) {
	return {
		...parseDef(_def.innerType._def, refs),
		default: _def.defaultValue()
	};
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/effects.js
function parseEffectsDef(_def, refs) {
	return refs.effectStrategy === "input" ? parseDef(_def.schema._def, refs) : parseAnyDef(refs);
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/enum.js
function parseEnumDef(def) {
	return {
		type: "string",
		enum: Array.from(def.values)
	};
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/intersection.js
const isJsonSchema7AllOfType = (type) => {
	if ("type" in type && type.type === "string") return false;
	return "allOf" in type;
};
function parseIntersectionDef(def, refs) {
	const allOf = [parseDef(def.left._def, {
		...refs,
		currentPath: [
			...refs.currentPath,
			"allOf",
			"0"
		]
	}), parseDef(def.right._def, {
		...refs,
		currentPath: [
			...refs.currentPath,
			"allOf",
			"1"
		]
	})].filter((x) => !!x);
	let unevaluatedProperties = refs.target === "jsonSchema2019-09" ? { unevaluatedProperties: false } : void 0;
	const mergedAllOf = [];
	allOf.forEach((schema) => {
		if (isJsonSchema7AllOfType(schema)) {
			mergedAllOf.push(...schema.allOf);
			if (schema.unevaluatedProperties === void 0) unevaluatedProperties = void 0;
		} else {
			let nestedSchema = schema;
			if ("additionalProperties" in schema && schema.additionalProperties === false) {
				const { additionalProperties,...rest } = schema;
				nestedSchema = rest;
			} else unevaluatedProperties = void 0;
			mergedAllOf.push(nestedSchema);
		}
	});
	return mergedAllOf.length ? {
		allOf: mergedAllOf,
		...unevaluatedProperties
	} : void 0;
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/literal.js
function parseLiteralDef(def, refs) {
	const parsedType = typeof def.value;
	if (parsedType !== "bigint" && parsedType !== "number" && parsedType !== "boolean" && parsedType !== "string") return { type: Array.isArray(def.value) ? "array" : "object" };
	if (refs.target === "openApi3") return {
		type: parsedType === "bigint" ? "integer" : parsedType,
		enum: [def.value]
	};
	return {
		type: parsedType === "bigint" ? "integer" : parsedType,
		const: def.value
	};
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/string.js
let emojiRegex = void 0;
/**
* Generated from the regular expressions found here as of 2024-05-22:
* https://github.com/colinhacks/zod/blob/master/src/types.ts.
*
* Expressions with /i flag have been changed accordingly.
*/
const zodPatterns = {
	cuid: /^[cC][^\s-]{8,}$/,
	cuid2: /^[0-9a-z]+$/,
	ulid: /^[0-9A-HJKMNP-TV-Z]{26}$/,
	email: /^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,
	emoji: () => {
		if (emojiRegex === void 0) emojiRegex = RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$", "u");
		return emojiRegex;
	},
	uuid: /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/,
	ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,
	ipv4Cidr: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,
	ipv6: /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,
	ipv6Cidr: /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,
	base64: /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,
	base64url: /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,
	nanoid: /^[a-zA-Z0-9_-]{21}$/,
	jwt: /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/
};
function parseStringDef(def, refs) {
	const res = { type: "string" };
	if (def.checks) for (const check of def.checks) switch (check.kind) {
		case "min":
			setResponseValueAndErrors(res, "minLength", typeof res.minLength === "number" ? Math.max(res.minLength, check.value) : check.value, check.message, refs);
			break;
		case "max":
			setResponseValueAndErrors(res, "maxLength", typeof res.maxLength === "number" ? Math.min(res.maxLength, check.value) : check.value, check.message, refs);
			break;
		case "email":
			switch (refs.emailStrategy) {
				case "format:email":
					addFormat(res, "email", check.message, refs);
					break;
				case "format:idn-email":
					addFormat(res, "idn-email", check.message, refs);
					break;
				case "pattern:zod":
					addPattern(res, zodPatterns.email, check.message, refs);
					break;
			}
			break;
		case "url":
			addFormat(res, "uri", check.message, refs);
			break;
		case "uuid":
			addFormat(res, "uuid", check.message, refs);
			break;
		case "regex":
			addPattern(res, check.regex, check.message, refs);
			break;
		case "cuid":
			addPattern(res, zodPatterns.cuid, check.message, refs);
			break;
		case "cuid2":
			addPattern(res, zodPatterns.cuid2, check.message, refs);
			break;
		case "startsWith":
			addPattern(res, RegExp(`^${escapeLiteralCheckValue(check.value, refs)}`), check.message, refs);
			break;
		case "endsWith":
			addPattern(res, RegExp(`${escapeLiteralCheckValue(check.value, refs)}$`), check.message, refs);
			break;
		case "datetime":
			addFormat(res, "date-time", check.message, refs);
			break;
		case "date":
			addFormat(res, "date", check.message, refs);
			break;
		case "time":
			addFormat(res, "time", check.message, refs);
			break;
		case "duration":
			addFormat(res, "duration", check.message, refs);
			break;
		case "length":
			setResponseValueAndErrors(res, "minLength", typeof res.minLength === "number" ? Math.max(res.minLength, check.value) : check.value, check.message, refs);
			setResponseValueAndErrors(res, "maxLength", typeof res.maxLength === "number" ? Math.min(res.maxLength, check.value) : check.value, check.message, refs);
			break;
		case "includes": {
			addPattern(res, RegExp(escapeLiteralCheckValue(check.value, refs)), check.message, refs);
			break;
		}
		case "ip": {
			if (check.version !== "v6") addFormat(res, "ipv4", check.message, refs);
			if (check.version !== "v4") addFormat(res, "ipv6", check.message, refs);
			break;
		}
		case "base64url":
			addPattern(res, zodPatterns.base64url, check.message, refs);
			break;
		case "jwt":
			addPattern(res, zodPatterns.jwt, check.message, refs);
			break;
		case "cidr": {
			if (check.version !== "v6") addPattern(res, zodPatterns.ipv4Cidr, check.message, refs);
			if (check.version !== "v4") addPattern(res, zodPatterns.ipv6Cidr, check.message, refs);
			break;
		}
		case "emoji":
			addPattern(res, zodPatterns.emoji(), check.message, refs);
			break;
		case "ulid": {
			addPattern(res, zodPatterns.ulid, check.message, refs);
			break;
		}
		case "base64": {
			switch (refs.base64Strategy) {
				case "format:binary": {
					addFormat(res, "binary", check.message, refs);
					break;
				}
				case "contentEncoding:base64": {
					setResponseValueAndErrors(res, "contentEncoding", "base64", check.message, refs);
					break;
				}
				case "pattern:zod": {
					addPattern(res, zodPatterns.base64, check.message, refs);
					break;
				}
			}
			break;
		}
		case "nanoid": addPattern(res, zodPatterns.nanoid, check.message, refs);
		case "toLowerCase":
		case "toUpperCase":
		case "trim": break;
		default:
 /* c8 ignore next */
		((_) => {})(check);
	}
	return res;
}
function escapeLiteralCheckValue(literal, refs) {
	return refs.patternStrategy === "escape" ? escapeNonAlphaNumeric(literal) : literal;
}
const ALPHA_NUMERIC = /* @__PURE__ */ new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");
function escapeNonAlphaNumeric(source) {
	let result = "";
	for (let i = 0; i < source.length; i++) {
		if (!ALPHA_NUMERIC.has(source[i])) result += "\\";
		result += source[i];
	}
	return result;
}
function addFormat(schema, value, message, refs) {
	if (schema.format || schema.anyOf?.some((x) => x.format)) {
		if (!schema.anyOf) schema.anyOf = [];
		if (schema.format) {
			schema.anyOf.push({
				format: schema.format,
				...schema.errorMessage && refs.errorMessages && { errorMessage: { format: schema.errorMessage.format } }
			});
			delete schema.format;
			if (schema.errorMessage) {
				delete schema.errorMessage.format;
				if (Object.keys(schema.errorMessage).length === 0) delete schema.errorMessage;
			}
		}
		schema.anyOf.push({
			format: value,
			...message && refs.errorMessages && { errorMessage: { format: message } }
		});
	} else setResponseValueAndErrors(schema, "format", value, message, refs);
}
function addPattern(schema, regex, message, refs) {
	if (schema.pattern || schema.allOf?.some((x) => x.pattern)) {
		if (!schema.allOf) schema.allOf = [];
		if (schema.pattern) {
			schema.allOf.push({
				pattern: schema.pattern,
				...schema.errorMessage && refs.errorMessages && { errorMessage: { pattern: schema.errorMessage.pattern } }
			});
			delete schema.pattern;
			if (schema.errorMessage) {
				delete schema.errorMessage.pattern;
				if (Object.keys(schema.errorMessage).length === 0) delete schema.errorMessage;
			}
		}
		schema.allOf.push({
			pattern: stringifyRegExpWithFlags(regex, refs),
			...message && refs.errorMessages && { errorMessage: { pattern: message } }
		});
	} else setResponseValueAndErrors(schema, "pattern", stringifyRegExpWithFlags(regex, refs), message, refs);
}
function stringifyRegExpWithFlags(regex, refs) {
	if (!refs.applyRegexFlags || !regex.flags) return regex.source;
	const flags = {
		i: regex.flags.includes("i"),
		m: regex.flags.includes("m"),
		s: regex.flags.includes("s")
	};
	const source = flags.i ? regex.source.toLowerCase() : regex.source;
	let pattern = "";
	let isEscaped = false;
	let inCharGroup = false;
	let inCharRange = false;
	for (let i = 0; i < source.length; i++) {
		if (isEscaped) {
			pattern += source[i];
			isEscaped = false;
			continue;
		}
		if (flags.i) {
			if (inCharGroup) {
				if (source[i].match(/[a-z]/)) {
					if (inCharRange) {
						pattern += source[i];
						pattern += `${source[i - 2]}-${source[i]}`.toUpperCase();
						inCharRange = false;
					} else if (source[i + 1] === "-" && source[i + 2]?.match(/[a-z]/)) {
						pattern += source[i];
						inCharRange = true;
					} else pattern += `${source[i]}${source[i].toUpperCase()}`;
					continue;
				}
			} else if (source[i].match(/[a-z]/)) {
				pattern += `[${source[i]}${source[i].toUpperCase()}]`;
				continue;
			}
		}
		if (flags.m) {
			if (source[i] === "^") {
				pattern += `(^|(?<=[\r\n]))`;
				continue;
			} else if (source[i] === "$") {
				pattern += `($|(?=[\r\n]))`;
				continue;
			}
		}
		if (flags.s && source[i] === ".") {
			pattern += inCharGroup ? `${source[i]}\r\n` : `[${source[i]}\r\n]`;
			continue;
		}
		pattern += source[i];
		if (source[i] === "\\") isEscaped = true;
		else if (inCharGroup && source[i] === "]") inCharGroup = false;
		else if (!inCharGroup && source[i] === "[") inCharGroup = true;
	}
	try {
		new RegExp(pattern);
	} catch {
		console.warn(`Could not convert regex pattern at ${refs.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`);
		return regex.source;
	}
	return pattern;
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/record.js
function parseRecordDef(def, refs) {
	if (refs.target === "openAi") console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead.");
	if (refs.target === "openApi3" && def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodEnum) return {
		type: "object",
		required: def.keyType._def.values,
		properties: def.keyType._def.values.reduce((acc, key) => ({
			...acc,
			[key]: parseDef(def.valueType._def, {
				...refs,
				currentPath: [
					...refs.currentPath,
					"properties",
					key
				]
			}) ?? parseAnyDef(refs)
		}), {}),
		additionalProperties: refs.rejectedAdditionalProperties
	};
	const schema = {
		type: "object",
		additionalProperties: parseDef(def.valueType._def, {
			...refs,
			currentPath: [...refs.currentPath, "additionalProperties"]
		}) ?? refs.allowedAdditionalProperties
	};
	if (refs.target === "openApi3") return schema;
	if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodString && def.keyType._def.checks?.length) {
		const { type,...keyType } = parseStringDef(def.keyType._def, refs);
		return {
			...schema,
			propertyNames: keyType
		};
	} else if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodEnum) return {
		...schema,
		propertyNames: { enum: def.keyType._def.values }
	};
	else if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodBranded && def.keyType._def.type._def.typeName === ZodFirstPartyTypeKind.ZodString && def.keyType._def.type._def.checks?.length) {
		const { type,...keyType } = parseBrandedDef(def.keyType._def, refs);
		return {
			...schema,
			propertyNames: keyType
		};
	}
	return schema;
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/map.js
function parseMapDef(def, refs) {
	if (refs.mapStrategy === "record") return parseRecordDef(def, refs);
	const keys = parseDef(def.keyType._def, {
		...refs,
		currentPath: [
			...refs.currentPath,
			"items",
			"items",
			"0"
		]
	}) || parseAnyDef(refs);
	const values = parseDef(def.valueType._def, {
		...refs,
		currentPath: [
			...refs.currentPath,
			"items",
			"items",
			"1"
		]
	}) || parseAnyDef(refs);
	return {
		type: "array",
		maxItems: 125,
		items: {
			type: "array",
			items: [keys, values],
			minItems: 2,
			maxItems: 2
		}
	};
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/nativeEnum.js
function parseNativeEnumDef(def) {
	const object$1 = def.values;
	const actualKeys = Object.keys(def.values).filter((key) => {
		return typeof object$1[object$1[key]] !== "number";
	});
	const actualValues = actualKeys.map((key) => object$1[key]);
	const parsedTypes = Array.from(new Set(actualValues.map((values) => typeof values)));
	return {
		type: parsedTypes.length === 1 ? parsedTypes[0] === "string" ? "string" : "number" : ["string", "number"],
		enum: actualValues
	};
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/never.js
function parseNeverDef(refs) {
	return refs.target === "openAi" ? void 0 : { not: parseAnyDef({
		...refs,
		currentPath: [...refs.currentPath, "not"]
	}) };
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/null.js
function parseNullDef(refs) {
	return refs.target === "openApi3" ? {
		enum: ["null"],
		nullable: true
	} : { type: "null" };
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/union.js
const primitiveMappings = {
	ZodString: "string",
	ZodNumber: "number",
	ZodBigInt: "integer",
	ZodBoolean: "boolean",
	ZodNull: "null"
};
function parseUnionDef(def, refs) {
	if (refs.target === "openApi3") return asAnyOf(def, refs);
	const options = def.options instanceof Map ? Array.from(def.options.values()) : def.options;
	if (options.every((x) => x._def.typeName in primitiveMappings && (!x._def.checks || !x._def.checks.length))) {
		const types = options.reduce((types$1, x) => {
			const type = primitiveMappings[x._def.typeName];
			return type && !types$1.includes(type) ? [...types$1, type] : types$1;
		}, []);
		return { type: types.length > 1 ? types : types[0] };
	} else if (options.every((x) => x._def.typeName === "ZodLiteral" && !x.description)) {
		const types = options.reduce((acc, x) => {
			const type = typeof x._def.value;
			switch (type) {
				case "string":
				case "number":
				case "boolean": return [...acc, type];
				case "bigint": return [...acc, "integer"];
				case "object": if (x._def.value === null) return [...acc, "null"];
				case "symbol":
				case "undefined":
				case "function":
				default: return acc;
			}
		}, []);
		if (types.length === options.length) {
			const uniqueTypes = types.filter((x, i, a) => a.indexOf(x) === i);
			return {
				type: uniqueTypes.length > 1 ? uniqueTypes : uniqueTypes[0],
				enum: options.reduce((acc, x) => {
					return acc.includes(x._def.value) ? acc : [...acc, x._def.value];
				}, [])
			};
		}
	} else if (options.every((x) => x._def.typeName === "ZodEnum")) return {
		type: "string",
		enum: options.reduce((acc, x) => [...acc, ...x._def.values.filter((x$1) => !acc.includes(x$1))], [])
	};
	return asAnyOf(def, refs);
}
const asAnyOf = (def, refs) => {
	const anyOf = (def.options instanceof Map ? Array.from(def.options.values()) : def.options).map((x, i) => parseDef(x._def, {
		...refs,
		currentPath: [
			...refs.currentPath,
			"anyOf",
			`${i}`
		]
	})).filter((x) => !!x && (!refs.strictUnions || typeof x === "object" && Object.keys(x).length > 0));
	return anyOf.length ? { anyOf } : void 0;
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/nullable.js
function parseNullableDef(def, refs) {
	if ([
		"ZodString",
		"ZodNumber",
		"ZodBigInt",
		"ZodBoolean",
		"ZodNull"
	].includes(def.innerType._def.typeName) && (!def.innerType._def.checks || !def.innerType._def.checks.length)) {
		if (refs.target === "openApi3") return {
			type: primitiveMappings[def.innerType._def.typeName],
			nullable: true
		};
		return { type: [primitiveMappings[def.innerType._def.typeName], "null"] };
	}
	if (refs.target === "openApi3") {
		const base$1 = parseDef(def.innerType._def, {
			...refs,
			currentPath: [...refs.currentPath]
		});
		if (base$1 && "$ref" in base$1) return {
			allOf: [base$1],
			nullable: true
		};
		return base$1 && {
			...base$1,
			nullable: true
		};
	}
	const base = parseDef(def.innerType._def, {
		...refs,
		currentPath: [
			...refs.currentPath,
			"anyOf",
			"0"
		]
	});
	return base && { anyOf: [base, { type: "null" }] };
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/number.js
function parseNumberDef(def, refs) {
	const res = { type: "number" };
	if (!def.checks) return res;
	for (const check of def.checks) switch (check.kind) {
		case "int":
			res.type = "integer";
			addErrorMessage(res, "type", check.message, refs);
			break;
		case "min":
			if (refs.target === "jsonSchema7") if (check.inclusive) setResponseValueAndErrors(res, "minimum", check.value, check.message, refs);
			else setResponseValueAndErrors(res, "exclusiveMinimum", check.value, check.message, refs);
			else {
				if (!check.inclusive) res.exclusiveMinimum = true;
				setResponseValueAndErrors(res, "minimum", check.value, check.message, refs);
			}
			break;
		case "max":
			if (refs.target === "jsonSchema7") if (check.inclusive) setResponseValueAndErrors(res, "maximum", check.value, check.message, refs);
			else setResponseValueAndErrors(res, "exclusiveMaximum", check.value, check.message, refs);
			else {
				if (!check.inclusive) res.exclusiveMaximum = true;
				setResponseValueAndErrors(res, "maximum", check.value, check.message, refs);
			}
			break;
		case "multipleOf":
			setResponseValueAndErrors(res, "multipleOf", check.value, check.message, refs);
			break;
	}
	return res;
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/object.js
function parseObjectDef(def, refs) {
	const forceOptionalIntoNullable = refs.target === "openAi";
	const result = {
		type: "object",
		properties: {}
	};
	const required = [];
	const shape = def.shape();
	for (const propName in shape) {
		let propDef = shape[propName];
		if (propDef === void 0 || propDef._def === void 0) continue;
		let propOptional = safeIsOptional(propDef);
		if (propOptional && forceOptionalIntoNullable) {
			if (propDef._def.typeName === "ZodOptional") propDef = propDef._def.innerType;
			if (!propDef.isNullable()) propDef = propDef.nullable();
			propOptional = false;
		}
		const parsedDef = parseDef(propDef._def, {
			...refs,
			currentPath: [
				...refs.currentPath,
				"properties",
				propName
			],
			propertyPath: [
				...refs.currentPath,
				"properties",
				propName
			]
		});
		if (parsedDef === void 0) continue;
		result.properties[propName] = parsedDef;
		if (!propOptional) required.push(propName);
	}
	if (required.length) result.required = required;
	const additionalProperties = decideAdditionalProperties(def, refs);
	if (additionalProperties !== void 0) result.additionalProperties = additionalProperties;
	return result;
}
function decideAdditionalProperties(def, refs) {
	if (def.catchall._def.typeName !== "ZodNever") return parseDef(def.catchall._def, {
		...refs,
		currentPath: [...refs.currentPath, "additionalProperties"]
	});
	switch (def.unknownKeys) {
		case "passthrough": return refs.allowedAdditionalProperties;
		case "strict": return refs.rejectedAdditionalProperties;
		case "strip": return refs.removeAdditionalStrategy === "strict" ? refs.allowedAdditionalProperties : refs.rejectedAdditionalProperties;
	}
}
function safeIsOptional(schema) {
	try {
		return schema.isOptional();
	} catch {
		return true;
	}
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/optional.js
const parseOptionalDef = (def, refs) => {
	if (refs.currentPath.toString() === refs.propertyPath?.toString()) return parseDef(def.innerType._def, refs);
	const innerSchema = parseDef(def.innerType._def, {
		...refs,
		currentPath: [
			...refs.currentPath,
			"anyOf",
			"1"
		]
	});
	return innerSchema ? { anyOf: [{ not: parseAnyDef(refs) }, innerSchema] } : parseAnyDef(refs);
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/pipeline.js
const parsePipelineDef = (def, refs) => {
	if (refs.pipeStrategy === "input") return parseDef(def.in._def, refs);
	else if (refs.pipeStrategy === "output") return parseDef(def.out._def, refs);
	const a = parseDef(def.in._def, {
		...refs,
		currentPath: [
			...refs.currentPath,
			"allOf",
			"0"
		]
	});
	const b = parseDef(def.out._def, {
		...refs,
		currentPath: [
			...refs.currentPath,
			"allOf",
			a ? "1" : "0"
		]
	});
	return { allOf: [a, b].filter((x) => x !== void 0) };
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/promise.js
function parsePromiseDef(def, refs) {
	return parseDef(def.type._def, refs);
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/set.js
function parseSetDef(def, refs) {
	const items = parseDef(def.valueType._def, {
		...refs,
		currentPath: [...refs.currentPath, "items"]
	});
	const schema = {
		type: "array",
		uniqueItems: true,
		items
	};
	if (def.minSize) setResponseValueAndErrors(schema, "minItems", def.minSize.value, def.minSize.message, refs);
	if (def.maxSize) setResponseValueAndErrors(schema, "maxItems", def.maxSize.value, def.maxSize.message, refs);
	return schema;
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/tuple.js
function parseTupleDef(def, refs) {
	if (def.rest) return {
		type: "array",
		minItems: def.items.length,
		items: def.items.map((x, i) => parseDef(x._def, {
			...refs,
			currentPath: [
				...refs.currentPath,
				"items",
				`${i}`
			]
		})).reduce((acc, x) => x === void 0 ? acc : [...acc, x], []),
		additionalItems: parseDef(def.rest._def, {
			...refs,
			currentPath: [...refs.currentPath, "additionalItems"]
		})
	};
	else return {
		type: "array",
		minItems: def.items.length,
		maxItems: def.items.length,
		items: def.items.map((x, i) => parseDef(x._def, {
			...refs,
			currentPath: [
				...refs.currentPath,
				"items",
				`${i}`
			]
		})).reduce((acc, x) => x === void 0 ? acc : [...acc, x], [])
	};
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/undefined.js
function parseUndefinedDef(refs) {
	return { not: parseAnyDef(refs) };
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/unknown.js
function parseUnknownDef(refs) {
	return parseAnyDef(refs);
}

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parsers/readonly.js
const parseReadonlyDef = (def, refs) => {
	return parseDef(def.innerType._def, refs);
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/selectParser.js
const selectParser = (def, typeName, refs) => {
	switch (typeName) {
		case ZodFirstPartyTypeKind.ZodString: return parseStringDef(def, refs);
		case ZodFirstPartyTypeKind.ZodNumber: return parseNumberDef(def, refs);
		case ZodFirstPartyTypeKind.ZodObject: return parseObjectDef(def, refs);
		case ZodFirstPartyTypeKind.ZodBigInt: return parseBigintDef(def, refs);
		case ZodFirstPartyTypeKind.ZodBoolean: return parseBooleanDef();
		case ZodFirstPartyTypeKind.ZodDate: return parseDateDef(def, refs);
		case ZodFirstPartyTypeKind.ZodUndefined: return parseUndefinedDef(refs);
		case ZodFirstPartyTypeKind.ZodNull: return parseNullDef(refs);
		case ZodFirstPartyTypeKind.ZodArray: return parseArrayDef(def, refs);
		case ZodFirstPartyTypeKind.ZodUnion:
		case ZodFirstPartyTypeKind.ZodDiscriminatedUnion: return parseUnionDef(def, refs);
		case ZodFirstPartyTypeKind.ZodIntersection: return parseIntersectionDef(def, refs);
		case ZodFirstPartyTypeKind.ZodTuple: return parseTupleDef(def, refs);
		case ZodFirstPartyTypeKind.ZodRecord: return parseRecordDef(def, refs);
		case ZodFirstPartyTypeKind.ZodLiteral: return parseLiteralDef(def, refs);
		case ZodFirstPartyTypeKind.ZodEnum: return parseEnumDef(def);
		case ZodFirstPartyTypeKind.ZodNativeEnum: return parseNativeEnumDef(def);
		case ZodFirstPartyTypeKind.ZodNullable: return parseNullableDef(def, refs);
		case ZodFirstPartyTypeKind.ZodOptional: return parseOptionalDef(def, refs);
		case ZodFirstPartyTypeKind.ZodMap: return parseMapDef(def, refs);
		case ZodFirstPartyTypeKind.ZodSet: return parseSetDef(def, refs);
		case ZodFirstPartyTypeKind.ZodLazy: return () => def.getter()._def;
		case ZodFirstPartyTypeKind.ZodPromise: return parsePromiseDef(def, refs);
		case ZodFirstPartyTypeKind.ZodNaN:
		case ZodFirstPartyTypeKind.ZodNever: return parseNeverDef(refs);
		case ZodFirstPartyTypeKind.ZodEffects: return parseEffectsDef(def, refs);
		case ZodFirstPartyTypeKind.ZodAny: return parseAnyDef(refs);
		case ZodFirstPartyTypeKind.ZodUnknown: return parseUnknownDef(refs);
		case ZodFirstPartyTypeKind.ZodDefault: return parseDefaultDef(def, refs);
		case ZodFirstPartyTypeKind.ZodBranded: return parseBrandedDef(def, refs);
		case ZodFirstPartyTypeKind.ZodReadonly: return parseReadonlyDef(def, refs);
		case ZodFirstPartyTypeKind.ZodCatch: return parseCatchDef(def, refs);
		case ZodFirstPartyTypeKind.ZodPipeline: return parsePipelineDef(def, refs);
		case ZodFirstPartyTypeKind.ZodFunction:
		case ZodFirstPartyTypeKind.ZodVoid:
		case ZodFirstPartyTypeKind.ZodSymbol: return void 0;
		default:
 /* c8 ignore next */
		return ((_) => void 0)(typeName);
	}
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/parseDef.js
function parseDef(def, refs, forceResolution = false) {
	const seenItem = refs.seen.get(def);
	if (refs.override) {
		const overrideResult = refs.override?.(def, refs, seenItem, forceResolution);
		if (overrideResult !== ignoreOverride) return overrideResult;
	}
	if (seenItem && !forceResolution) {
		const seenSchema = get$ref(seenItem, refs);
		if (seenSchema !== void 0) return seenSchema;
	}
	const newItem = {
		def,
		path: refs.currentPath,
		jsonSchema: void 0
	};
	refs.seen.set(def, newItem);
	const jsonSchemaOrGetter = selectParser(def, def.typeName, refs);
	const jsonSchema$1 = typeof jsonSchemaOrGetter === "function" ? parseDef(jsonSchemaOrGetter(), refs) : jsonSchemaOrGetter;
	if (jsonSchema$1) addMeta(def, refs, jsonSchema$1);
	if (refs.postProcess) {
		const postProcessResult = refs.postProcess(jsonSchema$1, def, refs);
		newItem.jsonSchema = jsonSchema$1;
		return postProcessResult;
	}
	newItem.jsonSchema = jsonSchema$1;
	return jsonSchema$1;
}
const get$ref = (item, refs) => {
	switch (refs.$refStrategy) {
		case "root": return { $ref: item.path.join("/") };
		case "relative": return { $ref: getRelativePath(refs.currentPath, item.path) };
		case "none":
		case "seen": {
			if (item.path.length < refs.currentPath.length && item.path.every((value, index) => refs.currentPath[index] === value)) {
				console.warn(`Recursive reference detected at ${refs.currentPath.join("/")}! Defaulting to any`);
				return parseAnyDef(refs);
			}
			return refs.$refStrategy === "seen" ? parseAnyDef(refs) : void 0;
		}
	}
};
const addMeta = (def, refs, jsonSchema$1) => {
	if (def.description) {
		jsonSchema$1.description = def.description;
		if (refs.markdownDescription) jsonSchema$1.markdownDescription = def.description;
	}
	return jsonSchema$1;
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/zodToJsonSchema.js
const zodToJsonSchema = (schema, options) => {
	const refs = getRefs(options);
	let definitions = typeof options === "object" && options.definitions ? Object.entries(options.definitions).reduce((acc, [name$2, schema$1]) => ({
		...acc,
		[name$2]: parseDef(schema$1._def, {
			...refs,
			currentPath: [
				...refs.basePath,
				refs.definitionPath,
				name$2
			]
		}, true) ?? parseAnyDef(refs)
	}), {}) : void 0;
	const name$1 = typeof options === "string" ? options : options?.nameStrategy === "title" ? void 0 : options?.name;
	const main = parseDef(schema._def, name$1 === void 0 ? refs : {
		...refs,
		currentPath: [
			...refs.basePath,
			refs.definitionPath,
			name$1
		]
	}, false) ?? parseAnyDef(refs);
	const title = typeof options === "object" && options.name !== void 0 && options.nameStrategy === "title" ? options.name : void 0;
	if (title !== void 0) main.title = title;
	if (refs.flags.hasReferencedOpenAiAnyType) {
		if (!definitions) definitions = {};
		if (!definitions[refs.openAiAnyTypeName]) definitions[refs.openAiAnyTypeName] = {
			type: [
				"string",
				"number",
				"integer",
				"boolean",
				"array",
				"null"
			],
			items: { $ref: refs.$refStrategy === "relative" ? "1" : [
				...refs.basePath,
				refs.definitionPath,
				refs.openAiAnyTypeName
			].join("/") }
		};
	}
	const combined = name$1 === void 0 ? definitions ? {
		...main,
		[refs.definitionPath]: definitions
	} : main : {
		$ref: [
			...refs.$refStrategy === "relative" ? [] : refs.basePath,
			refs.definitionPath,
			name$1
		].join("/"),
		[refs.definitionPath]: {
			...definitions,
			[name$1]: main
		}
	};
	if (refs.target === "jsonSchema7") combined.$schema = "http://json-schema.org/draft-07/schema#";
	else if (refs.target === "jsonSchema2019-09" || refs.target === "openAi") combined.$schema = "https://json-schema.org/draft/2019-09/schema#";
	if (refs.target === "openAi" && ("anyOf" in combined || "oneOf" in combined || "allOf" in combined || "type" in combined && Array.isArray(combined.type))) console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property.");
	return combined;
};

//#endregion
//#region node_modules/zod-to-json-schema/dist/esm/index.js
var esm_default = zodToJsonSchema;

//#endregion
//#region node_modules/@ai-sdk/ui-utils/dist/index.mjs
var textStreamPart = {
	code: "0",
	name: "text",
	parse: (value) => {
		if (typeof value !== "string") throw new Error("\"text\" parts expect a string value.");
		return {
			type: "text",
			value
		};
	}
};
var errorStreamPart = {
	code: "3",
	name: "error",
	parse: (value) => {
		if (typeof value !== "string") throw new Error("\"error\" parts expect a string value.");
		return {
			type: "error",
			value
		};
	}
};
var assistantMessageStreamPart = {
	code: "4",
	name: "assistant_message",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("id" in value) || !("role" in value) || !("content" in value) || typeof value.id !== "string" || typeof value.role !== "string" || value.role !== "assistant" || !Array.isArray(value.content) || !value.content.every((item) => item != null && typeof item === "object" && "type" in item && item.type === "text" && "text" in item && item.text != null && typeof item.text === "object" && "value" in item.text && typeof item.text.value === "string")) throw new Error("\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.");
		return {
			type: "assistant_message",
			value
		};
	}
};
var assistantControlDataStreamPart = {
	code: "5",
	name: "assistant_control_data",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("threadId" in value) || !("messageId" in value) || typeof value.threadId !== "string" || typeof value.messageId !== "string") throw new Error("\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.");
		return {
			type: "assistant_control_data",
			value: {
				threadId: value.threadId,
				messageId: value.messageId
			}
		};
	}
};
var dataMessageStreamPart = {
	code: "6",
	name: "data_message",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("role" in value) || !("data" in value) || typeof value.role !== "string" || value.role !== "data") throw new Error("\"data_message\" parts expect an object with a \"role\" and \"data\" property.");
		return {
			type: "data_message",
			value
		};
	}
};
var assistantStreamParts = [
	textStreamPart,
	errorStreamPart,
	assistantMessageStreamPart,
	assistantControlDataStreamPart,
	dataMessageStreamPart
];
var assistantStreamPartsByCode = {
	[textStreamPart.code]: textStreamPart,
	[errorStreamPart.code]: errorStreamPart,
	[assistantMessageStreamPart.code]: assistantMessageStreamPart,
	[assistantControlDataStreamPart.code]: assistantControlDataStreamPart,
	[dataMessageStreamPart.code]: dataMessageStreamPart
};
var StreamStringPrefixes = {
	[textStreamPart.name]: textStreamPart.code,
	[errorStreamPart.name]: errorStreamPart.code,
	[assistantMessageStreamPart.name]: assistantMessageStreamPart.code,
	[assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,
	[dataMessageStreamPart.name]: dataMessageStreamPart.code
};
var validCodes = assistantStreamParts.map((part) => part.code);
var parseAssistantStreamPart = (line) => {
	const firstSeparatorIndex = line.indexOf(":");
	if (firstSeparatorIndex === -1) throw new Error("Failed to parse stream string. No separator found.");
	const prefix = line.slice(0, firstSeparatorIndex);
	if (!validCodes.includes(prefix)) throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);
	const code = prefix;
	const textValue = line.slice(firstSeparatorIndex + 1);
	const jsonValue = JSON.parse(textValue);
	return assistantStreamPartsByCode[code].parse(jsonValue);
};
function formatAssistantStreamPart(type, value) {
	const streamPart = assistantStreamParts.find((part) => part.name === type);
	if (!streamPart) throw new Error(`Invalid stream part type: ${type}`);
	return `${streamPart.code}:${JSON.stringify(value)}
`;
}
function fixJson(input) {
	const stack = ["ROOT"];
	let lastValidIndex = -1;
	let literalStart = null;
	function processValueStart(char, i, swapState) {
		switch (char) {
			case "\"": {
				lastValidIndex = i;
				stack.pop();
				stack.push(swapState);
				stack.push("INSIDE_STRING");
				break;
			}
			case "f":
			case "t":
			case "n": {
				lastValidIndex = i;
				literalStart = i;
				stack.pop();
				stack.push(swapState);
				stack.push("INSIDE_LITERAL");
				break;
			}
			case "-": {
				stack.pop();
				stack.push(swapState);
				stack.push("INSIDE_NUMBER");
				break;
			}
			case "0":
			case "1":
			case "2":
			case "3":
			case "4":
			case "5":
			case "6":
			case "7":
			case "8":
			case "9": {
				lastValidIndex = i;
				stack.pop();
				stack.push(swapState);
				stack.push("INSIDE_NUMBER");
				break;
			}
			case "{": {
				lastValidIndex = i;
				stack.pop();
				stack.push(swapState);
				stack.push("INSIDE_OBJECT_START");
				break;
			}
			case "[": {
				lastValidIndex = i;
				stack.pop();
				stack.push(swapState);
				stack.push("INSIDE_ARRAY_START");
				break;
			}
		}
	}
	function processAfterObjectValue(char, i) {
		switch (char) {
			case ",": {
				stack.pop();
				stack.push("INSIDE_OBJECT_AFTER_COMMA");
				break;
			}
			case "}": {
				lastValidIndex = i;
				stack.pop();
				break;
			}
		}
	}
	function processAfterArrayValue(char, i) {
		switch (char) {
			case ",": {
				stack.pop();
				stack.push("INSIDE_ARRAY_AFTER_COMMA");
				break;
			}
			case "]": {
				lastValidIndex = i;
				stack.pop();
				break;
			}
		}
	}
	for (let i = 0; i < input.length; i++) {
		const char = input[i];
		const currentState = stack[stack.length - 1];
		switch (currentState) {
			case "ROOT":
				processValueStart(char, i, "FINISH");
				break;
			case "INSIDE_OBJECT_START": {
				switch (char) {
					case "\"": {
						stack.pop();
						stack.push("INSIDE_OBJECT_KEY");
						break;
					}
					case "}": {
						lastValidIndex = i;
						stack.pop();
						break;
					}
				}
				break;
			}
			case "INSIDE_OBJECT_AFTER_COMMA": {
				switch (char) {
					case "\"": {
						stack.pop();
						stack.push("INSIDE_OBJECT_KEY");
						break;
					}
				}
				break;
			}
			case "INSIDE_OBJECT_KEY": {
				switch (char) {
					case "\"": {
						stack.pop();
						stack.push("INSIDE_OBJECT_AFTER_KEY");
						break;
					}
				}
				break;
			}
			case "INSIDE_OBJECT_AFTER_KEY": {
				switch (char) {
					case ":": {
						stack.pop();
						stack.push("INSIDE_OBJECT_BEFORE_VALUE");
						break;
					}
				}
				break;
			}
			case "INSIDE_OBJECT_BEFORE_VALUE": {
				processValueStart(char, i, "INSIDE_OBJECT_AFTER_VALUE");
				break;
			}
			case "INSIDE_OBJECT_AFTER_VALUE": {
				processAfterObjectValue(char, i);
				break;
			}
			case "INSIDE_STRING": {
				switch (char) {
					case "\"": {
						stack.pop();
						lastValidIndex = i;
						break;
					}
					case "\\": {
						stack.push("INSIDE_STRING_ESCAPE");
						break;
					}
					default: lastValidIndex = i;
				}
				break;
			}
			case "INSIDE_ARRAY_START": {
				switch (char) {
					case "]": {
						lastValidIndex = i;
						stack.pop();
						break;
					}
					default: {
						lastValidIndex = i;
						processValueStart(char, i, "INSIDE_ARRAY_AFTER_VALUE");
						break;
					}
				}
				break;
			}
			case "INSIDE_ARRAY_AFTER_VALUE": {
				switch (char) {
					case ",": {
						stack.pop();
						stack.push("INSIDE_ARRAY_AFTER_COMMA");
						break;
					}
					case "]": {
						lastValidIndex = i;
						stack.pop();
						break;
					}
					default: {
						lastValidIndex = i;
						break;
					}
				}
				break;
			}
			case "INSIDE_ARRAY_AFTER_COMMA": {
				processValueStart(char, i, "INSIDE_ARRAY_AFTER_VALUE");
				break;
			}
			case "INSIDE_STRING_ESCAPE": {
				stack.pop();
				lastValidIndex = i;
				break;
			}
			case "INSIDE_NUMBER": {
				switch (char) {
					case "0":
					case "1":
					case "2":
					case "3":
					case "4":
					case "5":
					case "6":
					case "7":
					case "8":
					case "9": {
						lastValidIndex = i;
						break;
					}
					case "e":
					case "E":
					case "-":
					case ".": break;
					case ",": {
						stack.pop();
						if (stack[stack.length - 1] === "INSIDE_ARRAY_AFTER_VALUE") processAfterArrayValue(char, i);
						if (stack[stack.length - 1] === "INSIDE_OBJECT_AFTER_VALUE") processAfterObjectValue(char, i);
						break;
					}
					case "}": {
						stack.pop();
						if (stack[stack.length - 1] === "INSIDE_OBJECT_AFTER_VALUE") processAfterObjectValue(char, i);
						break;
					}
					case "]": {
						stack.pop();
						if (stack[stack.length - 1] === "INSIDE_ARRAY_AFTER_VALUE") processAfterArrayValue(char, i);
						break;
					}
					default: {
						stack.pop();
						break;
					}
				}
				break;
			}
			case "INSIDE_LITERAL": {
				const partialLiteral = input.substring(literalStart, i + 1);
				if (!"false".startsWith(partialLiteral) && !"true".startsWith(partialLiteral) && !"null".startsWith(partialLiteral)) {
					stack.pop();
					if (stack[stack.length - 1] === "INSIDE_OBJECT_AFTER_VALUE") processAfterObjectValue(char, i);
					else if (stack[stack.length - 1] === "INSIDE_ARRAY_AFTER_VALUE") processAfterArrayValue(char, i);
				} else lastValidIndex = i;
				break;
			}
		}
	}
	let result = input.slice(0, lastValidIndex + 1);
	for (let i = stack.length - 1; i >= 0; i--) {
		const state = stack[i];
		switch (state) {
			case "INSIDE_STRING": {
				result += "\"";
				break;
			}
			case "INSIDE_OBJECT_KEY":
			case "INSIDE_OBJECT_AFTER_KEY":
			case "INSIDE_OBJECT_AFTER_COMMA":
			case "INSIDE_OBJECT_START":
			case "INSIDE_OBJECT_BEFORE_VALUE":
			case "INSIDE_OBJECT_AFTER_VALUE": {
				result += "}";
				break;
			}
			case "INSIDE_ARRAY_START":
			case "INSIDE_ARRAY_AFTER_COMMA":
			case "INSIDE_ARRAY_AFTER_VALUE": {
				result += "]";
				break;
			}
			case "INSIDE_LITERAL": {
				const partialLiteral = input.substring(literalStart, input.length);
				if ("true".startsWith(partialLiteral)) result += "true".slice(partialLiteral.length);
				else if ("false".startsWith(partialLiteral)) result += "false".slice(partialLiteral.length);
				else if ("null".startsWith(partialLiteral)) result += "null".slice(partialLiteral.length);
			}
		}
	}
	return result;
}
function parsePartialJson(jsonText) {
	if (jsonText === void 0) return {
		value: void 0,
		state: "undefined-input"
	};
	let result = safeParseJSON({ text: jsonText });
	if (result.success) return {
		value: result.value,
		state: "successful-parse"
	};
	result = safeParseJSON({ text: fixJson(jsonText) });
	if (result.success) return {
		value: result.value,
		state: "repaired-parse"
	};
	return {
		value: void 0,
		state: "failed-parse"
	};
}
var textStreamPart2 = {
	code: "0",
	name: "text",
	parse: (value) => {
		if (typeof value !== "string") throw new Error("\"text\" parts expect a string value.");
		return {
			type: "text",
			value
		};
	}
};
var dataStreamPart = {
	code: "2",
	name: "data",
	parse: (value) => {
		if (!Array.isArray(value)) throw new Error("\"data\" parts expect an array value.");
		return {
			type: "data",
			value
		};
	}
};
var errorStreamPart2 = {
	code: "3",
	name: "error",
	parse: (value) => {
		if (typeof value !== "string") throw new Error("\"error\" parts expect a string value.");
		return {
			type: "error",
			value
		};
	}
};
var messageAnnotationsStreamPart = {
	code: "8",
	name: "message_annotations",
	parse: (value) => {
		if (!Array.isArray(value)) throw new Error("\"message_annotations\" parts expect an array value.");
		return {
			type: "message_annotations",
			value
		};
	}
};
var toolCallStreamPart = {
	code: "9",
	name: "tool_call",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("toolCallId" in value) || typeof value.toolCallId !== "string" || !("toolName" in value) || typeof value.toolName !== "string" || !("args" in value) || typeof value.args !== "object") throw new Error("\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.");
		return {
			type: "tool_call",
			value
		};
	}
};
var toolResultStreamPart = {
	code: "a",
	name: "tool_result",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("toolCallId" in value) || typeof value.toolCallId !== "string" || !("result" in value)) throw new Error("\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.");
		return {
			type: "tool_result",
			value
		};
	}
};
var toolCallStreamingStartStreamPart = {
	code: "b",
	name: "tool_call_streaming_start",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("toolCallId" in value) || typeof value.toolCallId !== "string" || !("toolName" in value) || typeof value.toolName !== "string") throw new Error("\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.");
		return {
			type: "tool_call_streaming_start",
			value
		};
	}
};
var toolCallDeltaStreamPart = {
	code: "c",
	name: "tool_call_delta",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("toolCallId" in value) || typeof value.toolCallId !== "string" || !("argsTextDelta" in value) || typeof value.argsTextDelta !== "string") throw new Error("\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.");
		return {
			type: "tool_call_delta",
			value
		};
	}
};
var finishMessageStreamPart = {
	code: "d",
	name: "finish_message",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("finishReason" in value) || typeof value.finishReason !== "string") throw new Error("\"finish_message\" parts expect an object with a \"finishReason\" property.");
		const result = { finishReason: value.finishReason };
		if ("usage" in value && value.usage != null && typeof value.usage === "object" && "promptTokens" in value.usage && "completionTokens" in value.usage) result.usage = {
			promptTokens: typeof value.usage.promptTokens === "number" ? value.usage.promptTokens : NaN,
			completionTokens: typeof value.usage.completionTokens === "number" ? value.usage.completionTokens : NaN
		};
		return {
			type: "finish_message",
			value: result
		};
	}
};
var finishStepStreamPart = {
	code: "e",
	name: "finish_step",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("finishReason" in value) || typeof value.finishReason !== "string") throw new Error("\"finish_step\" parts expect an object with a \"finishReason\" property.");
		const result = {
			finishReason: value.finishReason,
			isContinued: false
		};
		if ("usage" in value && value.usage != null && typeof value.usage === "object" && "promptTokens" in value.usage && "completionTokens" in value.usage) result.usage = {
			promptTokens: typeof value.usage.promptTokens === "number" ? value.usage.promptTokens : NaN,
			completionTokens: typeof value.usage.completionTokens === "number" ? value.usage.completionTokens : NaN
		};
		if ("isContinued" in value && typeof value.isContinued === "boolean") result.isContinued = value.isContinued;
		return {
			type: "finish_step",
			value: result
		};
	}
};
var startStepStreamPart = {
	code: "f",
	name: "start_step",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("messageId" in value) || typeof value.messageId !== "string") throw new Error("\"start_step\" parts expect an object with an \"id\" property.");
		return {
			type: "start_step",
			value: { messageId: value.messageId }
		};
	}
};
var reasoningStreamPart = {
	code: "g",
	name: "reasoning",
	parse: (value) => {
		if (typeof value !== "string") throw new Error("\"reasoning\" parts expect a string value.");
		return {
			type: "reasoning",
			value
		};
	}
};
var sourcePart = {
	code: "h",
	name: "source",
	parse: (value) => {
		if (value == null || typeof value !== "object") throw new Error("\"source\" parts expect a Source object.");
		return {
			type: "source",
			value
		};
	}
};
var redactedReasoningStreamPart = {
	code: "i",
	name: "redacted_reasoning",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("data" in value) || typeof value.data !== "string") throw new Error("\"redacted_reasoning\" parts expect an object with a \"data\" property.");
		return {
			type: "redacted_reasoning",
			value: { data: value.data }
		};
	}
};
var reasoningSignatureStreamPart = {
	code: "j",
	name: "reasoning_signature",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("signature" in value) || typeof value.signature !== "string") throw new Error("\"reasoning_signature\" parts expect an object with a \"signature\" property.");
		return {
			type: "reasoning_signature",
			value: { signature: value.signature }
		};
	}
};
var fileStreamPart = {
	code: "k",
	name: "file",
	parse: (value) => {
		if (value == null || typeof value !== "object" || !("data" in value) || typeof value.data !== "string" || !("mimeType" in value) || typeof value.mimeType !== "string") throw new Error("\"file\" parts expect an object with a \"data\" and \"mimeType\" property.");
		return {
			type: "file",
			value
		};
	}
};
var dataStreamParts = [
	textStreamPart2,
	dataStreamPart,
	errorStreamPart2,
	messageAnnotationsStreamPart,
	toolCallStreamPart,
	toolResultStreamPart,
	toolCallStreamingStartStreamPart,
	toolCallDeltaStreamPart,
	finishMessageStreamPart,
	finishStepStreamPart,
	startStepStreamPart,
	reasoningStreamPart,
	sourcePart,
	redactedReasoningStreamPart,
	reasoningSignatureStreamPart,
	fileStreamPart
];
var dataStreamPartsByCode = Object.fromEntries(dataStreamParts.map((part) => [part.code, part]));
var DataStreamStringPrefixes = Object.fromEntries(dataStreamParts.map((part) => [part.name, part.code]));
var validCodes2 = dataStreamParts.map((part) => part.code);
var parseDataStreamPart = (line) => {
	const firstSeparatorIndex = line.indexOf(":");
	if (firstSeparatorIndex === -1) throw new Error("Failed to parse stream string. No separator found.");
	const prefix = line.slice(0, firstSeparatorIndex);
	if (!validCodes2.includes(prefix)) throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);
	const code = prefix;
	const textValue = line.slice(firstSeparatorIndex + 1);
	const jsonValue = JSON.parse(textValue);
	return dataStreamPartsByCode[code].parse(jsonValue);
};
function formatDataStreamPart(type, value) {
	const streamPart = dataStreamParts.find((part) => part.name === type);
	if (!streamPart) throw new Error(`Invalid stream part type: ${type}`);
	return `${streamPart.code}:${JSON.stringify(value)}
`;
}
var NEWLINE = "\n".charCodeAt(0);
function concatChunks(chunks, totalLength) {
	const concatenatedChunks = new Uint8Array(totalLength);
	let offset = 0;
	for (const chunk of chunks) {
		concatenatedChunks.set(chunk, offset);
		offset += chunk.length;
	}
	chunks.length = 0;
	return concatenatedChunks;
}
async function processDataStream({ stream, onTextPart, onReasoningPart, onReasoningSignaturePart, onRedactedReasoningPart, onSourcePart, onFilePart, onDataPart, onErrorPart, onToolCallStreamingStartPart, onToolCallDeltaPart, onToolCallPart, onToolResultPart, onMessageAnnotationsPart, onFinishMessagePart, onFinishStepPart, onStartStepPart }) {
	const reader = stream.getReader();
	const decoder = new TextDecoder();
	const chunks = [];
	let totalLength = 0;
	while (true) {
		const { value } = await reader.read();
		if (value) {
			chunks.push(value);
			totalLength += value.length;
			if (value[value.length - 1] !== NEWLINE) continue;
		}
		if (chunks.length === 0) break;
		const concatenatedChunks = concatChunks(chunks, totalLength);
		totalLength = 0;
		const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split("\n").filter((line) => line !== "").map(parseDataStreamPart);
		for (const { type, value: value2 } of streamParts) switch (type) {
			case "text":
				await (onTextPart == null ? void 0 : onTextPart(value2));
				break;
			case "reasoning":
				await (onReasoningPart == null ? void 0 : onReasoningPart(value2));
				break;
			case "reasoning_signature":
				await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));
				break;
			case "redacted_reasoning":
				await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));
				break;
			case "file":
				await (onFilePart == null ? void 0 : onFilePart(value2));
				break;
			case "source":
				await (onSourcePart == null ? void 0 : onSourcePart(value2));
				break;
			case "data":
				await (onDataPart == null ? void 0 : onDataPart(value2));
				break;
			case "error":
				await (onErrorPart == null ? void 0 : onErrorPart(value2));
				break;
			case "message_annotations":
				await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));
				break;
			case "tool_call_streaming_start":
				await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));
				break;
			case "tool_call_delta":
				await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));
				break;
			case "tool_call":
				await (onToolCallPart == null ? void 0 : onToolCallPart(value2));
				break;
			case "tool_result":
				await (onToolResultPart == null ? void 0 : onToolResultPart(value2));
				break;
			case "finish_message":
				await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));
				break;
			case "finish_step":
				await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));
				break;
			case "start_step":
				await (onStartStepPart == null ? void 0 : onStartStepPart(value2));
				break;
			default: {
				const exhaustiveCheck = type;
				throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);
			}
		}
	}
}
async function processTextStream({ stream, onTextPart }) {
	const reader = stream.pipeThrough(new TextDecoderStream()).getReader();
	while (true) {
		const { done, value } = await reader.read();
		if (done) break;
		await onTextPart(value);
	}
}
function extractMaxToolInvocationStep(toolInvocations) {
	return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation) => {
		var _a$1;
		return Math.max(max, (_a$1 = toolInvocation.step) != null ? _a$1 : 0);
	}, 0);
}
function isDeepEqualData(obj1, obj2) {
	if (obj1 === obj2) return true;
	if (obj1 == null || obj2 == null) return false;
	if (typeof obj1 !== "object" && typeof obj2 !== "object") return obj1 === obj2;
	if (obj1.constructor !== obj2.constructor) return false;
	if (obj1 instanceof Date && obj2 instanceof Date) return obj1.getTime() === obj2.getTime();
	if (Array.isArray(obj1)) {
		if (obj1.length !== obj2.length) return false;
		for (let i = 0; i < obj1.length; i++) if (!isDeepEqualData(obj1[i], obj2[i])) return false;
		return true;
	}
	const keys1 = Object.keys(obj1);
	const keys2 = Object.keys(obj2);
	if (keys1.length !== keys2.length) return false;
	for (const key of keys1) {
		if (!keys2.includes(key)) return false;
		if (!isDeepEqualData(obj1[key], obj2[key])) return false;
	}
	return true;
}
var NEWLINE2 = "\n".charCodeAt(0);
function zodSchema(zodSchema2, options) {
	var _a$1;
	const useReferences = (_a$1 = options == null ? void 0 : options.useReferences) != null ? _a$1 : false;
	return jsonSchema(esm_default(zodSchema2, {
		$refStrategy: useReferences ? "root" : "none",
		target: "jsonSchema7"
	}), { validate: (value) => {
		const result = zodSchema2.safeParse(value);
		return result.success ? {
			success: true,
			value: result.data
		} : {
			success: false,
			error: result.error
		};
	} });
}
var schemaSymbol = Symbol.for("vercel.ai.schema");
function jsonSchema(jsonSchema2, { validate } = {}) {
	return {
		[schemaSymbol]: true,
		_type: void 0,
		[validatorSymbol]: true,
		jsonSchema: jsonSchema2,
		validate
	};
}
function isSchema(value) {
	return typeof value === "object" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && "jsonSchema" in value && "validate" in value;
}
function asSchema(schema) {
	return isSchema(schema) ? schema : zodSchema(schema);
}

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/platform/browser/globalThis.js
/**
* - globalThis (New standard)
* - self (Will return the current window instance for supported browsers)
* - window (fallback for older browser implementations)
* - global (NodeJS implementation)
* - <object> (When all else fails)
*/
/** only globals that common to node and browsers are allowed */
var _globalThis = typeof globalThis === "object" ? globalThis : typeof self === "object" ? self : typeof window === "object" ? window : typeof global === "object" ? global : {};

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/version.js
var VERSION = "1.9.0";

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/internal/semver.js
var re = /^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;
/**
* Create a function to test an API version to see if it is compatible with the provided ownVersion.
*
* The returned function has the following semantics:
* - Exact match is always compatible
* - Major versions must match exactly
*    - 1.x package cannot use global 2.x package
*    - 2.x package cannot use global 1.x package
* - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API
*    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects
*    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3
* - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor
* - Patch and build tag differences are not considered at this time
*
* @param ownVersion version which should be checked against
*/
function _makeCompatibilityCheck(ownVersion) {
	var acceptedVersions = new Set([ownVersion]);
	var rejectedVersions = /* @__PURE__ */ new Set();
	var myVersionMatch = ownVersion.match(re);
	if (!myVersionMatch) return function() {
		return false;
	};
	var ownVersionParsed = {
		major: +myVersionMatch[1],
		minor: +myVersionMatch[2],
		patch: +myVersionMatch[3],
		prerelease: myVersionMatch[4]
	};
	if (ownVersionParsed.prerelease != null) return function isExactmatch(globalVersion) {
		return globalVersion === ownVersion;
	};
	function _reject(v) {
		rejectedVersions.add(v);
		return false;
	}
	function _accept(v) {
		acceptedVersions.add(v);
		return true;
	}
	return function isCompatible$1(globalVersion) {
		if (acceptedVersions.has(globalVersion)) return true;
		if (rejectedVersions.has(globalVersion)) return false;
		var globalVersionMatch = globalVersion.match(re);
		if (!globalVersionMatch) return _reject(globalVersion);
		var globalVersionParsed = {
			major: +globalVersionMatch[1],
			minor: +globalVersionMatch[2],
			patch: +globalVersionMatch[3],
			prerelease: globalVersionMatch[4]
		};
		if (globalVersionParsed.prerelease != null) return _reject(globalVersion);
		if (ownVersionParsed.major !== globalVersionParsed.major) return _reject(globalVersion);
		if (ownVersionParsed.major === 0) {
			if (ownVersionParsed.minor === globalVersionParsed.minor && ownVersionParsed.patch <= globalVersionParsed.patch) return _accept(globalVersion);
			return _reject(globalVersion);
		}
		if (ownVersionParsed.minor <= globalVersionParsed.minor) return _accept(globalVersion);
		return _reject(globalVersion);
	};
}
/**
* Test an API version to see if it is compatible with this API.
*
* - Exact match is always compatible
* - Major versions must match exactly
*    - 1.x package cannot use global 2.x package
*    - 2.x package cannot use global 1.x package
* - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API
*    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects
*    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3
* - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor
* - Patch and build tag differences are not considered at this time
*
* @param version version of the API requesting an instance of the global API
*/
var isCompatible = _makeCompatibilityCheck(VERSION);

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/internal/global-utils.js
var major = VERSION.split(".")[0];
var GLOBAL_OPENTELEMETRY_API_KEY = Symbol.for("opentelemetry.js.api." + major);
var _global = _globalThis;
function registerGlobal(type, instance, diag, allowOverride) {
	var _a$1;
	if (allowOverride === void 0) allowOverride = false;
	var api = _global[GLOBAL_OPENTELEMETRY_API_KEY] = (_a$1 = _global[GLOBAL_OPENTELEMETRY_API_KEY]) !== null && _a$1 !== void 0 ? _a$1 : { version: VERSION };
	if (!allowOverride && api[type]) {
		var err = /* @__PURE__ */ new Error("@opentelemetry/api: Attempted duplicate registration of API: " + type);
		diag.error(err.stack || err.message);
		return false;
	}
	if (api.version !== VERSION) {
		var err = /* @__PURE__ */ new Error("@opentelemetry/api: Registration of version v" + api.version + " for " + type + " does not match previously registered API v" + VERSION);
		diag.error(err.stack || err.message);
		return false;
	}
	api[type] = instance;
	diag.debug("@opentelemetry/api: Registered a global for " + type + " v" + VERSION + ".");
	return true;
}
function getGlobal(type) {
	var _a$1, _b;
	var globalVersion = (_a$1 = _global[GLOBAL_OPENTELEMETRY_API_KEY]) === null || _a$1 === void 0 ? void 0 : _a$1.version;
	if (!globalVersion || !isCompatible(globalVersion)) return;
	return (_b = _global[GLOBAL_OPENTELEMETRY_API_KEY]) === null || _b === void 0 ? void 0 : _b[type];
}
function unregisterGlobal(type, diag) {
	diag.debug("@opentelemetry/api: Unregistering a global for " + type + " v" + VERSION + ".");
	var api = _global[GLOBAL_OPENTELEMETRY_API_KEY];
	if (api) delete api[type];
}

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js
var __read$3 = void 0 && (void 0).__read || function(o, n) {
	var m = typeof Symbol === "function" && o[Symbol.iterator];
	if (!m) return o;
	var i = m.call(o), r, ar = [], e;
	try {
		while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
	} catch (error) {
		e = { error };
	} finally {
		try {
			if (r && !r.done && (m = i["return"])) m.call(i);
		} finally {
			if (e) throw e.error;
		}
	}
	return ar;
};
var __spreadArray$3 = void 0 && (void 0).__spreadArray || function(to, from, pack) {
	if (pack || arguments.length === 2) {
		for (var i = 0, l = from.length, ar; i < l; i++) if (ar || !(i in from)) {
			if (!ar) ar = Array.prototype.slice.call(from, 0, i);
			ar[i] = from[i];
		}
	}
	return to.concat(ar || Array.prototype.slice.call(from));
};
/**
* Component Logger which is meant to be used as part of any component which
* will add automatically additional namespace in front of the log message.
* It will then forward all message to global diag logger
* @example
* const cLogger = diag.createComponentLogger({ namespace: '@opentelemetry/instrumentation-http' });
* cLogger.debug('test');
* // @opentelemetry/instrumentation-http test
*/
var DiagComponentLogger = function() {
	function DiagComponentLogger$1(props) {
		this._namespace = props.namespace || "DiagComponentLogger";
	}
	DiagComponentLogger$1.prototype.debug = function() {
		var args = [];
		for (var _i = 0; _i < arguments.length; _i++) args[_i] = arguments[_i];
		return logProxy("debug", this._namespace, args);
	};
	DiagComponentLogger$1.prototype.error = function() {
		var args = [];
		for (var _i = 0; _i < arguments.length; _i++) args[_i] = arguments[_i];
		return logProxy("error", this._namespace, args);
	};
	DiagComponentLogger$1.prototype.info = function() {
		var args = [];
		for (var _i = 0; _i < arguments.length; _i++) args[_i] = arguments[_i];
		return logProxy("info", this._namespace, args);
	};
	DiagComponentLogger$1.prototype.warn = function() {
		var args = [];
		for (var _i = 0; _i < arguments.length; _i++) args[_i] = arguments[_i];
		return logProxy("warn", this._namespace, args);
	};
	DiagComponentLogger$1.prototype.verbose = function() {
		var args = [];
		for (var _i = 0; _i < arguments.length; _i++) args[_i] = arguments[_i];
		return logProxy("verbose", this._namespace, args);
	};
	return DiagComponentLogger$1;
}();
function logProxy(funcName, namespace, args) {
	var logger = getGlobal("diag");
	if (!logger) return;
	args.unshift(namespace);
	return logger[funcName].apply(logger, __spreadArray$3([], __read$3(args), false));
}

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/diag/types.js
/**
* Defines the available internal logging levels for the diagnostic logger, the numeric values
* of the levels are defined to match the original values from the initial LogLevel to avoid
* compatibility/migration issues for any implementation that assume the numeric ordering.
*/
var DiagLogLevel;
(function(DiagLogLevel$1) {
	/** Diagnostic Logging level setting to disable all logging (except and forced logs) */
	DiagLogLevel$1[DiagLogLevel$1["NONE"] = 0] = "NONE";
	/** Identifies an error scenario */
	DiagLogLevel$1[DiagLogLevel$1["ERROR"] = 30] = "ERROR";
	/** Identifies a warning scenario */
	DiagLogLevel$1[DiagLogLevel$1["WARN"] = 50] = "WARN";
	/** General informational log message */
	DiagLogLevel$1[DiagLogLevel$1["INFO"] = 60] = "INFO";
	/** General debug log message */
	DiagLogLevel$1[DiagLogLevel$1["DEBUG"] = 70] = "DEBUG";
	/**
	* Detailed trace level logging should only be used for development, should only be set
	* in a development environment.
	*/
	DiagLogLevel$1[DiagLogLevel$1["VERBOSE"] = 80] = "VERBOSE";
	/** Used to set the logging level to include all logging */
	DiagLogLevel$1[DiagLogLevel$1["ALL"] = 9999] = "ALL";
})(DiagLogLevel || (DiagLogLevel = {}));

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js
function createLogLevelDiagLogger(maxLevel, logger) {
	if (maxLevel < DiagLogLevel.NONE) maxLevel = DiagLogLevel.NONE;
	else if (maxLevel > DiagLogLevel.ALL) maxLevel = DiagLogLevel.ALL;
	logger = logger || {};
	function _filterFunc(funcName, theLevel) {
		var theFunc = logger[funcName];
		if (typeof theFunc === "function" && maxLevel >= theLevel) return theFunc.bind(logger);
		return function() {};
	}
	return {
		error: _filterFunc("error", DiagLogLevel.ERROR),
		warn: _filterFunc("warn", DiagLogLevel.WARN),
		info: _filterFunc("info", DiagLogLevel.INFO),
		debug: _filterFunc("debug", DiagLogLevel.DEBUG),
		verbose: _filterFunc("verbose", DiagLogLevel.VERBOSE)
	};
}

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/api/diag.js
var __read$2 = void 0 && (void 0).__read || function(o, n) {
	var m = typeof Symbol === "function" && o[Symbol.iterator];
	if (!m) return o;
	var i = m.call(o), r, ar = [], e;
	try {
		while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
	} catch (error) {
		e = { error };
	} finally {
		try {
			if (r && !r.done && (m = i["return"])) m.call(i);
		} finally {
			if (e) throw e.error;
		}
	}
	return ar;
};
var __spreadArray$2 = void 0 && (void 0).__spreadArray || function(to, from, pack) {
	if (pack || arguments.length === 2) {
		for (var i = 0, l = from.length, ar; i < l; i++) if (ar || !(i in from)) {
			if (!ar) ar = Array.prototype.slice.call(from, 0, i);
			ar[i] = from[i];
		}
	}
	return to.concat(ar || Array.prototype.slice.call(from));
};
var API_NAME$2 = "diag";
/**
* Singleton object which represents the entry point to the OpenTelemetry internal
* diagnostic API
*/
var DiagAPI = function() {
	/**
	* Private internal constructor
	* @private
	*/
	function DiagAPI$1() {
		function _logProxy(funcName) {
			return function() {
				var args = [];
				for (var _i = 0; _i < arguments.length; _i++) args[_i] = arguments[_i];
				var logger = getGlobal("diag");
				if (!logger) return;
				return logger[funcName].apply(logger, __spreadArray$2([], __read$2(args), false));
			};
		}
		var self$1 = this;
		var setLogger = function(logger, optionsOrLogLevel) {
			var _a$1, _b, _c;
			if (optionsOrLogLevel === void 0) optionsOrLogLevel = { logLevel: DiagLogLevel.INFO };
			if (logger === self$1) {
				var err = /* @__PURE__ */ new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");
				self$1.error((_a$1 = err.stack) !== null && _a$1 !== void 0 ? _a$1 : err.message);
				return false;
			}
			if (typeof optionsOrLogLevel === "number") optionsOrLogLevel = { logLevel: optionsOrLogLevel };
			var oldLogger = getGlobal("diag");
			var newLogger = createLogLevelDiagLogger((_b = optionsOrLogLevel.logLevel) !== null && _b !== void 0 ? _b : DiagLogLevel.INFO, logger);
			if (oldLogger && !optionsOrLogLevel.suppressOverrideMessage) {
				var stack = (_c = (/* @__PURE__ */ new Error()).stack) !== null && _c !== void 0 ? _c : "<failed to generate stacktrace>";
				oldLogger.warn("Current logger will be overwritten from " + stack);
				newLogger.warn("Current logger will overwrite one already registered from " + stack);
			}
			return registerGlobal("diag", newLogger, self$1, true);
		};
		self$1.setLogger = setLogger;
		self$1.disable = function() {
			unregisterGlobal(API_NAME$2, self$1);
		};
		self$1.createComponentLogger = function(options) {
			return new DiagComponentLogger(options);
		};
		self$1.verbose = _logProxy("verbose");
		self$1.debug = _logProxy("debug");
		self$1.info = _logProxy("info");
		self$1.warn = _logProxy("warn");
		self$1.error = _logProxy("error");
	}
	/** Get the singleton instance of the DiagAPI API */
	DiagAPI$1.instance = function() {
		if (!this._instance) this._instance = new DiagAPI$1();
		return this._instance;
	};
	return DiagAPI$1;
}();

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/context/context.js
/** Get a key to uniquely identify a context value */
function createContextKey(description) {
	return Symbol.for(description);
}
var BaseContext = function() {
	/**
	* Construct a new context which inherits values from an optional parent context.
	*
	* @param parentContext a context from which to inherit values
	*/
	function BaseContext$1(parentContext) {
		var self$1 = this;
		self$1._currentContext = parentContext ? new Map(parentContext) : /* @__PURE__ */ new Map();
		self$1.getValue = function(key) {
			return self$1._currentContext.get(key);
		};
		self$1.setValue = function(key, value) {
			var context = new BaseContext$1(self$1._currentContext);
			context._currentContext.set(key, value);
			return context;
		};
		self$1.deleteValue = function(key) {
			var context = new BaseContext$1(self$1._currentContext);
			context._currentContext.delete(key);
			return context;
		};
	}
	return BaseContext$1;
}();
/** The root context is used as the default parent context when there is no active context */
var ROOT_CONTEXT = new BaseContext();

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js
var __read$1 = void 0 && (void 0).__read || function(o, n) {
	var m = typeof Symbol === "function" && o[Symbol.iterator];
	if (!m) return o;
	var i = m.call(o), r, ar = [], e;
	try {
		while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
	} catch (error) {
		e = { error };
	} finally {
		try {
			if (r && !r.done && (m = i["return"])) m.call(i);
		} finally {
			if (e) throw e.error;
		}
	}
	return ar;
};
var __spreadArray$1 = void 0 && (void 0).__spreadArray || function(to, from, pack) {
	if (pack || arguments.length === 2) {
		for (var i = 0, l = from.length, ar; i < l; i++) if (ar || !(i in from)) {
			if (!ar) ar = Array.prototype.slice.call(from, 0, i);
			ar[i] = from[i];
		}
	}
	return to.concat(ar || Array.prototype.slice.call(from));
};
var NoopContextManager = function() {
	function NoopContextManager$1() {}
	NoopContextManager$1.prototype.active = function() {
		return ROOT_CONTEXT;
	};
	NoopContextManager$1.prototype.with = function(_context, fn, thisArg) {
		var args = [];
		for (var _i = 3; _i < arguments.length; _i++) args[_i - 3] = arguments[_i];
		return fn.call.apply(fn, __spreadArray$1([thisArg], __read$1(args), false));
	};
	NoopContextManager$1.prototype.bind = function(_context, target) {
		return target;
	};
	NoopContextManager$1.prototype.enable = function() {
		return this;
	};
	NoopContextManager$1.prototype.disable = function() {
		return this;
	};
	return NoopContextManager$1;
}();

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/api/context.js
var __read = void 0 && (void 0).__read || function(o, n) {
	var m = typeof Symbol === "function" && o[Symbol.iterator];
	if (!m) return o;
	var i = m.call(o), r, ar = [], e;
	try {
		while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
	} catch (error) {
		e = { error };
	} finally {
		try {
			if (r && !r.done && (m = i["return"])) m.call(i);
		} finally {
			if (e) throw e.error;
		}
	}
	return ar;
};
var __spreadArray = void 0 && (void 0).__spreadArray || function(to, from, pack) {
	if (pack || arguments.length === 2) {
		for (var i = 0, l = from.length, ar; i < l; i++) if (ar || !(i in from)) {
			if (!ar) ar = Array.prototype.slice.call(from, 0, i);
			ar[i] = from[i];
		}
	}
	return to.concat(ar || Array.prototype.slice.call(from));
};
var API_NAME$1 = "context";
var NOOP_CONTEXT_MANAGER = new NoopContextManager();
/**
* Singleton object which represents the entry point to the OpenTelemetry Context API
*/
var ContextAPI = function() {
	/** Empty private constructor prevents end users from constructing a new instance of the API */
	function ContextAPI$1() {}
	/** Get the singleton instance of the Context API */
	ContextAPI$1.getInstance = function() {
		if (!this._instance) this._instance = new ContextAPI$1();
		return this._instance;
	};
	/**
	* Set the current context manager.
	*
	* @returns true if the context manager was successfully registered, else false
	*/
	ContextAPI$1.prototype.setGlobalContextManager = function(contextManager) {
		return registerGlobal(API_NAME$1, contextManager, DiagAPI.instance());
	};
	/**
	* Get the currently active context
	*/
	ContextAPI$1.prototype.active = function() {
		return this._getContextManager().active();
	};
	/**
	* Execute a function with an active context
	*
	* @param context context to be active during function execution
	* @param fn function to execute in a context
	* @param thisArg optional receiver to be used for calling fn
	* @param args optional arguments forwarded to fn
	*/
	ContextAPI$1.prototype.with = function(context, fn, thisArg) {
		var _a$1;
		var args = [];
		for (var _i = 3; _i < arguments.length; _i++) args[_i - 3] = arguments[_i];
		return (_a$1 = this._getContextManager()).with.apply(_a$1, __spreadArray([
			context,
			fn,
			thisArg
		], __read(args), false));
	};
	/**
	* Bind a context to a target function or event emitter
	*
	* @param context context to bind to the event emitter or function. Defaults to the currently active context
	* @param target function or event emitter to bind
	*/
	ContextAPI$1.prototype.bind = function(context, target) {
		return this._getContextManager().bind(context, target);
	};
	ContextAPI$1.prototype._getContextManager = function() {
		return getGlobal(API_NAME$1) || NOOP_CONTEXT_MANAGER;
	};
	/** Disable and remove the global context manager */
	ContextAPI$1.prototype.disable = function() {
		this._getContextManager().disable();
		unregisterGlobal(API_NAME$1, DiagAPI.instance());
	};
	return ContextAPI$1;
}();

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js
var TraceFlags;
(function(TraceFlags$1) {
	/** Represents no flag set. */
	TraceFlags$1[TraceFlags$1["NONE"] = 0] = "NONE";
	/** Bit to represent whether trace is sampled in trace flags. */
	TraceFlags$1[TraceFlags$1["SAMPLED"] = 1] = "SAMPLED";
})(TraceFlags || (TraceFlags = {}));

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js
var INVALID_SPANID = "0000000000000000";
var INVALID_TRACEID = "00000000000000000000000000000000";
var INVALID_SPAN_CONTEXT = {
	traceId: INVALID_TRACEID,
	spanId: INVALID_SPANID,
	traceFlags: TraceFlags.NONE
};

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js
/**
* The NonRecordingSpan is the default {@link Span} that is used when no Span
* implementation is available. All operations are no-op including context
* propagation.
*/
var NonRecordingSpan = function() {
	function NonRecordingSpan$1(_spanContext) {
		if (_spanContext === void 0) _spanContext = INVALID_SPAN_CONTEXT;
		this._spanContext = _spanContext;
	}
	NonRecordingSpan$1.prototype.spanContext = function() {
		return this._spanContext;
	};
	NonRecordingSpan$1.prototype.setAttribute = function(_key, _value) {
		return this;
	};
	NonRecordingSpan$1.prototype.setAttributes = function(_attributes) {
		return this;
	};
	NonRecordingSpan$1.prototype.addEvent = function(_name, _attributes) {
		return this;
	};
	NonRecordingSpan$1.prototype.addLink = function(_link) {
		return this;
	};
	NonRecordingSpan$1.prototype.addLinks = function(_links) {
		return this;
	};
	NonRecordingSpan$1.prototype.setStatus = function(_status) {
		return this;
	};
	NonRecordingSpan$1.prototype.updateName = function(_name) {
		return this;
	};
	NonRecordingSpan$1.prototype.end = function(_endTime) {};
	NonRecordingSpan$1.prototype.isRecording = function() {
		return false;
	};
	NonRecordingSpan$1.prototype.recordException = function(_exception, _time) {};
	return NonRecordingSpan$1;
}();

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/context-utils.js
/**
* span key
*/
var SPAN_KEY = createContextKey("OpenTelemetry Context Key SPAN");
/**
* Return the span if one exists
*
* @param context context to get span from
*/
function getSpan(context) {
	return context.getValue(SPAN_KEY) || void 0;
}
/**
* Gets the span from the current context, if one exists.
*/
function getActiveSpan() {
	return getSpan(ContextAPI.getInstance().active());
}
/**
* Set the span on a context
*
* @param context context to use as parent
* @param span span to set active
*/
function setSpan(context, span) {
	return context.setValue(SPAN_KEY, span);
}
/**
* Remove current span stored in the context
*
* @param context context to delete span from
*/
function deleteSpan(context) {
	return context.deleteValue(SPAN_KEY);
}
/**
* Wrap span context in a NoopSpan and set as span in a new
* context
*
* @param context context to set active span on
* @param spanContext span context to be wrapped
*/
function setSpanContext(context, spanContext) {
	return setSpan(context, new NonRecordingSpan(spanContext));
}
/**
* Get the span context of the span if it exists.
*
* @param context context to get values from
*/
function getSpanContext(context) {
	var _a$1;
	return (_a$1 = getSpan(context)) === null || _a$1 === void 0 ? void 0 : _a$1.spanContext();
}

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js
var VALID_TRACEID_REGEX = /^([0-9a-f]{32})$/i;
var VALID_SPANID_REGEX = /^[0-9a-f]{16}$/i;
function isValidTraceId(traceId) {
	return VALID_TRACEID_REGEX.test(traceId) && traceId !== INVALID_TRACEID;
}
function isValidSpanId(spanId) {
	return VALID_SPANID_REGEX.test(spanId) && spanId !== INVALID_SPANID;
}
/**
* Returns true if this {@link SpanContext} is valid.
* @return true if this {@link SpanContext} is valid.
*/
function isSpanContextValid(spanContext) {
	return isValidTraceId(spanContext.traceId) && isValidSpanId(spanContext.spanId);
}
/**
* Wrap the given {@link SpanContext} in a new non-recording {@link Span}
*
* @param spanContext span context to be wrapped
* @returns a new non-recording {@link Span} with the provided context
*/
function wrapSpanContext(spanContext) {
	return new NonRecordingSpan(spanContext);
}

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js
var contextApi = ContextAPI.getInstance();
/**
* No-op implementations of {@link Tracer}.
*/
var NoopTracer = function() {
	function NoopTracer$1() {}
	NoopTracer$1.prototype.startSpan = function(name$1, options, context) {
		if (context === void 0) context = contextApi.active();
		var root = Boolean(options === null || options === void 0 ? void 0 : options.root);
		if (root) return new NonRecordingSpan();
		var parentFromContext = context && getSpanContext(context);
		if (isSpanContext(parentFromContext) && isSpanContextValid(parentFromContext)) return new NonRecordingSpan(parentFromContext);
		else return new NonRecordingSpan();
	};
	NoopTracer$1.prototype.startActiveSpan = function(name$1, arg2, arg3, arg4) {
		var opts;
		var ctx;
		var fn;
		if (arguments.length < 2) return;
		else if (arguments.length === 2) fn = arg2;
		else if (arguments.length === 3) {
			opts = arg2;
			fn = arg3;
		} else {
			opts = arg2;
			ctx = arg3;
			fn = arg4;
		}
		var parentContext = ctx !== null && ctx !== void 0 ? ctx : contextApi.active();
		var span = this.startSpan(name$1, opts, parentContext);
		var contextWithSpanSet = setSpan(parentContext, span);
		return contextApi.with(contextWithSpanSet, fn, void 0, span);
	};
	return NoopTracer$1;
}();
function isSpanContext(spanContext) {
	return typeof spanContext === "object" && typeof spanContext["spanId"] === "string" && typeof spanContext["traceId"] === "string" && typeof spanContext["traceFlags"] === "number";
}

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js
var NOOP_TRACER = new NoopTracer();
/**
* Proxy tracer provided by the proxy tracer provider
*/
var ProxyTracer = function() {
	function ProxyTracer$1(_provider, name$1, version, options) {
		this._provider = _provider;
		this.name = name$1;
		this.version = version;
		this.options = options;
	}
	ProxyTracer$1.prototype.startSpan = function(name$1, options, context) {
		return this._getTracer().startSpan(name$1, options, context);
	};
	ProxyTracer$1.prototype.startActiveSpan = function(_name, _options, _context, _fn) {
		var tracer = this._getTracer();
		return Reflect.apply(tracer.startActiveSpan, tracer, arguments);
	};
	/**
	* Try to get a tracer from the proxy tracer provider.
	* If the proxy tracer provider has no delegate, return a noop tracer.
	*/
	ProxyTracer$1.prototype._getTracer = function() {
		if (this._delegate) return this._delegate;
		var tracer = this._provider.getDelegateTracer(this.name, this.version, this.options);
		if (!tracer) return NOOP_TRACER;
		this._delegate = tracer;
		return this._delegate;
	};
	return ProxyTracer$1;
}();

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js
/**
* An implementation of the {@link TracerProvider} which returns an impotent
* Tracer for all calls to `getTracer`.
*
* All operations are no-op.
*/
var NoopTracerProvider = function() {
	function NoopTracerProvider$1() {}
	NoopTracerProvider$1.prototype.getTracer = function(_name, _version, _options) {
		return new NoopTracer();
	};
	return NoopTracerProvider$1;
}();

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js
var NOOP_TRACER_PROVIDER = new NoopTracerProvider();
/**
* Tracer provider which provides {@link ProxyTracer}s.
*
* Before a delegate is set, tracers provided are NoOp.
*   When a delegate is set, traces are provided from the delegate.
*   When a delegate is set after tracers have already been provided,
*   all tracers already provided will use the provided delegate implementation.
*/
var ProxyTracerProvider = function() {
	function ProxyTracerProvider$1() {}
	/**
	* Get a {@link ProxyTracer}
	*/
	ProxyTracerProvider$1.prototype.getTracer = function(name$1, version, options) {
		var _a$1;
		return (_a$1 = this.getDelegateTracer(name$1, version, options)) !== null && _a$1 !== void 0 ? _a$1 : new ProxyTracer(this, name$1, version, options);
	};
	ProxyTracerProvider$1.prototype.getDelegate = function() {
		var _a$1;
		return (_a$1 = this._delegate) !== null && _a$1 !== void 0 ? _a$1 : NOOP_TRACER_PROVIDER;
	};
	/**
	* Set the delegate tracer provider
	*/
	ProxyTracerProvider$1.prototype.setDelegate = function(delegate) {
		this._delegate = delegate;
	};
	ProxyTracerProvider$1.prototype.getDelegateTracer = function(name$1, version, options) {
		var _a$1;
		return (_a$1 = this._delegate) === null || _a$1 === void 0 ? void 0 : _a$1.getTracer(name$1, version, options);
	};
	return ProxyTracerProvider$1;
}();

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace/status.js
/**
* An enumeration of status codes.
*/
var SpanStatusCode;
(function(SpanStatusCode$1) {
	/**
	* The default status.
	*/
	SpanStatusCode$1[SpanStatusCode$1["UNSET"] = 0] = "UNSET";
	/**
	* The operation has been validated by an Application developer or
	* Operator to have completed successfully.
	*/
	SpanStatusCode$1[SpanStatusCode$1["OK"] = 1] = "OK";
	/**
	* The operation contains an error.
	*/
	SpanStatusCode$1[SpanStatusCode$1["ERROR"] = 2] = "ERROR";
})(SpanStatusCode || (SpanStatusCode = {}));

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/api/trace.js
var API_NAME = "trace";
/**
* Singleton object which represents the entry point to the OpenTelemetry Tracing API
*/
var TraceAPI = function() {
	/** Empty private constructor prevents end users from constructing a new instance of the API */
	function TraceAPI$1() {
		this._proxyTracerProvider = new ProxyTracerProvider();
		this.wrapSpanContext = wrapSpanContext;
		this.isSpanContextValid = isSpanContextValid;
		this.deleteSpan = deleteSpan;
		this.getSpan = getSpan;
		this.getActiveSpan = getActiveSpan;
		this.getSpanContext = getSpanContext;
		this.setSpan = setSpan;
		this.setSpanContext = setSpanContext;
	}
	/** Get the singleton instance of the Trace API */
	TraceAPI$1.getInstance = function() {
		if (!this._instance) this._instance = new TraceAPI$1();
		return this._instance;
	};
	/**
	* Set the current global tracer.
	*
	* @returns true if the tracer provider was successfully registered, else false
	*/
	TraceAPI$1.prototype.setGlobalTracerProvider = function(provider) {
		var success = registerGlobal(API_NAME, this._proxyTracerProvider, DiagAPI.instance());
		if (success) this._proxyTracerProvider.setDelegate(provider);
		return success;
	};
	/**
	* Returns the global tracer provider.
	*/
	TraceAPI$1.prototype.getTracerProvider = function() {
		return getGlobal(API_NAME) || this._proxyTracerProvider;
	};
	/**
	* Returns a tracer from the global tracer provider.
	*/
	TraceAPI$1.prototype.getTracer = function(name$1, version) {
		return this.getTracerProvider().getTracer(name$1, version);
	};
	/** Remove the global tracer provider */
	TraceAPI$1.prototype.disable = function() {
		unregisterGlobal(API_NAME, DiagAPI.instance());
		this._proxyTracerProvider = new ProxyTracerProvider();
	};
	return TraceAPI$1;
}();

//#endregion
//#region node_modules/@opentelemetry/api/build/esm/trace-api.js
/** Entrypoint for trace API */
var trace = TraceAPI.getInstance();

//#endregion
//#region node_modules/ai/dist/index.mjs
var __defProp = Object.defineProperty;
var __export = (target, all) => {
	for (var name17 in all) __defProp(target, name17, {
		get: all[name17],
		enumerable: true
	});
};
function createDataStream({ execute, onError = () => "An error occurred." }) {
	let controller;
	const ongoingStreamPromises = [];
	const stream = new ReadableStream({ start(controllerArg) {
		controller = controllerArg;
	} });
	function safeEnqueue(data) {
		try {
			controller.enqueue(data);
		} catch (error) {}
	}
	try {
		const result = execute({
			write(data) {
				safeEnqueue(data);
			},
			writeData(data) {
				safeEnqueue(formatDataStreamPart("data", [data]));
			},
			writeMessageAnnotation(annotation) {
				safeEnqueue(formatDataStreamPart("message_annotations", [annotation]));
			},
			writeSource(source) {
				safeEnqueue(formatDataStreamPart("source", source));
			},
			merge(streamArg) {
				ongoingStreamPromises.push((async () => {
					const reader = streamArg.getReader();
					while (true) {
						const { done, value } = await reader.read();
						if (done) break;
						safeEnqueue(value);
					}
				})().catch((error) => {
					safeEnqueue(formatDataStreamPart("error", onError(error)));
				}));
			},
			onError
		});
		if (result) ongoingStreamPromises.push(result.catch((error) => {
			safeEnqueue(formatDataStreamPart("error", onError(error)));
		}));
	} catch (error) {
		safeEnqueue(formatDataStreamPart("error", onError(error)));
	}
	const waitForStreams = new Promise(async (resolve) => {
		while (ongoingStreamPromises.length > 0) await ongoingStreamPromises.shift();
		resolve();
	});
	waitForStreams.finally(() => {
		try {
			controller.close();
		} catch (error) {}
	});
	return stream;
}
function prepareResponseHeaders(headers, { contentType, dataStreamVersion }) {
	const responseHeaders = new Headers(headers != null ? headers : {});
	if (!responseHeaders.has("Content-Type")) responseHeaders.set("Content-Type", contentType);
	if (dataStreamVersion !== void 0) responseHeaders.set("X-Vercel-AI-Data-Stream", dataStreamVersion);
	return responseHeaders;
}
function createDataStreamResponse({ status, statusText, headers, execute, onError }) {
	return new Response(createDataStream({
		execute,
		onError
	}).pipeThrough(new TextEncoderStream()), {
		status,
		statusText,
		headers: prepareResponseHeaders(headers, {
			contentType: "text/plain; charset=utf-8",
			dataStreamVersion: "v1"
		})
	});
}
function prepareOutgoingHttpHeaders(headers, { contentType, dataStreamVersion }) {
	const outgoingHeaders = {};
	if (headers != null) for (const [key, value] of Object.entries(headers)) outgoingHeaders[key] = value;
	if (outgoingHeaders["Content-Type"] == null) outgoingHeaders["Content-Type"] = contentType;
	if (dataStreamVersion !== void 0) outgoingHeaders["X-Vercel-AI-Data-Stream"] = dataStreamVersion;
	return outgoingHeaders;
}
function writeToServerResponse({ response, status, statusText, headers, stream }) {
	response.writeHead(status != null ? status : 200, statusText, headers);
	const reader = stream.getReader();
	const read = async () => {
		try {
			while (true) {
				const { done, value } = await reader.read();
				if (done) break;
				response.write(value);
			}
		} catch (error) {
			throw error;
		} finally {
			response.end();
		}
	};
	read();
}
function pipeDataStreamToResponse(response, { status, statusText, headers, execute, onError }) {
	writeToServerResponse({
		response,
		status,
		statusText,
		headers: prepareOutgoingHttpHeaders(headers, {
			contentType: "text/plain; charset=utf-8",
			dataStreamVersion: "v1"
		}),
		stream: createDataStream({
			execute,
			onError
		}).pipeThrough(new TextEncoderStream())
	});
}
var name = "AI_InvalidArgumentError";
var marker = `vercel.ai.error.${name}`;
var symbol = Symbol.for(marker);
var _a;
var InvalidArgumentError$1 = class extends AISDKError {
	constructor({ parameter, value, message }) {
		super({
			name,
			message: `Invalid argument for parameter ${parameter}: ${message}`
		});
		this[_a] = true;
		this.parameter = parameter;
		this.value = value;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker);
	}
};
_a = symbol;
var name2 = "AI_RetryError";
var marker2 = `vercel.ai.error.${name2}`;
var symbol2 = Symbol.for(marker2);
var _a2;
var RetryError = class extends AISDKError {
	constructor({ message, reason, errors }) {
		super({
			name: name2,
			message
		});
		this[_a2] = true;
		this.reason = reason;
		this.errors = errors;
		this.lastError = errors[errors.length - 1];
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker2);
	}
};
_a2 = symbol2;
var retryWithExponentialBackoff = ({ maxRetries = 2, initialDelayInMs = 2e3, backoffFactor = 2 } = {}) => async (f) => _retryWithExponentialBackoff(f, {
	maxRetries,
	delayInMs: initialDelayInMs,
	backoffFactor
});
async function _retryWithExponentialBackoff(f, { maxRetries, delayInMs, backoffFactor }, errors = []) {
	try {
		return await f();
	} catch (error) {
		if (isAbortError(error)) throw error;
		if (maxRetries === 0) throw error;
		const errorMessage = getErrorMessage(error);
		const newErrors = [...errors, error];
		const tryNumber = newErrors.length;
		if (tryNumber > maxRetries) throw new RetryError({
			message: `Failed after ${tryNumber} attempts. Last error: ${errorMessage}`,
			reason: "maxRetriesExceeded",
			errors: newErrors
		});
		if (error instanceof Error && APICallError.isInstance(error) && error.isRetryable === true && tryNumber <= maxRetries) {
			await delay(delayInMs);
			return _retryWithExponentialBackoff(f, {
				maxRetries,
				delayInMs: backoffFactor * delayInMs,
				backoffFactor
			}, newErrors);
		}
		if (tryNumber === 1) throw error;
		throw new RetryError({
			message: `Failed after ${tryNumber} attempts with non-retryable error: '${errorMessage}'`,
			reason: "errorNotRetryable",
			errors: newErrors
		});
	}
}
function prepareRetries({ maxRetries }) {
	if (maxRetries != null) {
		if (!Number.isInteger(maxRetries)) throw new InvalidArgumentError$1({
			parameter: "maxRetries",
			value: maxRetries,
			message: "maxRetries must be an integer"
		});
		if (maxRetries < 0) throw new InvalidArgumentError$1({
			parameter: "maxRetries",
			value: maxRetries,
			message: "maxRetries must be >= 0"
		});
	}
	const maxRetriesResult = maxRetries != null ? maxRetries : 2;
	return {
		maxRetries: maxRetriesResult,
		retry: retryWithExponentialBackoff({ maxRetries: maxRetriesResult })
	};
}
function assembleOperationName({ operationId, telemetry }) {
	return {
		"operation.name": `${operationId}${(telemetry == null ? void 0 : telemetry.functionId) != null ? ` ${telemetry.functionId}` : ""}`,
		"resource.name": telemetry == null ? void 0 : telemetry.functionId,
		"ai.operationId": operationId,
		"ai.telemetry.functionId": telemetry == null ? void 0 : telemetry.functionId
	};
}
function getBaseTelemetryAttributes({ model, settings, telemetry, headers }) {
	var _a17;
	return {
		"ai.model.provider": model.provider,
		"ai.model.id": model.modelId,
		...Object.entries(settings).reduce((attributes, [key, value]) => {
			attributes[`ai.settings.${key}`] = value;
			return attributes;
		}, {}),
		...Object.entries((_a17 = telemetry == null ? void 0 : telemetry.metadata) != null ? _a17 : {}).reduce((attributes, [key, value]) => {
			attributes[`ai.telemetry.metadata.${key}`] = value;
			return attributes;
		}, {}),
		...Object.entries(headers != null ? headers : {}).reduce((attributes, [key, value]) => {
			if (value !== void 0) attributes[`ai.request.headers.${key}`] = value;
			return attributes;
		}, {})
	};
}
var noopTracer = {
	startSpan() {
		return noopSpan;
	},
	startActiveSpan(name17, arg1, arg2, arg3) {
		if (typeof arg1 === "function") return arg1(noopSpan);
		if (typeof arg2 === "function") return arg2(noopSpan);
		if (typeof arg3 === "function") return arg3(noopSpan);
	}
};
var noopSpan = {
	spanContext() {
		return noopSpanContext;
	},
	setAttribute() {
		return this;
	},
	setAttributes() {
		return this;
	},
	addEvent() {
		return this;
	},
	addLink() {
		return this;
	},
	addLinks() {
		return this;
	},
	setStatus() {
		return this;
	},
	updateName() {
		return this;
	},
	end() {
		return this;
	},
	isRecording() {
		return false;
	},
	recordException() {
		return this;
	}
};
var noopSpanContext = {
	traceId: "",
	spanId: "",
	traceFlags: 0
};
function getTracer({ isEnabled = false, tracer } = {}) {
	if (!isEnabled) return noopTracer;
	if (tracer) return tracer;
	return trace.getTracer("ai");
}
function recordSpan({ name: name17, tracer, attributes, fn, endWhenDone = true }) {
	return tracer.startActiveSpan(name17, { attributes }, async (span) => {
		try {
			const result = await fn(span);
			if (endWhenDone) span.end();
			return result;
		} catch (error) {
			try {
				if (error instanceof Error) {
					span.recordException({
						name: error.name,
						message: error.message,
						stack: error.stack
					});
					span.setStatus({
						code: SpanStatusCode.ERROR,
						message: error.message
					});
				} else span.setStatus({ code: SpanStatusCode.ERROR });
			} finally {
				span.end();
			}
			throw error;
		}
	});
}
function selectTelemetryAttributes({ telemetry, attributes }) {
	if ((telemetry == null ? void 0 : telemetry.isEnabled) !== true) return {};
	return Object.entries(attributes).reduce((attributes2, [key, value]) => {
		if (value === void 0) return attributes2;
		if (typeof value === "object" && "input" in value && typeof value.input === "function") {
			if ((telemetry == null ? void 0 : telemetry.recordInputs) === false) return attributes2;
			const result = value.input();
			return result === void 0 ? attributes2 : {
				...attributes2,
				[key]: result
			};
		}
		if (typeof value === "object" && "output" in value && typeof value.output === "function") {
			if ((telemetry == null ? void 0 : telemetry.recordOutputs) === false) return attributes2;
			const result = value.output();
			return result === void 0 ? attributes2 : {
				...attributes2,
				[key]: result
			};
		}
		return {
			...attributes2,
			[key]: value
		};
	}, {});
}
async function embed({ model, value, maxRetries: maxRetriesArg, abortSignal, headers, experimental_telemetry: telemetry }) {
	const { maxRetries, retry } = prepareRetries({ maxRetries: maxRetriesArg });
	const baseTelemetryAttributes = getBaseTelemetryAttributes({
		model,
		telemetry,
		headers,
		settings: { maxRetries }
	});
	const tracer = getTracer(telemetry);
	return recordSpan({
		name: "ai.embed",
		attributes: selectTelemetryAttributes({
			telemetry,
			attributes: {
				...assembleOperationName({
					operationId: "ai.embed",
					telemetry
				}),
				...baseTelemetryAttributes,
				"ai.value": { input: () => JSON.stringify(value) }
			}
		}),
		tracer,
		fn: async (span) => {
			const { embedding, usage, rawResponse } = await retry(() => recordSpan({
				name: "ai.embed.doEmbed",
				attributes: selectTelemetryAttributes({
					telemetry,
					attributes: {
						...assembleOperationName({
							operationId: "ai.embed.doEmbed",
							telemetry
						}),
						...baseTelemetryAttributes,
						"ai.values": { input: () => [JSON.stringify(value)] }
					}
				}),
				tracer,
				fn: async (doEmbedSpan) => {
					var _a17;
					const modelResponse = await model.doEmbed({
						values: [value],
						abortSignal,
						headers
					});
					const embedding2 = modelResponse.embeddings[0];
					const usage2 = (_a17 = modelResponse.usage) != null ? _a17 : { tokens: NaN };
					doEmbedSpan.setAttributes(selectTelemetryAttributes({
						telemetry,
						attributes: {
							"ai.embeddings": { output: () => modelResponse.embeddings.map((embedding3) => JSON.stringify(embedding3)) },
							"ai.usage.tokens": usage2.tokens
						}
					}));
					return {
						embedding: embedding2,
						usage: usage2,
						rawResponse: modelResponse.rawResponse
					};
				}
			}));
			span.setAttributes(selectTelemetryAttributes({
				telemetry,
				attributes: {
					"ai.embedding": { output: () => JSON.stringify(embedding) },
					"ai.usage.tokens": usage.tokens
				}
			}));
			return new DefaultEmbedResult({
				value,
				embedding,
				usage,
				rawResponse
			});
		}
	});
}
var DefaultEmbedResult = class {
	constructor(options) {
		this.value = options.value;
		this.embedding = options.embedding;
		this.usage = options.usage;
		this.rawResponse = options.rawResponse;
	}
};
function splitArray(array, chunkSize) {
	if (chunkSize <= 0) throw new Error("chunkSize must be greater than 0");
	const result = [];
	for (let i = 0; i < array.length; i += chunkSize) result.push(array.slice(i, i + chunkSize));
	return result;
}
async function embedMany({ model, values, maxRetries: maxRetriesArg, abortSignal, headers, experimental_telemetry: telemetry }) {
	const { maxRetries, retry } = prepareRetries({ maxRetries: maxRetriesArg });
	const baseTelemetryAttributes = getBaseTelemetryAttributes({
		model,
		telemetry,
		headers,
		settings: { maxRetries }
	});
	const tracer = getTracer(telemetry);
	return recordSpan({
		name: "ai.embedMany",
		attributes: selectTelemetryAttributes({
			telemetry,
			attributes: {
				...assembleOperationName({
					operationId: "ai.embedMany",
					telemetry
				}),
				...baseTelemetryAttributes,
				"ai.values": { input: () => values.map((value) => JSON.stringify(value)) }
			}
		}),
		tracer,
		fn: async (span) => {
			const maxEmbeddingsPerCall = model.maxEmbeddingsPerCall;
			if (maxEmbeddingsPerCall == null) {
				const { embeddings: embeddings2, usage } = await retry(() => {
					return recordSpan({
						name: "ai.embedMany.doEmbed",
						attributes: selectTelemetryAttributes({
							telemetry,
							attributes: {
								...assembleOperationName({
									operationId: "ai.embedMany.doEmbed",
									telemetry
								}),
								...baseTelemetryAttributes,
								"ai.values": { input: () => values.map((value) => JSON.stringify(value)) }
							}
						}),
						tracer,
						fn: async (doEmbedSpan) => {
							var _a17;
							const modelResponse = await model.doEmbed({
								values,
								abortSignal,
								headers
							});
							const embeddings3 = modelResponse.embeddings;
							const usage2 = (_a17 = modelResponse.usage) != null ? _a17 : { tokens: NaN };
							doEmbedSpan.setAttributes(selectTelemetryAttributes({
								telemetry,
								attributes: {
									"ai.embeddings": { output: () => embeddings3.map((embedding) => JSON.stringify(embedding)) },
									"ai.usage.tokens": usage2.tokens
								}
							}));
							return {
								embeddings: embeddings3,
								usage: usage2
							};
						}
					});
				});
				span.setAttributes(selectTelemetryAttributes({
					telemetry,
					attributes: {
						"ai.embeddings": { output: () => embeddings2.map((embedding) => JSON.stringify(embedding)) },
						"ai.usage.tokens": usage.tokens
					}
				}));
				return new DefaultEmbedManyResult({
					values,
					embeddings: embeddings2,
					usage
				});
			}
			const valueChunks = splitArray(values, maxEmbeddingsPerCall);
			const embeddings = [];
			let tokens = 0;
			for (const chunk of valueChunks) {
				const { embeddings: responseEmbeddings, usage } = await retry(() => {
					return recordSpan({
						name: "ai.embedMany.doEmbed",
						attributes: selectTelemetryAttributes({
							telemetry,
							attributes: {
								...assembleOperationName({
									operationId: "ai.embedMany.doEmbed",
									telemetry
								}),
								...baseTelemetryAttributes,
								"ai.values": { input: () => chunk.map((value) => JSON.stringify(value)) }
							}
						}),
						tracer,
						fn: async (doEmbedSpan) => {
							var _a17;
							const modelResponse = await model.doEmbed({
								values: chunk,
								abortSignal,
								headers
							});
							const embeddings2 = modelResponse.embeddings;
							const usage2 = (_a17 = modelResponse.usage) != null ? _a17 : { tokens: NaN };
							doEmbedSpan.setAttributes(selectTelemetryAttributes({
								telemetry,
								attributes: {
									"ai.embeddings": { output: () => embeddings2.map((embedding) => JSON.stringify(embedding)) },
									"ai.usage.tokens": usage2.tokens
								}
							}));
							return {
								embeddings: embeddings2,
								usage: usage2
							};
						}
					});
				});
				embeddings.push(...responseEmbeddings);
				tokens += usage.tokens;
			}
			span.setAttributes(selectTelemetryAttributes({
				telemetry,
				attributes: {
					"ai.embeddings": { output: () => embeddings.map((embedding) => JSON.stringify(embedding)) },
					"ai.usage.tokens": tokens
				}
			}));
			return new DefaultEmbedManyResult({
				values,
				embeddings,
				usage: { tokens }
			});
		}
	});
}
var DefaultEmbedManyResult = class {
	constructor(options) {
		this.values = options.values;
		this.embeddings = options.embeddings;
		this.usage = options.usage;
	}
};
var name3 = "AI_NoImageGeneratedError";
var marker3 = `vercel.ai.error.${name3}`;
var symbol3 = Symbol.for(marker3);
var _a3;
var NoImageGeneratedError = class extends AISDKError {
	constructor({ message = "No image generated.", cause, responses }) {
		super({
			name: name3,
			message,
			cause
		});
		this[_a3] = true;
		this.responses = responses;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker3);
	}
};
_a3 = symbol3;
var DefaultGeneratedFile = class {
	constructor({ data, mimeType }) {
		const isUint8Array = data instanceof Uint8Array;
		this.base64Data = isUint8Array ? void 0 : data;
		this.uint8ArrayData = isUint8Array ? data : void 0;
		this.mimeType = mimeType;
	}
	get base64() {
		if (this.base64Data == null) this.base64Data = convertUint8ArrayToBase64(this.uint8ArrayData);
		return this.base64Data;
	}
	get uint8Array() {
		if (this.uint8ArrayData == null) this.uint8ArrayData = convertBase64ToUint8Array(this.base64Data);
		return this.uint8ArrayData;
	}
};
var DefaultGeneratedFileWithType = class extends DefaultGeneratedFile {
	constructor(options) {
		super(options);
		this.type = "file";
	}
};
var imageMimeTypeSignatures = [
	{
		mimeType: "image/gif",
		bytesPrefix: [
			71,
			73,
			70
		],
		base64Prefix: "R0lG"
	},
	{
		mimeType: "image/png",
		bytesPrefix: [
			137,
			80,
			78,
			71
		],
		base64Prefix: "iVBORw"
	},
	{
		mimeType: "image/jpeg",
		bytesPrefix: [255, 216],
		base64Prefix: "/9j/"
	},
	{
		mimeType: "image/webp",
		bytesPrefix: [
			82,
			73,
			70,
			70
		],
		base64Prefix: "UklGRg"
	},
	{
		mimeType: "image/bmp",
		bytesPrefix: [66, 77],
		base64Prefix: "Qk"
	},
	{
		mimeType: "image/tiff",
		bytesPrefix: [
			73,
			73,
			42,
			0
		],
		base64Prefix: "SUkqAA"
	},
	{
		mimeType: "image/tiff",
		bytesPrefix: [
			77,
			77,
			0,
			42
		],
		base64Prefix: "TU0AKg"
	},
	{
		mimeType: "image/avif",
		bytesPrefix: [
			0,
			0,
			0,
			32,
			102,
			116,
			121,
			112,
			97,
			118,
			105,
			102
		],
		base64Prefix: "AAAAIGZ0eXBhdmlm"
	},
	{
		mimeType: "image/heic",
		bytesPrefix: [
			0,
			0,
			0,
			32,
			102,
			116,
			121,
			112,
			104,
			101,
			105,
			99
		],
		base64Prefix: "AAAAIGZ0eXBoZWlj"
	}
];
var audioMimeTypeSignatures = [
	{
		mimeType: "audio/mpeg",
		bytesPrefix: [255, 251],
		base64Prefix: "//s="
	},
	{
		mimeType: "audio/wav",
		bytesPrefix: [
			82,
			73,
			70,
			70
		],
		base64Prefix: "UklGR"
	},
	{
		mimeType: "audio/ogg",
		bytesPrefix: [
			79,
			103,
			103,
			83
		],
		base64Prefix: "T2dnUw"
	},
	{
		mimeType: "audio/flac",
		bytesPrefix: [
			102,
			76,
			97,
			67
		],
		base64Prefix: "ZkxhQw"
	},
	{
		mimeType: "audio/aac",
		bytesPrefix: [
			64,
			21,
			0,
			0
		],
		base64Prefix: "QBUA"
	},
	{
		mimeType: "audio/mp4",
		bytesPrefix: [
			102,
			116,
			121,
			112
		],
		base64Prefix: "ZnR5cA"
	}
];
var stripID3 = (data) => {
	const bytes = typeof data === "string" ? convertBase64ToUint8Array(data) : data;
	const id3Size = (bytes[6] & 127) << 21 | (bytes[7] & 127) << 14 | (bytes[8] & 127) << 7 | bytes[9] & 127;
	return bytes.slice(id3Size + 10);
};
function stripID3TagsIfPresent(data) {
	const hasId3 = typeof data === "string" && data.startsWith("SUQz") || typeof data !== "string" && data.length > 10 && data[0] === 73 && data[1] === 68 && data[2] === 51;
	return hasId3 ? stripID3(data) : data;
}
function detectMimeType({ data, signatures }) {
	const processedData = stripID3TagsIfPresent(data);
	for (const signature of signatures) if (typeof processedData === "string" ? processedData.startsWith(signature.base64Prefix) : processedData.length >= signature.bytesPrefix.length && signature.bytesPrefix.every((byte, index) => processedData[index] === byte)) return signature.mimeType;
	return void 0;
}
async function generateImage({ model, prompt, n = 1, size, aspectRatio, seed, providerOptions, maxRetries: maxRetriesArg, abortSignal, headers }) {
	var _a17;
	const { retry } = prepareRetries({ maxRetries: maxRetriesArg });
	const maxImagesPerCall = (_a17 = model.maxImagesPerCall) != null ? _a17 : 1;
	const callCount = Math.ceil(n / maxImagesPerCall);
	const callImageCounts = Array.from({ length: callCount }, (_, i) => {
		if (i < callCount - 1) return maxImagesPerCall;
		const remainder = n % maxImagesPerCall;
		return remainder === 0 ? maxImagesPerCall : remainder;
	});
	const results = await Promise.all(callImageCounts.map(async (callImageCount) => retry(() => model.doGenerate({
		prompt,
		n: callImageCount,
		abortSignal,
		headers,
		size,
		aspectRatio,
		seed,
		providerOptions: providerOptions != null ? providerOptions : {}
	}))));
	const images = [];
	const warnings = [];
	const responses = [];
	for (const result of results) {
		images.push(...result.images.map((image) => {
			var _a18;
			return new DefaultGeneratedFile({
				data: image,
				mimeType: (_a18 = detectMimeType({
					data: image,
					signatures: imageMimeTypeSignatures
				})) != null ? _a18 : "image/png"
			});
		}));
		warnings.push(...result.warnings);
		responses.push(result.response);
	}
	if (!images.length) throw new NoImageGeneratedError({ responses });
	return new DefaultGenerateImageResult({
		images,
		warnings,
		responses
	});
}
var DefaultGenerateImageResult = class {
	constructor(options) {
		this.images = options.images;
		this.warnings = options.warnings;
		this.responses = options.responses;
	}
	get image() {
		return this.images[0];
	}
};
var name4 = "AI_NoObjectGeneratedError";
var marker4 = `vercel.ai.error.${name4}`;
var symbol4 = Symbol.for(marker4);
var _a4;
var NoObjectGeneratedError = class extends AISDKError {
	constructor({ message = "No object generated.", cause, text: text2, response, usage, finishReason }) {
		super({
			name: name4,
			message,
			cause
		});
		this[_a4] = true;
		this.text = text2;
		this.response = response;
		this.usage = usage;
		this.finishReason = finishReason;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker4);
	}
};
_a4 = symbol4;
var name5 = "AI_DownloadError";
var marker5 = `vercel.ai.error.${name5}`;
var symbol5 = Symbol.for(marker5);
var _a5;
var DownloadError = class extends AISDKError {
	constructor({ url, statusCode, statusText, cause, message = cause == null ? `Failed to download ${url}: ${statusCode} ${statusText}` : `Failed to download ${url}: ${cause}` }) {
		super({
			name: name5,
			message,
			cause
		});
		this[_a5] = true;
		this.url = url;
		this.statusCode = statusCode;
		this.statusText = statusText;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker5);
	}
};
_a5 = symbol5;
async function download({ url }) {
	var _a17;
	const urlText = url.toString();
	try {
		const response = await fetch(urlText);
		if (!response.ok) throw new DownloadError({
			url: urlText,
			statusCode: response.status,
			statusText: response.statusText
		});
		return {
			data: new Uint8Array(await response.arrayBuffer()),
			mimeType: (_a17 = response.headers.get("content-type")) != null ? _a17 : void 0
		};
	} catch (error) {
		if (DownloadError.isInstance(error)) throw error;
		throw new DownloadError({
			url: urlText,
			cause: error
		});
	}
}
var name6 = "AI_InvalidDataContentError";
var marker6 = `vercel.ai.error.${name6}`;
var symbol6 = Symbol.for(marker6);
var _a6;
var InvalidDataContentError = class extends AISDKError {
	constructor({ content, cause, message = `Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof content}.` }) {
		super({
			name: name6,
			message,
			cause
		});
		this[_a6] = true;
		this.content = content;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker6);
	}
};
_a6 = symbol6;
var dataContentSchema = unionType([
	stringType(),
	instanceOfType(Uint8Array),
	instanceOfType(ArrayBuffer),
	custom((value) => {
		var _a17, _b;
		return (_b = (_a17 = globalThis.Buffer) == null ? void 0 : _a17.isBuffer(value)) != null ? _b : false;
	}, { message: "Must be a Buffer" })
]);
function convertDataContentToBase64String(content) {
	if (typeof content === "string") return content;
	if (content instanceof ArrayBuffer) return convertUint8ArrayToBase64(new Uint8Array(content));
	return convertUint8ArrayToBase64(content);
}
function convertDataContentToUint8Array(content) {
	if (content instanceof Uint8Array) return content;
	if (typeof content === "string") try {
		return convertBase64ToUint8Array(content);
	} catch (error) {
		throw new InvalidDataContentError({
			message: "Invalid data content. Content string is not a base64-encoded media.",
			content,
			cause: error
		});
	}
	if (content instanceof ArrayBuffer) return new Uint8Array(content);
	throw new InvalidDataContentError({ content });
}
function convertUint8ArrayToText(uint8Array) {
	try {
		return new TextDecoder().decode(uint8Array);
	} catch (error) {
		throw new Error("Error decoding Uint8Array to text");
	}
}
var name7 = "AI_InvalidMessageRoleError";
var marker7 = `vercel.ai.error.${name7}`;
var symbol7 = Symbol.for(marker7);
var _a7;
var InvalidMessageRoleError = class extends AISDKError {
	constructor({ role, message = `Invalid message role: '${role}'. Must be one of: "system", "user", "assistant", "tool".` }) {
		super({
			name: name7,
			message
		});
		this[_a7] = true;
		this.role = role;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker7);
	}
};
_a7 = symbol7;
function splitDataUrl(dataUrl) {
	try {
		const [header, base64Content] = dataUrl.split(",");
		return {
			mimeType: header.split(";")[0].split(":")[1],
			base64Content
		};
	} catch (error) {
		return {
			mimeType: void 0,
			base64Content: void 0
		};
	}
}
async function convertToLanguageModelPrompt({ prompt, modelSupportsImageUrls = true, modelSupportsUrl = () => false, downloadImplementation = download }) {
	const downloadedAssets = await downloadAssets(prompt.messages, downloadImplementation, modelSupportsImageUrls, modelSupportsUrl);
	return [...prompt.system != null ? [{
		role: "system",
		content: prompt.system
	}] : [], ...prompt.messages.map((message) => convertToLanguageModelMessage(message, downloadedAssets))];
}
function convertToLanguageModelMessage(message, downloadedAssets) {
	var _a17, _b, _c, _d, _e, _f;
	const role = message.role;
	switch (role) {
		case "system": return {
			role: "system",
			content: message.content,
			providerMetadata: (_a17 = message.providerOptions) != null ? _a17 : message.experimental_providerMetadata
		};
		case "user": {
			if (typeof message.content === "string") return {
				role: "user",
				content: [{
					type: "text",
					text: message.content
				}],
				providerMetadata: (_b = message.providerOptions) != null ? _b : message.experimental_providerMetadata
			};
			return {
				role: "user",
				content: message.content.map((part) => convertPartToLanguageModelPart(part, downloadedAssets)).filter((part) => part.type !== "text" || part.text !== ""),
				providerMetadata: (_c = message.providerOptions) != null ? _c : message.experimental_providerMetadata
			};
		}
		case "assistant": {
			if (typeof message.content === "string") return {
				role: "assistant",
				content: [{
					type: "text",
					text: message.content
				}],
				providerMetadata: (_d = message.providerOptions) != null ? _d : message.experimental_providerMetadata
			};
			return {
				role: "assistant",
				content: message.content.filter((part) => part.type !== "text" || part.text !== "").map((part) => {
					var _a18;
					const providerOptions = (_a18 = part.providerOptions) != null ? _a18 : part.experimental_providerMetadata;
					switch (part.type) {
						case "file": return {
							type: "file",
							data: part.data instanceof URL ? part.data : convertDataContentToBase64String(part.data),
							filename: part.filename,
							mimeType: part.mimeType,
							providerMetadata: providerOptions
						};
						case "reasoning": return {
							type: "reasoning",
							text: part.text,
							signature: part.signature,
							providerMetadata: providerOptions
						};
						case "redacted-reasoning": return {
							type: "redacted-reasoning",
							data: part.data,
							providerMetadata: providerOptions
						};
						case "text": return {
							type: "text",
							text: part.text,
							providerMetadata: providerOptions
						};
						case "tool-call": return {
							type: "tool-call",
							toolCallId: part.toolCallId,
							toolName: part.toolName,
							args: part.args,
							providerMetadata: providerOptions
						};
					}
				}),
				providerMetadata: (_e = message.providerOptions) != null ? _e : message.experimental_providerMetadata
			};
		}
		case "tool": return {
			role: "tool",
			content: message.content.map((part) => {
				var _a18;
				return {
					type: "tool-result",
					toolCallId: part.toolCallId,
					toolName: part.toolName,
					result: part.result,
					content: part.experimental_content,
					isError: part.isError,
					providerMetadata: (_a18 = part.providerOptions) != null ? _a18 : part.experimental_providerMetadata
				};
			}),
			providerMetadata: (_f = message.providerOptions) != null ? _f : message.experimental_providerMetadata
		};
		default: {
			const _exhaustiveCheck = role;
			throw new InvalidMessageRoleError({ role: _exhaustiveCheck });
		}
	}
}
async function downloadAssets(messages, downloadImplementation, modelSupportsImageUrls, modelSupportsUrl) {
	const urls = messages.filter((message) => message.role === "user").map((message) => message.content).filter((content) => Array.isArray(content)).flat().filter((part) => part.type === "image" || part.type === "file").filter((part) => !(part.type === "image" && modelSupportsImageUrls === true)).map((part) => part.type === "image" ? part.image : part.data).map((part) => typeof part === "string" && (part.startsWith("http:") || part.startsWith("https:")) ? new URL(part) : part).filter((image) => image instanceof URL).filter((url) => !modelSupportsUrl(url));
	const downloadedImages = await Promise.all(urls.map(async (url) => ({
		url,
		data: await downloadImplementation({ url })
	})));
	return Object.fromEntries(downloadedImages.map(({ url, data }) => [url.toString(), data]));
}
function convertPartToLanguageModelPart(part, downloadedAssets) {
	var _a17, _b, _c, _d;
	if (part.type === "text") return {
		type: "text",
		text: part.text,
		providerMetadata: (_a17 = part.providerOptions) != null ? _a17 : part.experimental_providerMetadata
	};
	let mimeType = part.mimeType;
	let data;
	let content;
	let normalizedData;
	const type = part.type;
	switch (type) {
		case "image":
			data = part.image;
			break;
		case "file":
			data = part.data;
			break;
		default: throw new Error(`Unsupported part type: ${type}`);
	}
	try {
		content = typeof data === "string" ? new URL(data) : data;
	} catch (error) {
		content = data;
	}
	if (content instanceof URL) if (content.protocol === "data:") {
		const { mimeType: dataUrlMimeType, base64Content } = splitDataUrl(content.toString());
		if (dataUrlMimeType == null || base64Content == null) throw new Error(`Invalid data URL format in part ${type}`);
		mimeType = dataUrlMimeType;
		normalizedData = convertDataContentToUint8Array(base64Content);
	} else {
		const downloadedFile = downloadedAssets[content.toString()];
		if (downloadedFile) {
			normalizedData = downloadedFile.data;
			mimeType != null || (mimeType = downloadedFile.mimeType);
		} else normalizedData = content;
	}
	else normalizedData = convertDataContentToUint8Array(content);
	switch (type) {
		case "image": {
			if (normalizedData instanceof Uint8Array) mimeType = (_b = detectMimeType({
				data: normalizedData,
				signatures: imageMimeTypeSignatures
			})) != null ? _b : mimeType;
			return {
				type: "image",
				image: normalizedData,
				mimeType,
				providerMetadata: (_c = part.providerOptions) != null ? _c : part.experimental_providerMetadata
			};
		}
		case "file": {
			if (mimeType == null) throw new Error(`Mime type is missing for file part`);
			return {
				type: "file",
				data: normalizedData instanceof Uint8Array ? convertDataContentToBase64String(normalizedData) : normalizedData,
				filename: part.filename,
				mimeType,
				providerMetadata: (_d = part.providerOptions) != null ? _d : part.experimental_providerMetadata
			};
		}
	}
}
function prepareCallSettings({ maxTokens, temperature, topP, topK, presencePenalty, frequencyPenalty, stopSequences, seed }) {
	if (maxTokens != null) {
		if (!Number.isInteger(maxTokens)) throw new InvalidArgumentError$1({
			parameter: "maxTokens",
			value: maxTokens,
			message: "maxTokens must be an integer"
		});
		if (maxTokens < 1) throw new InvalidArgumentError$1({
			parameter: "maxTokens",
			value: maxTokens,
			message: "maxTokens must be >= 1"
		});
	}
	if (temperature != null) {
		if (typeof temperature !== "number") throw new InvalidArgumentError$1({
			parameter: "temperature",
			value: temperature,
			message: "temperature must be a number"
		});
	}
	if (topP != null) {
		if (typeof topP !== "number") throw new InvalidArgumentError$1({
			parameter: "topP",
			value: topP,
			message: "topP must be a number"
		});
	}
	if (topK != null) {
		if (typeof topK !== "number") throw new InvalidArgumentError$1({
			parameter: "topK",
			value: topK,
			message: "topK must be a number"
		});
	}
	if (presencePenalty != null) {
		if (typeof presencePenalty !== "number") throw new InvalidArgumentError$1({
			parameter: "presencePenalty",
			value: presencePenalty,
			message: "presencePenalty must be a number"
		});
	}
	if (frequencyPenalty != null) {
		if (typeof frequencyPenalty !== "number") throw new InvalidArgumentError$1({
			parameter: "frequencyPenalty",
			value: frequencyPenalty,
			message: "frequencyPenalty must be a number"
		});
	}
	if (seed != null) {
		if (!Number.isInteger(seed)) throw new InvalidArgumentError$1({
			parameter: "seed",
			value: seed,
			message: "seed must be an integer"
		});
	}
	return {
		maxTokens,
		temperature: temperature != null ? temperature : 0,
		topP,
		topK,
		presencePenalty,
		frequencyPenalty,
		stopSequences: stopSequences != null && stopSequences.length > 0 ? stopSequences : void 0,
		seed
	};
}
function attachmentsToParts(attachments) {
	var _a17, _b, _c;
	const parts = [];
	for (const attachment of attachments) {
		let url;
		try {
			url = new URL(attachment.url);
		} catch (error) {
			throw new Error(`Invalid URL: ${attachment.url}`);
		}
		switch (url.protocol) {
			case "http:":
			case "https:": {
				if ((_a17 = attachment.contentType) == null ? void 0 : _a17.startsWith("image/")) parts.push({
					type: "image",
					image: url
				});
				else {
					if (!attachment.contentType) throw new Error("If the attachment is not an image, it must specify a content type");
					parts.push({
						type: "file",
						data: url,
						mimeType: attachment.contentType
					});
				}
				break;
			}
			case "data:": {
				let header;
				let base64Content;
				let mimeType;
				try {
					[header, base64Content] = attachment.url.split(",");
					mimeType = header.split(";")[0].split(":")[1];
				} catch (error) {
					throw new Error(`Error processing data URL: ${attachment.url}`);
				}
				if (mimeType == null || base64Content == null) throw new Error(`Invalid data URL format: ${attachment.url}`);
				if ((_b = attachment.contentType) == null ? void 0 : _b.startsWith("image/")) parts.push({
					type: "image",
					image: convertDataContentToUint8Array(base64Content)
				});
				else if ((_c = attachment.contentType) == null ? void 0 : _c.startsWith("text/")) parts.push({
					type: "text",
					text: convertUint8ArrayToText(convertDataContentToUint8Array(base64Content))
				});
				else {
					if (!attachment.contentType) throw new Error("If the attachment is not an image or text, it must specify a content type");
					parts.push({
						type: "file",
						data: base64Content,
						mimeType: attachment.contentType
					});
				}
				break;
			}
			default: throw new Error(`Unsupported URL protocol: ${url.protocol}`);
		}
	}
	return parts;
}
var name8 = "AI_MessageConversionError";
var marker8 = `vercel.ai.error.${name8}`;
var symbol8 = Symbol.for(marker8);
var _a8;
var MessageConversionError = class extends AISDKError {
	constructor({ originalMessage, message }) {
		super({
			name: name8,
			message
		});
		this[_a8] = true;
		this.originalMessage = originalMessage;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker8);
	}
};
_a8 = symbol8;
function convertToCoreMessages(messages, options) {
	var _a17, _b;
	const tools = (_a17 = options == null ? void 0 : options.tools) != null ? _a17 : {};
	const coreMessages = [];
	for (let i = 0; i < messages.length; i++) {
		const message = messages[i];
		const isLastMessage = i === messages.length - 1;
		const { role, content, experimental_attachments } = message;
		switch (role) {
			case "system": {
				coreMessages.push({
					role: "system",
					content
				});
				break;
			}
			case "user": {
				if (message.parts == null) coreMessages.push({
					role: "user",
					content: experimental_attachments ? [{
						type: "text",
						text: content
					}, ...attachmentsToParts(experimental_attachments)] : content
				});
				else {
					const textParts = message.parts.filter((part) => part.type === "text").map((part) => ({
						type: "text",
						text: part.text
					}));
					coreMessages.push({
						role: "user",
						content: experimental_attachments ? [...textParts, ...attachmentsToParts(experimental_attachments)] : textParts
					});
				}
				break;
			}
			case "assistant": {
				if (message.parts != null) {
					let processBlock2 = function() {
						const content2 = [];
						for (const part of block) switch (part.type) {
							case "file":
							case "text": {
								content2.push(part);
								break;
							}
							case "reasoning": {
								for (const detail of part.details) switch (detail.type) {
									case "text":
										content2.push({
											type: "reasoning",
											text: detail.text,
											signature: detail.signature
										});
										break;
									case "redacted":
										content2.push({
											type: "redacted-reasoning",
											data: detail.data
										});
										break;
								}
								break;
							}
							case "tool-invocation":
								content2.push({
									type: "tool-call",
									toolCallId: part.toolInvocation.toolCallId,
									toolName: part.toolInvocation.toolName,
									args: part.toolInvocation.args
								});
								break;
							default: {
								const _exhaustiveCheck = part;
								throw new Error(`Unsupported part: ${_exhaustiveCheck}`);
							}
						}
						coreMessages.push({
							role: "assistant",
							content: content2
						});
						const stepInvocations = block.filter((part) => part.type === "tool-invocation").map((part) => part.toolInvocation);
						if (stepInvocations.length > 0) coreMessages.push({
							role: "tool",
							content: stepInvocations.map((toolInvocation) => {
								if (!("result" in toolInvocation)) throw new MessageConversionError({
									originalMessage: message,
									message: "ToolInvocation must have a result: " + JSON.stringify(toolInvocation)
								});
								const { toolCallId, toolName, result } = toolInvocation;
								const tool2 = tools[toolName];
								return (tool2 == null ? void 0 : tool2.experimental_toToolResultContent) != null ? {
									type: "tool-result",
									toolCallId,
									toolName,
									result: tool2.experimental_toToolResultContent(result),
									experimental_content: tool2.experimental_toToolResultContent(result)
								} : {
									type: "tool-result",
									toolCallId,
									toolName,
									result
								};
							})
						});
						block = [];
						blockHasToolInvocations = false;
						currentStep++;
					};
					var processBlock = processBlock2;
					let currentStep = 0;
					let blockHasToolInvocations = false;
					let block = [];
					for (const part of message.parts) switch (part.type) {
						case "text": {
							if (blockHasToolInvocations) processBlock2();
							block.push(part);
							break;
						}
						case "file":
						case "reasoning": {
							block.push(part);
							break;
						}
						case "tool-invocation": {
							if (((_b = part.toolInvocation.step) != null ? _b : 0) !== currentStep) processBlock2();
							block.push(part);
							blockHasToolInvocations = true;
							break;
						}
					}
					processBlock2();
					break;
				}
				const toolInvocations = message.toolInvocations;
				if (toolInvocations == null || toolInvocations.length === 0) {
					coreMessages.push({
						role: "assistant",
						content
					});
					break;
				}
				const maxStep = toolInvocations.reduce((max, toolInvocation) => {
					var _a18;
					return Math.max(max, (_a18 = toolInvocation.step) != null ? _a18 : 0);
				}, 0);
				for (let i2 = 0; i2 <= maxStep; i2++) {
					const stepInvocations = toolInvocations.filter((toolInvocation) => {
						var _a18;
						return ((_a18 = toolInvocation.step) != null ? _a18 : 0) === i2;
					});
					if (stepInvocations.length === 0) continue;
					coreMessages.push({
						role: "assistant",
						content: [...isLastMessage && content && i2 === 0 ? [{
							type: "text",
							text: content
						}] : [], ...stepInvocations.map(({ toolCallId, toolName, args }) => ({
							type: "tool-call",
							toolCallId,
							toolName,
							args
						}))]
					});
					coreMessages.push({
						role: "tool",
						content: stepInvocations.map((toolInvocation) => {
							if (!("result" in toolInvocation)) throw new MessageConversionError({
								originalMessage: message,
								message: "ToolInvocation must have a result: " + JSON.stringify(toolInvocation)
							});
							const { toolCallId, toolName, result } = toolInvocation;
							const tool2 = tools[toolName];
							return (tool2 == null ? void 0 : tool2.experimental_toToolResultContent) != null ? {
								type: "tool-result",
								toolCallId,
								toolName,
								result: tool2.experimental_toToolResultContent(result),
								experimental_content: tool2.experimental_toToolResultContent(result)
							} : {
								type: "tool-result",
								toolCallId,
								toolName,
								result
							};
						})
					});
				}
				if (content && !isLastMessage) coreMessages.push({
					role: "assistant",
					content
				});
				break;
			}
			case "data": break;
			default: {
				const _exhaustiveCheck = role;
				throw new MessageConversionError({
					originalMessage: message,
					message: `Unsupported role: ${_exhaustiveCheck}`
				});
			}
		}
	}
	return coreMessages;
}
var jsonValueSchema = lazyType(() => unionType([
	nullType(),
	stringType(),
	numberType(),
	booleanType(),
	recordType(stringType(), jsonValueSchema),
	arrayType(jsonValueSchema)
]));
var providerMetadataSchema = recordType(stringType(), recordType(stringType(), jsonValueSchema));
var toolResultContentSchema = arrayType(unionType([objectType({
	type: literalType("text"),
	text: stringType()
}), objectType({
	type: literalType("image"),
	data: stringType(),
	mimeType: stringType().optional()
})]));
var textPartSchema = objectType({
	type: literalType("text"),
	text: stringType(),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var imagePartSchema = objectType({
	type: literalType("image"),
	image: unionType([dataContentSchema, instanceOfType(URL)]),
	mimeType: stringType().optional(),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var filePartSchema = objectType({
	type: literalType("file"),
	data: unionType([dataContentSchema, instanceOfType(URL)]),
	filename: stringType().optional(),
	mimeType: stringType(),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var reasoningPartSchema = objectType({
	type: literalType("reasoning"),
	text: stringType(),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var redactedReasoningPartSchema = objectType({
	type: literalType("redacted-reasoning"),
	data: stringType(),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var toolCallPartSchema = objectType({
	type: literalType("tool-call"),
	toolCallId: stringType(),
	toolName: stringType(),
	args: unknownType(),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var toolResultPartSchema = objectType({
	type: literalType("tool-result"),
	toolCallId: stringType(),
	toolName: stringType(),
	result: unknownType(),
	content: toolResultContentSchema.optional(),
	isError: booleanType().optional(),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var coreSystemMessageSchema = objectType({
	role: literalType("system"),
	content: stringType(),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var coreUserMessageSchema = objectType({
	role: literalType("user"),
	content: unionType([stringType(), arrayType(unionType([
		textPartSchema,
		imagePartSchema,
		filePartSchema
	]))]),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var coreAssistantMessageSchema = objectType({
	role: literalType("assistant"),
	content: unionType([stringType(), arrayType(unionType([
		textPartSchema,
		filePartSchema,
		reasoningPartSchema,
		redactedReasoningPartSchema,
		toolCallPartSchema
	]))]),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var coreToolMessageSchema = objectType({
	role: literalType("tool"),
	content: arrayType(toolResultPartSchema),
	providerOptions: providerMetadataSchema.optional(),
	experimental_providerMetadata: providerMetadataSchema.optional()
});
var coreMessageSchema = unionType([
	coreSystemMessageSchema,
	coreUserMessageSchema,
	coreAssistantMessageSchema,
	coreToolMessageSchema
]);
function standardizePrompt({ prompt, tools }) {
	if (prompt.prompt == null && prompt.messages == null) throw new InvalidPromptError({
		prompt,
		message: "prompt or messages must be defined"
	});
	if (prompt.prompt != null && prompt.messages != null) throw new InvalidPromptError({
		prompt,
		message: "prompt and messages cannot be defined at the same time"
	});
	if (prompt.system != null && typeof prompt.system !== "string") throw new InvalidPromptError({
		prompt,
		message: "system must be a string"
	});
	if (prompt.prompt != null) {
		if (typeof prompt.prompt !== "string") throw new InvalidPromptError({
			prompt,
			message: "prompt must be a string"
		});
		return {
			type: "prompt",
			system: prompt.system,
			messages: [{
				role: "user",
				content: prompt.prompt
			}]
		};
	}
	if (prompt.messages != null) {
		const promptType = detectPromptType(prompt.messages);
		const messages = promptType === "ui-messages" ? convertToCoreMessages(prompt.messages, { tools }) : prompt.messages;
		if (messages.length === 0) throw new InvalidPromptError({
			prompt,
			message: "messages must not be empty"
		});
		const validationResult = safeValidateTypes({
			value: messages,
			schema: arrayType(coreMessageSchema)
		});
		if (!validationResult.success) throw new InvalidPromptError({
			prompt,
			message: ["message must be a CoreMessage or a UI message", `Validation error: ${validationResult.error.message}`].join("\n"),
			cause: validationResult.error
		});
		return {
			type: "messages",
			messages,
			system: prompt.system
		};
	}
	throw new Error("unreachable");
}
function detectPromptType(prompt) {
	if (!Array.isArray(prompt)) throw new InvalidPromptError({
		prompt,
		message: ["messages must be an array of CoreMessage or UIMessage", `Received non-array value: ${JSON.stringify(prompt)}`].join("\n"),
		cause: prompt
	});
	if (prompt.length === 0) return "messages";
	const characteristics = prompt.map(detectSingleMessageCharacteristics);
	if (characteristics.some((c) => c === "has-ui-specific-parts")) return "ui-messages";
	const nonMessageIndex = characteristics.findIndex((c) => c !== "has-core-specific-parts" && c !== "message");
	if (nonMessageIndex === -1) return "messages";
	throw new InvalidPromptError({
		prompt,
		message: [
			"messages must be an array of CoreMessage or UIMessage",
			`Received message of type: "${characteristics[nonMessageIndex]}" at index ${nonMessageIndex}`,
			`messages[${nonMessageIndex}]: ${JSON.stringify(prompt[nonMessageIndex])}`
		].join("\n"),
		cause: prompt
	});
}
function detectSingleMessageCharacteristics(message) {
	if (typeof message === "object" && message !== null && (message.role === "function" || message.role === "data" || "toolInvocations" in message || "parts" in message || "experimental_attachments" in message)) return "has-ui-specific-parts";
	else if (typeof message === "object" && message !== null && "content" in message && (Array.isArray(message.content) || "experimental_providerMetadata" in message || "providerOptions" in message)) return "has-core-specific-parts";
	else if (typeof message === "object" && message !== null && "role" in message && "content" in message && typeof message.content === "string" && [
		"system",
		"user",
		"assistant",
		"tool"
	].includes(message.role)) return "message";
	else return "other";
}
function calculateLanguageModelUsage({ promptTokens, completionTokens }) {
	return {
		promptTokens,
		completionTokens,
		totalTokens: promptTokens + completionTokens
	};
}
function addLanguageModelUsage(usage1, usage2) {
	return {
		promptTokens: usage1.promptTokens + usage2.promptTokens,
		completionTokens: usage1.completionTokens + usage2.completionTokens,
		totalTokens: usage1.totalTokens + usage2.totalTokens
	};
}
var DEFAULT_SCHEMA_PREFIX = "JSON schema:";
var DEFAULT_SCHEMA_SUFFIX = "You MUST answer with a JSON object that matches the JSON schema above.";
var DEFAULT_GENERIC_SUFFIX = "You MUST answer with JSON.";
function injectJsonInstruction({ prompt, schema, schemaPrefix = schema != null ? DEFAULT_SCHEMA_PREFIX : void 0, schemaSuffix = schema != null ? DEFAULT_SCHEMA_SUFFIX : DEFAULT_GENERIC_SUFFIX }) {
	return [
		prompt != null && prompt.length > 0 ? prompt : void 0,
		prompt != null && prompt.length > 0 ? "" : void 0,
		schemaPrefix,
		schema != null ? JSON.stringify(schema) : void 0,
		schemaSuffix
	].filter((line) => line != null).join("\n");
}
function createAsyncIterableStream(source) {
	const stream = source.pipeThrough(new TransformStream());
	stream[Symbol.asyncIterator] = () => {
		const reader = stream.getReader();
		return { async next() {
			const { done, value } = await reader.read();
			return done ? {
				done: true,
				value: void 0
			} : {
				done: false,
				value
			};
		} };
	};
	return stream;
}
var noSchemaOutputStrategy = {
	type: "no-schema",
	jsonSchema: void 0,
	validatePartialResult({ value, textDelta }) {
		return {
			success: true,
			value: {
				partial: value,
				textDelta
			}
		};
	},
	validateFinalResult(value, context) {
		return value === void 0 ? {
			success: false,
			error: new NoObjectGeneratedError({
				message: "No object generated: response did not match schema.",
				text: context.text,
				response: context.response,
				usage: context.usage,
				finishReason: context.finishReason
			})
		} : {
			success: true,
			value
		};
	},
	createElementStream() {
		throw new UnsupportedFunctionalityError({ functionality: "element streams in no-schema mode" });
	}
};
var objectOutputStrategy = (schema) => ({
	type: "object",
	jsonSchema: schema.jsonSchema,
	validatePartialResult({ value, textDelta }) {
		return {
			success: true,
			value: {
				partial: value,
				textDelta
			}
		};
	},
	validateFinalResult(value) {
		return safeValidateTypes({
			value,
			schema
		});
	},
	createElementStream() {
		throw new UnsupportedFunctionalityError({ functionality: "element streams in object mode" });
	}
});
var arrayOutputStrategy = (schema) => {
	const { $schema,...itemSchema } = schema.jsonSchema;
	return {
		type: "enum",
		jsonSchema: {
			$schema: "http://json-schema.org/draft-07/schema#",
			type: "object",
			properties: { elements: {
				type: "array",
				items: itemSchema
			} },
			required: ["elements"],
			additionalProperties: false
		},
		validatePartialResult({ value, latestObject, isFirstDelta, isFinalDelta }) {
			var _a17;
			if (!isJSONObject(value) || !isJSONArray(value.elements)) return {
				success: false,
				error: new TypeValidationError({
					value,
					cause: "value must be an object that contains an array of elements"
				})
			};
			const inputArray = value.elements;
			const resultArray = [];
			for (let i = 0; i < inputArray.length; i++) {
				const element = inputArray[i];
				const result = safeValidateTypes({
					value: element,
					schema
				});
				if (i === inputArray.length - 1 && !isFinalDelta) continue;
				if (!result.success) return result;
				resultArray.push(result.value);
			}
			const publishedElementCount = (_a17 = latestObject == null ? void 0 : latestObject.length) != null ? _a17 : 0;
			let textDelta = "";
			if (isFirstDelta) textDelta += "[";
			if (publishedElementCount > 0) textDelta += ",";
			textDelta += resultArray.slice(publishedElementCount).map((element) => JSON.stringify(element)).join(",");
			if (isFinalDelta) textDelta += "]";
			return {
				success: true,
				value: {
					partial: resultArray,
					textDelta
				}
			};
		},
		validateFinalResult(value) {
			if (!isJSONObject(value) || !isJSONArray(value.elements)) return {
				success: false,
				error: new TypeValidationError({
					value,
					cause: "value must be an object that contains an array of elements"
				})
			};
			const inputArray = value.elements;
			for (const element of inputArray) {
				const result = safeValidateTypes({
					value: element,
					schema
				});
				if (!result.success) return result;
			}
			return {
				success: true,
				value: inputArray
			};
		},
		createElementStream(originalStream) {
			let publishedElements = 0;
			return createAsyncIterableStream(originalStream.pipeThrough(new TransformStream({ transform(chunk, controller) {
				switch (chunk.type) {
					case "object": {
						const array = chunk.object;
						for (; publishedElements < array.length; publishedElements++) controller.enqueue(array[publishedElements]);
						break;
					}
					case "text-delta":
					case "finish":
					case "error": break;
					default: {
						const _exhaustiveCheck = chunk;
						throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);
					}
				}
			} })));
		}
	};
};
var enumOutputStrategy = (enumValues) => {
	return {
		type: "enum",
		jsonSchema: {
			$schema: "http://json-schema.org/draft-07/schema#",
			type: "object",
			properties: { result: {
				type: "string",
				enum: enumValues
			} },
			required: ["result"],
			additionalProperties: false
		},
		validateFinalResult(value) {
			if (!isJSONObject(value) || typeof value.result !== "string") return {
				success: false,
				error: new TypeValidationError({
					value,
					cause: "value must be an object that contains a string in the \"result\" property."
				})
			};
			const result = value.result;
			return enumValues.includes(result) ? {
				success: true,
				value: result
			} : {
				success: false,
				error: new TypeValidationError({
					value,
					cause: "value must be a string in the enum"
				})
			};
		},
		validatePartialResult() {
			throw new UnsupportedFunctionalityError({ functionality: "partial results in enum mode" });
		},
		createElementStream() {
			throw new UnsupportedFunctionalityError({ functionality: "element streams in enum mode" });
		}
	};
};
function getOutputStrategy({ output, schema, enumValues }) {
	switch (output) {
		case "object": return objectOutputStrategy(asSchema(schema));
		case "array": return arrayOutputStrategy(asSchema(schema));
		case "enum": return enumOutputStrategy(enumValues);
		case "no-schema": return noSchemaOutputStrategy;
		default: {
			const _exhaustiveCheck = output;
			throw new Error(`Unsupported output: ${_exhaustiveCheck}`);
		}
	}
}
function validateObjectGenerationInput({ output, mode, schema, schemaName, schemaDescription, enumValues }) {
	if (output != null && output !== "object" && output !== "array" && output !== "enum" && output !== "no-schema") throw new InvalidArgumentError$1({
		parameter: "output",
		value: output,
		message: "Invalid output type."
	});
	if (output === "no-schema") {
		if (mode === "auto" || mode === "tool") throw new InvalidArgumentError$1({
			parameter: "mode",
			value: mode,
			message: "Mode must be \"json\" for no-schema output."
		});
		if (schema != null) throw new InvalidArgumentError$1({
			parameter: "schema",
			value: schema,
			message: "Schema is not supported for no-schema output."
		});
		if (schemaDescription != null) throw new InvalidArgumentError$1({
			parameter: "schemaDescription",
			value: schemaDescription,
			message: "Schema description is not supported for no-schema output."
		});
		if (schemaName != null) throw new InvalidArgumentError$1({
			parameter: "schemaName",
			value: schemaName,
			message: "Schema name is not supported for no-schema output."
		});
		if (enumValues != null) throw new InvalidArgumentError$1({
			parameter: "enumValues",
			value: enumValues,
			message: "Enum values are not supported for no-schema output."
		});
	}
	if (output === "object") {
		if (schema == null) throw new InvalidArgumentError$1({
			parameter: "schema",
			value: schema,
			message: "Schema is required for object output."
		});
		if (enumValues != null) throw new InvalidArgumentError$1({
			parameter: "enumValues",
			value: enumValues,
			message: "Enum values are not supported for object output."
		});
	}
	if (output === "array") {
		if (schema == null) throw new InvalidArgumentError$1({
			parameter: "schema",
			value: schema,
			message: "Element schema is required for array output."
		});
		if (enumValues != null) throw new InvalidArgumentError$1({
			parameter: "enumValues",
			value: enumValues,
			message: "Enum values are not supported for array output."
		});
	}
	if (output === "enum") {
		if (schema != null) throw new InvalidArgumentError$1({
			parameter: "schema",
			value: schema,
			message: "Schema is not supported for enum output."
		});
		if (schemaDescription != null) throw new InvalidArgumentError$1({
			parameter: "schemaDescription",
			value: schemaDescription,
			message: "Schema description is not supported for enum output."
		});
		if (schemaName != null) throw new InvalidArgumentError$1({
			parameter: "schemaName",
			value: schemaName,
			message: "Schema name is not supported for enum output."
		});
		if (enumValues == null) throw new InvalidArgumentError$1({
			parameter: "enumValues",
			value: enumValues,
			message: "Enum values are required for enum output."
		});
		for (const value of enumValues) if (typeof value !== "string") throw new InvalidArgumentError$1({
			parameter: "enumValues",
			value,
			message: "Enum values must be strings."
		});
	}
}
function stringifyForTelemetry(prompt) {
	const processedPrompt = prompt.map((message) => {
		return {
			...message,
			content: typeof message.content === "string" ? message.content : message.content.map(processPart)
		};
	});
	return JSON.stringify(processedPrompt);
}
function processPart(part) {
	if (part.type === "image") return {
		...part,
		image: part.image instanceof Uint8Array ? convertDataContentToBase64String(part.image) : part.image
	};
	return part;
}
var originalGenerateId = createIdGenerator({
	prefix: "aiobj",
	size: 24
});
async function generateObject({ model, enum: enumValues, schema: inputSchema, schemaName, schemaDescription, mode, output = "object", system, prompt, messages, maxRetries: maxRetriesArg, abortSignal, headers, experimental_repairText: repairText, experimental_telemetry: telemetry, experimental_providerMetadata, providerOptions = experimental_providerMetadata, _internal: { generateId: generateId3 = originalGenerateId, currentDate = () => /* @__PURE__ */ new Date() } = {},...settings }) {
	validateObjectGenerationInput({
		output,
		mode,
		schema: inputSchema,
		schemaName,
		schemaDescription,
		enumValues
	});
	const { maxRetries, retry } = prepareRetries({ maxRetries: maxRetriesArg });
	const outputStrategy = getOutputStrategy({
		output,
		schema: inputSchema,
		enumValues
	});
	if (outputStrategy.type === "no-schema" && mode === void 0) mode = "json";
	const baseTelemetryAttributes = getBaseTelemetryAttributes({
		model,
		telemetry,
		headers,
		settings: {
			...settings,
			maxRetries
		}
	});
	const tracer = getTracer(telemetry);
	return recordSpan({
		name: "ai.generateObject",
		attributes: selectTelemetryAttributes({
			telemetry,
			attributes: {
				...assembleOperationName({
					operationId: "ai.generateObject",
					telemetry
				}),
				...baseTelemetryAttributes,
				"ai.prompt": { input: () => JSON.stringify({
					system,
					prompt,
					messages
				}) },
				"ai.schema": outputStrategy.jsonSchema != null ? { input: () => JSON.stringify(outputStrategy.jsonSchema) } : void 0,
				"ai.schema.name": schemaName,
				"ai.schema.description": schemaDescription,
				"ai.settings.output": outputStrategy.type,
				"ai.settings.mode": mode
			}
		}),
		tracer,
		fn: async (span) => {
			var _a17, _b, _c, _d;
			if (mode === "auto" || mode == null) mode = model.defaultObjectGenerationMode;
			let result;
			let finishReason;
			let usage;
			let warnings;
			let rawResponse;
			let response;
			let request;
			let logprobs;
			let resultProviderMetadata;
			switch (mode) {
				case "json": {
					const standardizedPrompt = standardizePrompt({
						prompt: {
							system: outputStrategy.jsonSchema == null ? injectJsonInstruction({ prompt: system }) : model.supportsStructuredOutputs ? system : injectJsonInstruction({
								prompt: system,
								schema: outputStrategy.jsonSchema
							}),
							prompt,
							messages
						},
						tools: void 0
					});
					const promptMessages = await convertToLanguageModelPrompt({
						prompt: standardizedPrompt,
						modelSupportsImageUrls: model.supportsImageUrls,
						modelSupportsUrl: (_a17 = model.supportsUrl) == null ? void 0 : _a17.bind(model)
					});
					const generateResult = await retry(() => recordSpan({
						name: "ai.generateObject.doGenerate",
						attributes: selectTelemetryAttributes({
							telemetry,
							attributes: {
								...assembleOperationName({
									operationId: "ai.generateObject.doGenerate",
									telemetry
								}),
								...baseTelemetryAttributes,
								"ai.prompt.format": { input: () => standardizedPrompt.type },
								"ai.prompt.messages": { input: () => JSON.stringify(promptMessages) },
								"ai.settings.mode": mode,
								"gen_ai.system": model.provider,
								"gen_ai.request.model": model.modelId,
								"gen_ai.request.frequency_penalty": settings.frequencyPenalty,
								"gen_ai.request.max_tokens": settings.maxTokens,
								"gen_ai.request.presence_penalty": settings.presencePenalty,
								"gen_ai.request.temperature": settings.temperature,
								"gen_ai.request.top_k": settings.topK,
								"gen_ai.request.top_p": settings.topP
							}
						}),
						tracer,
						fn: async (span2) => {
							var _a18, _b2, _c2, _d2, _e, _f;
							const result2 = await model.doGenerate({
								mode: {
									type: "object-json",
									schema: outputStrategy.jsonSchema,
									name: schemaName,
									description: schemaDescription
								},
								...prepareCallSettings(settings),
								inputFormat: standardizedPrompt.type,
								prompt: promptMessages,
								providerMetadata: providerOptions,
								abortSignal,
								headers
							});
							const responseData = {
								id: (_b2 = (_a18 = result2.response) == null ? void 0 : _a18.id) != null ? _b2 : generateId3(),
								timestamp: (_d2 = (_c2 = result2.response) == null ? void 0 : _c2.timestamp) != null ? _d2 : currentDate(),
								modelId: (_f = (_e = result2.response) == null ? void 0 : _e.modelId) != null ? _f : model.modelId
							};
							if (result2.text === void 0) throw new NoObjectGeneratedError({
								message: "No object generated: the model did not return a response.",
								response: responseData,
								usage: calculateLanguageModelUsage(result2.usage),
								finishReason: result2.finishReason
							});
							span2.setAttributes(selectTelemetryAttributes({
								telemetry,
								attributes: {
									"ai.response.finishReason": result2.finishReason,
									"ai.response.object": { output: () => result2.text },
									"ai.response.id": responseData.id,
									"ai.response.model": responseData.modelId,
									"ai.response.timestamp": responseData.timestamp.toISOString(),
									"ai.usage.promptTokens": result2.usage.promptTokens,
									"ai.usage.completionTokens": result2.usage.completionTokens,
									"gen_ai.response.finish_reasons": [result2.finishReason],
									"gen_ai.response.id": responseData.id,
									"gen_ai.response.model": responseData.modelId,
									"gen_ai.usage.prompt_tokens": result2.usage.promptTokens,
									"gen_ai.usage.completion_tokens": result2.usage.completionTokens
								}
							}));
							return {
								...result2,
								objectText: result2.text,
								responseData
							};
						}
					}));
					result = generateResult.objectText;
					finishReason = generateResult.finishReason;
					usage = generateResult.usage;
					warnings = generateResult.warnings;
					rawResponse = generateResult.rawResponse;
					logprobs = generateResult.logprobs;
					resultProviderMetadata = generateResult.providerMetadata;
					request = (_b = generateResult.request) != null ? _b : {};
					response = generateResult.responseData;
					break;
				}
				case "tool": {
					const standardizedPrompt = standardizePrompt({
						prompt: {
							system,
							prompt,
							messages
						},
						tools: void 0
					});
					const promptMessages = await convertToLanguageModelPrompt({
						prompt: standardizedPrompt,
						modelSupportsImageUrls: model.supportsImageUrls,
						modelSupportsUrl: (_c = model.supportsUrl) == null ? void 0 : _c.bind(model)
					});
					const inputFormat = standardizedPrompt.type;
					const generateResult = await retry(() => recordSpan({
						name: "ai.generateObject.doGenerate",
						attributes: selectTelemetryAttributes({
							telemetry,
							attributes: {
								...assembleOperationName({
									operationId: "ai.generateObject.doGenerate",
									telemetry
								}),
								...baseTelemetryAttributes,
								"ai.prompt.format": { input: () => inputFormat },
								"ai.prompt.messages": { input: () => stringifyForTelemetry(promptMessages) },
								"ai.settings.mode": mode,
								"gen_ai.system": model.provider,
								"gen_ai.request.model": model.modelId,
								"gen_ai.request.frequency_penalty": settings.frequencyPenalty,
								"gen_ai.request.max_tokens": settings.maxTokens,
								"gen_ai.request.presence_penalty": settings.presencePenalty,
								"gen_ai.request.temperature": settings.temperature,
								"gen_ai.request.top_k": settings.topK,
								"gen_ai.request.top_p": settings.topP
							}
						}),
						tracer,
						fn: async (span2) => {
							var _a18, _b2, _c2, _d2, _e, _f, _g, _h;
							const result2 = await model.doGenerate({
								mode: {
									type: "object-tool",
									tool: {
										type: "function",
										name: schemaName != null ? schemaName : "json",
										description: schemaDescription != null ? schemaDescription : "Respond with a JSON object.",
										parameters: outputStrategy.jsonSchema
									}
								},
								...prepareCallSettings(settings),
								inputFormat,
								prompt: promptMessages,
								providerMetadata: providerOptions,
								abortSignal,
								headers
							});
							const objectText = (_b2 = (_a18 = result2.toolCalls) == null ? void 0 : _a18[0]) == null ? void 0 : _b2.args;
							const responseData = {
								id: (_d2 = (_c2 = result2.response) == null ? void 0 : _c2.id) != null ? _d2 : generateId3(),
								timestamp: (_f = (_e = result2.response) == null ? void 0 : _e.timestamp) != null ? _f : currentDate(),
								modelId: (_h = (_g = result2.response) == null ? void 0 : _g.modelId) != null ? _h : model.modelId
							};
							if (objectText === void 0) throw new NoObjectGeneratedError({
								message: "No object generated: the tool was not called.",
								response: responseData,
								usage: calculateLanguageModelUsage(result2.usage),
								finishReason: result2.finishReason
							});
							span2.setAttributes(selectTelemetryAttributes({
								telemetry,
								attributes: {
									"ai.response.finishReason": result2.finishReason,
									"ai.response.object": { output: () => objectText },
									"ai.response.id": responseData.id,
									"ai.response.model": responseData.modelId,
									"ai.response.timestamp": responseData.timestamp.toISOString(),
									"ai.usage.promptTokens": result2.usage.promptTokens,
									"ai.usage.completionTokens": result2.usage.completionTokens,
									"gen_ai.response.finish_reasons": [result2.finishReason],
									"gen_ai.response.id": responseData.id,
									"gen_ai.response.model": responseData.modelId,
									"gen_ai.usage.input_tokens": result2.usage.promptTokens,
									"gen_ai.usage.output_tokens": result2.usage.completionTokens
								}
							}));
							return {
								...result2,
								objectText,
								responseData
							};
						}
					}));
					result = generateResult.objectText;
					finishReason = generateResult.finishReason;
					usage = generateResult.usage;
					warnings = generateResult.warnings;
					rawResponse = generateResult.rawResponse;
					logprobs = generateResult.logprobs;
					resultProviderMetadata = generateResult.providerMetadata;
					request = (_d = generateResult.request) != null ? _d : {};
					response = generateResult.responseData;
					break;
				}
				case void 0: throw new Error("Model does not have a default object generation mode.");
				default: {
					const _exhaustiveCheck = mode;
					throw new Error(`Unsupported mode: ${_exhaustiveCheck}`);
				}
			}
			function processResult(result2) {
				const parseResult = safeParseJSON({ text: result2 });
				if (!parseResult.success) throw new NoObjectGeneratedError({
					message: "No object generated: could not parse the response.",
					cause: parseResult.error,
					text: result2,
					response,
					usage: calculateLanguageModelUsage(usage),
					finishReason
				});
				const validationResult = outputStrategy.validateFinalResult(parseResult.value, {
					text: result2,
					response,
					usage: calculateLanguageModelUsage(usage)
				});
				if (!validationResult.success) throw new NoObjectGeneratedError({
					message: "No object generated: response did not match schema.",
					cause: validationResult.error,
					text: result2,
					response,
					usage: calculateLanguageModelUsage(usage),
					finishReason
				});
				return validationResult.value;
			}
			let object2;
			try {
				object2 = processResult(result);
			} catch (error) {
				if (repairText != null && NoObjectGeneratedError.isInstance(error) && (JSONParseError.isInstance(error.cause) || TypeValidationError.isInstance(error.cause))) {
					const repairedText = await repairText({
						text: result,
						error: error.cause
					});
					if (repairedText === null) throw error;
					object2 = processResult(repairedText);
				} else throw error;
			}
			span.setAttributes(selectTelemetryAttributes({
				telemetry,
				attributes: {
					"ai.response.finishReason": finishReason,
					"ai.response.object": { output: () => JSON.stringify(object2) },
					"ai.usage.promptTokens": usage.promptTokens,
					"ai.usage.completionTokens": usage.completionTokens
				}
			}));
			return new DefaultGenerateObjectResult({
				object: object2,
				finishReason,
				usage: calculateLanguageModelUsage(usage),
				warnings,
				request,
				response: {
					...response,
					headers: rawResponse == null ? void 0 : rawResponse.headers,
					body: rawResponse == null ? void 0 : rawResponse.body
				},
				logprobs,
				providerMetadata: resultProviderMetadata
			});
		}
	});
}
var DefaultGenerateObjectResult = class {
	constructor(options) {
		this.object = options.object;
		this.finishReason = options.finishReason;
		this.usage = options.usage;
		this.warnings = options.warnings;
		this.providerMetadata = options.providerMetadata;
		this.experimental_providerMetadata = options.providerMetadata;
		this.response = options.response;
		this.request = options.request;
		this.logprobs = options.logprobs;
	}
	toJsonResponse(init) {
		var _a17;
		return new Response(JSON.stringify(this.object), {
			status: (_a17 = init == null ? void 0 : init.status) != null ? _a17 : 200,
			headers: prepareResponseHeaders(init == null ? void 0 : init.headers, { contentType: "application/json; charset=utf-8" })
		});
	}
};
var DelayedPromise = class {
	constructor() {
		this.status = { type: "pending" };
		this._resolve = void 0;
		this._reject = void 0;
	}
	get value() {
		if (this.promise) return this.promise;
		this.promise = new Promise((resolve, reject) => {
			if (this.status.type === "resolved") resolve(this.status.value);
			else if (this.status.type === "rejected") reject(this.status.error);
			this._resolve = resolve;
			this._reject = reject;
		});
		return this.promise;
	}
	resolve(value) {
		var _a17;
		this.status = {
			type: "resolved",
			value
		};
		if (this.promise) (_a17 = this._resolve) == null || _a17.call(this, value);
	}
	reject(error) {
		var _a17;
		this.status = {
			type: "rejected",
			error
		};
		if (this.promise) (_a17 = this._reject) == null || _a17.call(this, error);
	}
};
function createResolvablePromise() {
	let resolve;
	let reject;
	const promise = new Promise((res, rej) => {
		resolve = res;
		reject = rej;
	});
	return {
		promise,
		resolve,
		reject
	};
}
function createStitchableStream() {
	let innerStreamReaders = [];
	let controller = null;
	let isClosed = false;
	let waitForNewStream = createResolvablePromise();
	const processPull = async () => {
		if (isClosed && innerStreamReaders.length === 0) {
			controller == null || controller.close();
			return;
		}
		if (innerStreamReaders.length === 0) {
			waitForNewStream = createResolvablePromise();
			await waitForNewStream.promise;
			return processPull();
		}
		try {
			const { value, done } = await innerStreamReaders[0].read();
			if (done) {
				innerStreamReaders.shift();
				if (innerStreamReaders.length > 0) await processPull();
				else if (isClosed) controller?.close();
			} else controller?.enqueue(value);
		} catch (error) {
			controller == null || controller.error(error);
			innerStreamReaders.shift();
			if (isClosed && innerStreamReaders.length === 0) controller?.close();
		}
	};
	return {
		stream: new ReadableStream({
			start(controllerParam) {
				controller = controllerParam;
			},
			pull: processPull,
			async cancel() {
				for (const reader of innerStreamReaders) await reader.cancel();
				innerStreamReaders = [];
				isClosed = true;
			}
		}),
		addStream: (innerStream) => {
			if (isClosed) throw new Error("Cannot add inner stream: outer stream is closed");
			innerStreamReaders.push(innerStream.getReader());
			waitForNewStream.resolve();
		},
		close: () => {
			isClosed = true;
			waitForNewStream.resolve();
			if (innerStreamReaders.length === 0) controller?.close();
		},
		terminate: () => {
			isClosed = true;
			waitForNewStream.resolve();
			innerStreamReaders.forEach((reader) => reader.cancel());
			innerStreamReaders = [];
			controller == null || controller.close();
		}
	};
}
function now() {
	var _a17, _b;
	return (_b = (_a17 = globalThis == null ? void 0 : globalThis.performance) == null ? void 0 : _a17.now()) != null ? _b : Date.now();
}
var originalGenerateId2 = createIdGenerator({
	prefix: "aiobj",
	size: 24
});
function streamObject({ model, schema: inputSchema, schemaName, schemaDescription, mode, output = "object", system, prompt, messages, maxRetries, abortSignal, headers, experimental_telemetry: telemetry, experimental_providerMetadata, providerOptions = experimental_providerMetadata, onError, onFinish, _internal: { generateId: generateId3 = originalGenerateId2, currentDate = () => /* @__PURE__ */ new Date(), now: now2 = now } = {},...settings }) {
	validateObjectGenerationInput({
		output,
		mode,
		schema: inputSchema,
		schemaName,
		schemaDescription
	});
	const outputStrategy = getOutputStrategy({
		output,
		schema: inputSchema
	});
	if (outputStrategy.type === "no-schema" && mode === void 0) mode = "json";
	return new DefaultStreamObjectResult({
		model,
		telemetry,
		headers,
		settings,
		maxRetries,
		abortSignal,
		outputStrategy,
		system,
		prompt,
		messages,
		schemaName,
		schemaDescription,
		providerOptions,
		mode,
		onError,
		onFinish,
		generateId: generateId3,
		currentDate,
		now: now2
	});
}
var DefaultStreamObjectResult = class {
	constructor({ model, headers, telemetry, settings, maxRetries: maxRetriesArg, abortSignal, outputStrategy, system, prompt, messages, schemaName, schemaDescription, providerOptions, mode, onError, onFinish, generateId: generateId3, currentDate, now: now2 }) {
		this.objectPromise = new DelayedPromise();
		this.usagePromise = new DelayedPromise();
		this.providerMetadataPromise = new DelayedPromise();
		this.warningsPromise = new DelayedPromise();
		this.requestPromise = new DelayedPromise();
		this.responsePromise = new DelayedPromise();
		const { maxRetries, retry } = prepareRetries({ maxRetries: maxRetriesArg });
		const baseTelemetryAttributes = getBaseTelemetryAttributes({
			model,
			telemetry,
			headers,
			settings: {
				...settings,
				maxRetries
			}
		});
		const tracer = getTracer(telemetry);
		const self$1 = this;
		const stitchableStream = createStitchableStream();
		const eventProcessor = new TransformStream({ transform(chunk, controller) {
			controller.enqueue(chunk);
			if (chunk.type === "error") onError?.({ error: chunk.error });
		} });
		this.baseStream = stitchableStream.stream.pipeThrough(eventProcessor);
		recordSpan({
			name: "ai.streamObject",
			attributes: selectTelemetryAttributes({
				telemetry,
				attributes: {
					...assembleOperationName({
						operationId: "ai.streamObject",
						telemetry
					}),
					...baseTelemetryAttributes,
					"ai.prompt": { input: () => JSON.stringify({
						system,
						prompt,
						messages
					}) },
					"ai.schema": outputStrategy.jsonSchema != null ? { input: () => JSON.stringify(outputStrategy.jsonSchema) } : void 0,
					"ai.schema.name": schemaName,
					"ai.schema.description": schemaDescription,
					"ai.settings.output": outputStrategy.type,
					"ai.settings.mode": mode
				}
			}),
			tracer,
			endWhenDone: false,
			fn: async (rootSpan) => {
				var _a17, _b;
				if (mode === "auto" || mode == null) mode = model.defaultObjectGenerationMode;
				let callOptions;
				let transformer;
				switch (mode) {
					case "json": {
						const standardizedPrompt = standardizePrompt({
							prompt: {
								system: outputStrategy.jsonSchema == null ? injectJsonInstruction({ prompt: system }) : model.supportsStructuredOutputs ? system : injectJsonInstruction({
									prompt: system,
									schema: outputStrategy.jsonSchema
								}),
								prompt,
								messages
							},
							tools: void 0
						});
						callOptions = {
							mode: {
								type: "object-json",
								schema: outputStrategy.jsonSchema,
								name: schemaName,
								description: schemaDescription
							},
							...prepareCallSettings(settings),
							inputFormat: standardizedPrompt.type,
							prompt: await convertToLanguageModelPrompt({
								prompt: standardizedPrompt,
								modelSupportsImageUrls: model.supportsImageUrls,
								modelSupportsUrl: (_a17 = model.supportsUrl) == null ? void 0 : _a17.bind(model)
							}),
							providerMetadata: providerOptions,
							abortSignal,
							headers
						};
						transformer = { transform: (chunk, controller) => {
							switch (chunk.type) {
								case "text-delta":
									controller.enqueue(chunk.textDelta);
									break;
								case "response-metadata":
								case "finish":
								case "error":
									controller.enqueue(chunk);
									break;
							}
						} };
						break;
					}
					case "tool": {
						const standardizedPrompt = standardizePrompt({
							prompt: {
								system,
								prompt,
								messages
							},
							tools: void 0
						});
						callOptions = {
							mode: {
								type: "object-tool",
								tool: {
									type: "function",
									name: schemaName != null ? schemaName : "json",
									description: schemaDescription != null ? schemaDescription : "Respond with a JSON object.",
									parameters: outputStrategy.jsonSchema
								}
							},
							...prepareCallSettings(settings),
							inputFormat: standardizedPrompt.type,
							prompt: await convertToLanguageModelPrompt({
								prompt: standardizedPrompt,
								modelSupportsImageUrls: model.supportsImageUrls,
								modelSupportsUrl: (_b = model.supportsUrl) == null ? void 0 : _b.bind(model)
							}),
							providerMetadata: providerOptions,
							abortSignal,
							headers
						};
						transformer = { transform(chunk, controller) {
							switch (chunk.type) {
								case "tool-call-delta":
									controller.enqueue(chunk.argsTextDelta);
									break;
								case "response-metadata":
								case "finish":
								case "error":
									controller.enqueue(chunk);
									break;
							}
						} };
						break;
					}
					case void 0: throw new Error("Model does not have a default object generation mode.");
					default: {
						const _exhaustiveCheck = mode;
						throw new Error(`Unsupported mode: ${_exhaustiveCheck}`);
					}
				}
				const { result: { stream, warnings, rawResponse, request }, doStreamSpan, startTimestampMs } = await retry(() => recordSpan({
					name: "ai.streamObject.doStream",
					attributes: selectTelemetryAttributes({
						telemetry,
						attributes: {
							...assembleOperationName({
								operationId: "ai.streamObject.doStream",
								telemetry
							}),
							...baseTelemetryAttributes,
							"ai.prompt.format": { input: () => callOptions.inputFormat },
							"ai.prompt.messages": { input: () => stringifyForTelemetry(callOptions.prompt) },
							"ai.settings.mode": mode,
							"gen_ai.system": model.provider,
							"gen_ai.request.model": model.modelId,
							"gen_ai.request.frequency_penalty": settings.frequencyPenalty,
							"gen_ai.request.max_tokens": settings.maxTokens,
							"gen_ai.request.presence_penalty": settings.presencePenalty,
							"gen_ai.request.temperature": settings.temperature,
							"gen_ai.request.top_k": settings.topK,
							"gen_ai.request.top_p": settings.topP
						}
					}),
					tracer,
					endWhenDone: false,
					fn: async (doStreamSpan2) => ({
						startTimestampMs: now2(),
						doStreamSpan: doStreamSpan2,
						result: await model.doStream(callOptions)
					})
				}));
				self$1.requestPromise.resolve(request != null ? request : {});
				let usage;
				let finishReason;
				let providerMetadata;
				let object2;
				let error;
				let accumulatedText = "";
				let textDelta = "";
				let response = {
					id: generateId3(),
					timestamp: currentDate(),
					modelId: model.modelId
				};
				let latestObjectJson = void 0;
				let latestObject = void 0;
				let isFirstChunk = true;
				let isFirstDelta = true;
				const transformedStream = stream.pipeThrough(new TransformStream(transformer)).pipeThrough(new TransformStream({
					async transform(chunk, controller) {
						var _a18, _b2, _c;
						if (isFirstChunk) {
							const msToFirstChunk = now2() - startTimestampMs;
							isFirstChunk = false;
							doStreamSpan.addEvent("ai.stream.firstChunk", { "ai.stream.msToFirstChunk": msToFirstChunk });
							doStreamSpan.setAttributes({ "ai.stream.msToFirstChunk": msToFirstChunk });
						}
						if (typeof chunk === "string") {
							accumulatedText += chunk;
							textDelta += chunk;
							const { value: currentObjectJson, state: parseState } = parsePartialJson(accumulatedText);
							if (currentObjectJson !== void 0 && !isDeepEqualData(latestObjectJson, currentObjectJson)) {
								const validationResult = outputStrategy.validatePartialResult({
									value: currentObjectJson,
									textDelta,
									latestObject,
									isFirstDelta,
									isFinalDelta: parseState === "successful-parse"
								});
								if (validationResult.success && !isDeepEqualData(latestObject, validationResult.value.partial)) {
									latestObjectJson = currentObjectJson;
									latestObject = validationResult.value.partial;
									controller.enqueue({
										type: "object",
										object: latestObject
									});
									controller.enqueue({
										type: "text-delta",
										textDelta: validationResult.value.textDelta
									});
									textDelta = "";
									isFirstDelta = false;
								}
							}
							return;
						}
						switch (chunk.type) {
							case "response-metadata": {
								response = {
									id: (_a18 = chunk.id) != null ? _a18 : response.id,
									timestamp: (_b2 = chunk.timestamp) != null ? _b2 : response.timestamp,
									modelId: (_c = chunk.modelId) != null ? _c : response.modelId
								};
								break;
							}
							case "finish": {
								if (textDelta !== "") controller.enqueue({
									type: "text-delta",
									textDelta
								});
								finishReason = chunk.finishReason;
								usage = calculateLanguageModelUsage(chunk.usage);
								providerMetadata = chunk.providerMetadata;
								controller.enqueue({
									...chunk,
									usage,
									response
								});
								self$1.usagePromise.resolve(usage);
								self$1.providerMetadataPromise.resolve(providerMetadata);
								self$1.responsePromise.resolve({
									...response,
									headers: rawResponse == null ? void 0 : rawResponse.headers
								});
								const validationResult = outputStrategy.validateFinalResult(latestObjectJson, {
									text: accumulatedText,
									response,
									usage
								});
								if (validationResult.success) {
									object2 = validationResult.value;
									self$1.objectPromise.resolve(object2);
								} else {
									error = new NoObjectGeneratedError({
										message: "No object generated: response did not match schema.",
										cause: validationResult.error,
										text: accumulatedText,
										response,
										usage,
										finishReason
									});
									self$1.objectPromise.reject(error);
								}
								break;
							}
							default: {
								controller.enqueue(chunk);
								break;
							}
						}
					},
					async flush(controller) {
						try {
							const finalUsage = usage != null ? usage : {
								promptTokens: NaN,
								completionTokens: NaN,
								totalTokens: NaN
							};
							doStreamSpan.setAttributes(selectTelemetryAttributes({
								telemetry,
								attributes: {
									"ai.response.finishReason": finishReason,
									"ai.response.object": { output: () => JSON.stringify(object2) },
									"ai.response.id": response.id,
									"ai.response.model": response.modelId,
									"ai.response.timestamp": response.timestamp.toISOString(),
									"ai.usage.promptTokens": finalUsage.promptTokens,
									"ai.usage.completionTokens": finalUsage.completionTokens,
									"gen_ai.response.finish_reasons": [finishReason],
									"gen_ai.response.id": response.id,
									"gen_ai.response.model": response.modelId,
									"gen_ai.usage.input_tokens": finalUsage.promptTokens,
									"gen_ai.usage.output_tokens": finalUsage.completionTokens
								}
							}));
							doStreamSpan.end();
							rootSpan.setAttributes(selectTelemetryAttributes({
								telemetry,
								attributes: {
									"ai.usage.promptTokens": finalUsage.promptTokens,
									"ai.usage.completionTokens": finalUsage.completionTokens,
									"ai.response.object": { output: () => JSON.stringify(object2) }
								}
							}));
							await (onFinish == null ? void 0 : onFinish({
								usage: finalUsage,
								object: object2,
								error,
								response: {
									...response,
									headers: rawResponse == null ? void 0 : rawResponse.headers
								},
								warnings,
								providerMetadata,
								experimental_providerMetadata: providerMetadata
							}));
						} catch (error2) {
							controller.enqueue({
								type: "error",
								error: error2
							});
						} finally {
							rootSpan.end();
						}
					}
				}));
				stitchableStream.addStream(transformedStream);
			}
		}).catch((error) => {
			stitchableStream.addStream(new ReadableStream({ start(controller) {
				controller.enqueue({
					type: "error",
					error
				});
				controller.close();
			} }));
		}).finally(() => {
			stitchableStream.close();
		});
		this.outputStrategy = outputStrategy;
	}
	get object() {
		return this.objectPromise.value;
	}
	get usage() {
		return this.usagePromise.value;
	}
	get experimental_providerMetadata() {
		return this.providerMetadataPromise.value;
	}
	get providerMetadata() {
		return this.providerMetadataPromise.value;
	}
	get warnings() {
		return this.warningsPromise.value;
	}
	get request() {
		return this.requestPromise.value;
	}
	get response() {
		return this.responsePromise.value;
	}
	get partialObjectStream() {
		return createAsyncIterableStream(this.baseStream.pipeThrough(new TransformStream({ transform(chunk, controller) {
			switch (chunk.type) {
				case "object":
					controller.enqueue(chunk.object);
					break;
				case "text-delta":
				case "finish":
				case "error": break;
				default: {
					const _exhaustiveCheck = chunk;
					throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);
				}
			}
		} })));
	}
	get elementStream() {
		return this.outputStrategy.createElementStream(this.baseStream);
	}
	get textStream() {
		return createAsyncIterableStream(this.baseStream.pipeThrough(new TransformStream({ transform(chunk, controller) {
			switch (chunk.type) {
				case "text-delta":
					controller.enqueue(chunk.textDelta);
					break;
				case "object":
				case "finish":
				case "error": break;
				default: {
					const _exhaustiveCheck = chunk;
					throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);
				}
			}
		} })));
	}
	get fullStream() {
		return createAsyncIterableStream(this.baseStream);
	}
	pipeTextStreamToResponse(response, init) {
		writeToServerResponse({
			response,
			status: init == null ? void 0 : init.status,
			statusText: init == null ? void 0 : init.statusText,
			headers: prepareOutgoingHttpHeaders(init == null ? void 0 : init.headers, { contentType: "text/plain; charset=utf-8" }),
			stream: this.textStream.pipeThrough(new TextEncoderStream())
		});
	}
	toTextStreamResponse(init) {
		var _a17;
		return new Response(this.textStream.pipeThrough(new TextEncoderStream()), {
			status: (_a17 = init == null ? void 0 : init.status) != null ? _a17 : 200,
			headers: prepareResponseHeaders(init == null ? void 0 : init.headers, { contentType: "text/plain; charset=utf-8" })
		});
	}
};
var name9 = "AI_NoOutputSpecifiedError";
var marker9 = `vercel.ai.error.${name9}`;
var symbol9 = Symbol.for(marker9);
var _a9;
var NoOutputSpecifiedError = class extends AISDKError {
	constructor({ message = "No output specified." } = {}) {
		super({
			name: name9,
			message
		});
		this[_a9] = true;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker9);
	}
};
_a9 = symbol9;
var name10 = "AI_ToolExecutionError";
var marker10 = `vercel.ai.error.${name10}`;
var symbol10 = Symbol.for(marker10);
var _a10;
var ToolExecutionError = class extends AISDKError {
	constructor({ toolArgs, toolName, toolCallId, cause, message = `Error executing tool ${toolName}: ${getErrorMessage$1(cause)}` }) {
		super({
			name: name10,
			message,
			cause
		});
		this[_a10] = true;
		this.toolArgs = toolArgs;
		this.toolName = toolName;
		this.toolCallId = toolCallId;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker10);
	}
};
_a10 = symbol10;
function isNonEmptyObject(object2) {
	return object2 != null && Object.keys(object2).length > 0;
}
function prepareToolsAndToolChoice({ tools, toolChoice, activeTools }) {
	if (!isNonEmptyObject(tools)) return {
		tools: void 0,
		toolChoice: void 0
	};
	const filteredTools = activeTools != null ? Object.entries(tools).filter(([name17]) => activeTools.includes(name17)) : Object.entries(tools);
	return {
		tools: filteredTools.map(([name17, tool2]) => {
			const toolType = tool2.type;
			switch (toolType) {
				case void 0:
				case "function": return {
					type: "function",
					name: name17,
					description: tool2.description,
					parameters: asSchema(tool2.parameters).jsonSchema
				};
				case "provider-defined": return {
					type: "provider-defined",
					name: name17,
					id: tool2.id,
					args: tool2.args
				};
				default: {
					const exhaustiveCheck = toolType;
					throw new Error(`Unsupported tool type: ${exhaustiveCheck}`);
				}
			}
		}),
		toolChoice: toolChoice == null ? { type: "auto" } : typeof toolChoice === "string" ? { type: toolChoice } : {
			type: "tool",
			toolName: toolChoice.toolName
		}
	};
}
var lastWhitespaceRegexp = /^([\s\S]*?)(\s+)(\S*)$/;
function splitOnLastWhitespace(text2) {
	const match = text2.match(lastWhitespaceRegexp);
	return match ? {
		prefix: match[1],
		whitespace: match[2],
		suffix: match[3]
	} : void 0;
}
function removeTextAfterLastWhitespace(text2) {
	const match = splitOnLastWhitespace(text2);
	return match ? match.prefix + match.whitespace : text2;
}
var name11 = "AI_InvalidToolArgumentsError";
var marker11 = `vercel.ai.error.${name11}`;
var symbol11 = Symbol.for(marker11);
var _a11;
var InvalidToolArgumentsError = class extends AISDKError {
	constructor({ toolArgs, toolName, cause, message = `Invalid arguments for tool ${toolName}: ${getErrorMessage$1(cause)}` }) {
		super({
			name: name11,
			message,
			cause
		});
		this[_a11] = true;
		this.toolArgs = toolArgs;
		this.toolName = toolName;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker11);
	}
};
_a11 = symbol11;
var name12 = "AI_NoSuchToolError";
var marker12 = `vercel.ai.error.${name12}`;
var symbol12 = Symbol.for(marker12);
var _a12;
var NoSuchToolError = class extends AISDKError {
	constructor({ toolName, availableTools = void 0, message = `Model tried to call unavailable tool '${toolName}'. ${availableTools === void 0 ? "No tools are available." : `Available tools: ${availableTools.join(", ")}.`}` }) {
		super({
			name: name12,
			message
		});
		this[_a12] = true;
		this.toolName = toolName;
		this.availableTools = availableTools;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker12);
	}
};
_a12 = symbol12;
var name13 = "AI_ToolCallRepairError";
var marker13 = `vercel.ai.error.${name13}`;
var symbol13 = Symbol.for(marker13);
var _a13;
var ToolCallRepairError = class extends AISDKError {
	constructor({ cause, originalError, message = `Error repairing tool call: ${getErrorMessage$1(cause)}` }) {
		super({
			name: name13,
			message,
			cause
		});
		this[_a13] = true;
		this.originalError = originalError;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker13);
	}
};
_a13 = symbol13;
async function parseToolCall({ toolCall, tools, repairToolCall, system, messages }) {
	if (tools == null) throw new NoSuchToolError({ toolName: toolCall.toolName });
	try {
		return await doParseToolCall({
			toolCall,
			tools
		});
	} catch (error) {
		if (repairToolCall == null || !(NoSuchToolError.isInstance(error) || InvalidToolArgumentsError.isInstance(error))) throw error;
		let repairedToolCall = null;
		try {
			repairedToolCall = await repairToolCall({
				toolCall,
				tools,
				parameterSchema: ({ toolName }) => asSchema(tools[toolName].parameters).jsonSchema,
				system,
				messages,
				error
			});
		} catch (repairError) {
			throw new ToolCallRepairError({
				cause: repairError,
				originalError: error
			});
		}
		if (repairedToolCall == null) throw error;
		return await doParseToolCall({
			toolCall: repairedToolCall,
			tools
		});
	}
}
async function doParseToolCall({ toolCall, tools }) {
	const toolName = toolCall.toolName;
	const tool2 = tools[toolName];
	if (tool2 == null) throw new NoSuchToolError({
		toolName: toolCall.toolName,
		availableTools: Object.keys(tools)
	});
	const schema = asSchema(tool2.parameters);
	const parseResult = toolCall.args.trim() === "" ? safeValidateTypes({
		value: {},
		schema
	}) : safeParseJSON({
		text: toolCall.args,
		schema
	});
	if (parseResult.success === false) throw new InvalidToolArgumentsError({
		toolName,
		toolArgs: toolCall.args,
		cause: parseResult.error
	});
	return {
		type: "tool-call",
		toolCallId: toolCall.toolCallId,
		toolName,
		args: parseResult.value
	};
}
function asReasoningText(reasoning) {
	const reasoningText = reasoning.filter((part) => part.type === "text").map((part) => part.text).join("");
	return reasoningText.length > 0 ? reasoningText : void 0;
}
function toResponseMessages({ text: text2 = "", files, reasoning, tools, toolCalls, toolResults, messageId, generateMessageId }) {
	const responseMessages = [];
	const content = [];
	if (reasoning.length > 0) content.push(...reasoning.map((part) => part.type === "text" ? {
		...part,
		type: "reasoning"
	} : {
		...part,
		type: "redacted-reasoning"
	}));
	if (files.length > 0) content.push(...files.map((file) => ({
		type: "file",
		data: file.base64,
		mimeType: file.mimeType
	})));
	if (text2.length > 0) content.push({
		type: "text",
		text: text2
	});
	if (toolCalls.length > 0) content.push(...toolCalls);
	if (content.length > 0) responseMessages.push({
		role: "assistant",
		content,
		id: messageId
	});
	if (toolResults.length > 0) responseMessages.push({
		role: "tool",
		id: generateMessageId(),
		content: toolResults.map((toolResult) => {
			const tool2 = tools[toolResult.toolName];
			return (tool2 == null ? void 0 : tool2.experimental_toToolResultContent) != null ? {
				type: "tool-result",
				toolCallId: toolResult.toolCallId,
				toolName: toolResult.toolName,
				result: tool2.experimental_toToolResultContent(toolResult.result),
				experimental_content: tool2.experimental_toToolResultContent(toolResult.result)
			} : {
				type: "tool-result",
				toolCallId: toolResult.toolCallId,
				toolName: toolResult.toolName,
				result: toolResult.result
			};
		})
	});
	return responseMessages;
}
var originalGenerateId3 = createIdGenerator({
	prefix: "aitxt",
	size: 24
});
var originalGenerateMessageId = createIdGenerator({
	prefix: "msg",
	size: 24
});
async function generateText({ model, tools, toolChoice, system, prompt, messages, maxRetries: maxRetriesArg, abortSignal, headers, maxSteps = 1, experimental_generateMessageId: generateMessageId = originalGenerateMessageId, experimental_output: output, experimental_continueSteps: continueSteps = false, experimental_telemetry: telemetry, experimental_providerMetadata, providerOptions = experimental_providerMetadata, experimental_activeTools: activeTools, experimental_prepareStep: prepareStep, experimental_repairToolCall: repairToolCall, _internal: { generateId: generateId3 = originalGenerateId3, currentDate = () => /* @__PURE__ */ new Date() } = {}, onStepFinish,...settings }) {
	var _a17;
	if (maxSteps < 1) throw new InvalidArgumentError$1({
		parameter: "maxSteps",
		value: maxSteps,
		message: "maxSteps must be at least 1"
	});
	const { maxRetries, retry } = prepareRetries({ maxRetries: maxRetriesArg });
	const baseTelemetryAttributes = getBaseTelemetryAttributes({
		model,
		telemetry,
		headers,
		settings: {
			...settings,
			maxRetries
		}
	});
	const initialPrompt = standardizePrompt({
		prompt: {
			system: (_a17 = output == null ? void 0 : output.injectIntoSystemPrompt({
				system,
				model
			})) != null ? _a17 : system,
			prompt,
			messages
		},
		tools
	});
	const tracer = getTracer(telemetry);
	return recordSpan({
		name: "ai.generateText",
		attributes: selectTelemetryAttributes({
			telemetry,
			attributes: {
				...assembleOperationName({
					operationId: "ai.generateText",
					telemetry
				}),
				...baseTelemetryAttributes,
				"ai.model.provider": model.provider,
				"ai.model.id": model.modelId,
				"ai.prompt": { input: () => JSON.stringify({
					system,
					prompt,
					messages
				}) },
				"ai.settings.maxSteps": maxSteps
			}
		}),
		tracer,
		fn: async (span) => {
			var _a18, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;
			const callSettings = prepareCallSettings(settings);
			let currentModelResponse;
			let currentToolCalls = [];
			let currentToolResults = [];
			let currentReasoningDetails = [];
			let stepCount = 0;
			const responseMessages = [];
			let text2 = "";
			const sources = [];
			const steps = [];
			let usage = {
				completionTokens: 0,
				promptTokens: 0,
				totalTokens: 0
			};
			let stepType = "initial";
			do {
				const promptFormat = stepCount === 0 ? initialPrompt.type : "messages";
				const stepInputMessages = [...initialPrompt.messages, ...responseMessages];
				const prepareStepResult = await (prepareStep == null ? void 0 : prepareStep({
					model,
					steps,
					maxSteps,
					stepNumber: stepCount
				}));
				const stepToolChoice = (_a18 = prepareStepResult == null ? void 0 : prepareStepResult.toolChoice) != null ? _a18 : toolChoice;
				const stepActiveTools = (_b = prepareStepResult == null ? void 0 : prepareStepResult.experimental_activeTools) != null ? _b : activeTools;
				const stepModel = (_c = prepareStepResult == null ? void 0 : prepareStepResult.model) != null ? _c : model;
				const promptMessages = await convertToLanguageModelPrompt({
					prompt: {
						type: promptFormat,
						system: initialPrompt.system,
						messages: stepInputMessages
					},
					modelSupportsImageUrls: stepModel.supportsImageUrls,
					modelSupportsUrl: (_d = stepModel.supportsUrl) == null ? void 0 : _d.bind(stepModel)
				});
				const mode = {
					type: "regular",
					...prepareToolsAndToolChoice({
						tools,
						toolChoice: stepToolChoice,
						activeTools: stepActiveTools
					})
				};
				currentModelResponse = await retry(() => recordSpan({
					name: "ai.generateText.doGenerate",
					attributes: selectTelemetryAttributes({
						telemetry,
						attributes: {
							...assembleOperationName({
								operationId: "ai.generateText.doGenerate",
								telemetry
							}),
							...baseTelemetryAttributes,
							"ai.model.provider": stepModel.provider,
							"ai.model.id": stepModel.modelId,
							"ai.prompt.format": { input: () => promptFormat },
							"ai.prompt.messages": { input: () => stringifyForTelemetry(promptMessages) },
							"ai.prompt.tools": { input: () => {
								var _a19;
								return (_a19 = mode.tools) == null ? void 0 : _a19.map((tool2) => JSON.stringify(tool2));
							} },
							"ai.prompt.toolChoice": { input: () => mode.toolChoice != null ? JSON.stringify(mode.toolChoice) : void 0 },
							"gen_ai.system": stepModel.provider,
							"gen_ai.request.model": stepModel.modelId,
							"gen_ai.request.frequency_penalty": settings.frequencyPenalty,
							"gen_ai.request.max_tokens": settings.maxTokens,
							"gen_ai.request.presence_penalty": settings.presencePenalty,
							"gen_ai.request.stop_sequences": settings.stopSequences,
							"gen_ai.request.temperature": settings.temperature,
							"gen_ai.request.top_k": settings.topK,
							"gen_ai.request.top_p": settings.topP
						}
					}),
					tracer,
					fn: async (span2) => {
						var _a19, _b2, _c2, _d2, _e2, _f2;
						const result = await stepModel.doGenerate({
							mode,
							...callSettings,
							inputFormat: promptFormat,
							responseFormat: output == null ? void 0 : output.responseFormat({ model }),
							prompt: promptMessages,
							providerMetadata: providerOptions,
							abortSignal,
							headers
						});
						const responseData = {
							id: (_b2 = (_a19 = result.response) == null ? void 0 : _a19.id) != null ? _b2 : generateId3(),
							timestamp: (_d2 = (_c2 = result.response) == null ? void 0 : _c2.timestamp) != null ? _d2 : currentDate(),
							modelId: (_f2 = (_e2 = result.response) == null ? void 0 : _e2.modelId) != null ? _f2 : stepModel.modelId
						};
						span2.setAttributes(selectTelemetryAttributes({
							telemetry,
							attributes: {
								"ai.response.finishReason": result.finishReason,
								"ai.response.text": { output: () => result.text },
								"ai.response.toolCalls": { output: () => JSON.stringify(result.toolCalls) },
								"ai.response.id": responseData.id,
								"ai.response.model": responseData.modelId,
								"ai.response.timestamp": responseData.timestamp.toISOString(),
								"ai.usage.promptTokens": result.usage.promptTokens,
								"ai.usage.completionTokens": result.usage.completionTokens,
								"gen_ai.response.finish_reasons": [result.finishReason],
								"gen_ai.response.id": responseData.id,
								"gen_ai.response.model": responseData.modelId,
								"gen_ai.usage.input_tokens": result.usage.promptTokens,
								"gen_ai.usage.output_tokens": result.usage.completionTokens
							}
						}));
						return {
							...result,
							response: responseData
						};
					}
				}));
				currentToolCalls = await Promise.all(((_e = currentModelResponse.toolCalls) != null ? _e : []).map((toolCall) => parseToolCall({
					toolCall,
					tools,
					repairToolCall,
					system,
					messages: stepInputMessages
				})));
				currentToolResults = tools == null ? [] : await executeTools({
					toolCalls: currentToolCalls,
					tools,
					tracer,
					telemetry,
					messages: stepInputMessages,
					abortSignal
				});
				const currentUsage = calculateLanguageModelUsage(currentModelResponse.usage);
				usage = addLanguageModelUsage(usage, currentUsage);
				let nextStepType = "done";
				if (++stepCount < maxSteps) {
					if (continueSteps && currentModelResponse.finishReason === "length" && currentToolCalls.length === 0) nextStepType = "continue";
					else if (currentToolCalls.length > 0 && currentToolResults.length === currentToolCalls.length) nextStepType = "tool-result";
				}
				const originalText = (_f = currentModelResponse.text) != null ? _f : "";
				const stepTextLeadingWhitespaceTrimmed = stepType === "continue" && text2.trimEnd() !== text2 ? originalText.trimStart() : originalText;
				const stepText = nextStepType === "continue" ? removeTextAfterLastWhitespace(stepTextLeadingWhitespaceTrimmed) : stepTextLeadingWhitespaceTrimmed;
				text2 = nextStepType === "continue" || stepType === "continue" ? text2 + stepText : stepText;
				currentReasoningDetails = asReasoningDetails(currentModelResponse.reasoning);
				sources.push(...(_g = currentModelResponse.sources) != null ? _g : []);
				if (stepType === "continue") {
					const lastMessage = responseMessages[responseMessages.length - 1];
					if (typeof lastMessage.content === "string") lastMessage.content += stepText;
					else lastMessage.content.push({
						text: stepText,
						type: "text"
					});
				} else responseMessages.push(...toResponseMessages({
					text: text2,
					files: asFiles(currentModelResponse.files),
					reasoning: asReasoningDetails(currentModelResponse.reasoning),
					tools: tools != null ? tools : {},
					toolCalls: currentToolCalls,
					toolResults: currentToolResults,
					messageId: generateMessageId(),
					generateMessageId
				}));
				const currentStepResult = {
					stepType,
					text: stepText,
					reasoning: asReasoningText(currentReasoningDetails),
					reasoningDetails: currentReasoningDetails,
					files: asFiles(currentModelResponse.files),
					sources: (_h = currentModelResponse.sources) != null ? _h : [],
					toolCalls: currentToolCalls,
					toolResults: currentToolResults,
					finishReason: currentModelResponse.finishReason,
					usage: currentUsage,
					warnings: currentModelResponse.warnings,
					logprobs: currentModelResponse.logprobs,
					request: (_i = currentModelResponse.request) != null ? _i : {},
					response: {
						...currentModelResponse.response,
						headers: (_j = currentModelResponse.rawResponse) == null ? void 0 : _j.headers,
						body: (_k = currentModelResponse.rawResponse) == null ? void 0 : _k.body,
						messages: structuredClone(responseMessages)
					},
					providerMetadata: currentModelResponse.providerMetadata,
					experimental_providerMetadata: currentModelResponse.providerMetadata,
					isContinued: nextStepType === "continue"
				};
				steps.push(currentStepResult);
				await (onStepFinish == null ? void 0 : onStepFinish(currentStepResult));
				stepType = nextStepType;
			} while (stepType !== "done");
			span.setAttributes(selectTelemetryAttributes({
				telemetry,
				attributes: {
					"ai.response.finishReason": currentModelResponse.finishReason,
					"ai.response.text": { output: () => currentModelResponse.text },
					"ai.response.toolCalls": { output: () => JSON.stringify(currentModelResponse.toolCalls) },
					"ai.usage.promptTokens": currentModelResponse.usage.promptTokens,
					"ai.usage.completionTokens": currentModelResponse.usage.completionTokens
				}
			}));
			return new DefaultGenerateTextResult({
				text: text2,
				files: asFiles(currentModelResponse.files),
				reasoning: asReasoningText(currentReasoningDetails),
				reasoningDetails: currentReasoningDetails,
				sources,
				outputResolver: () => {
					if (output == null) throw new NoOutputSpecifiedError();
					return output.parseOutput({ text: text2 }, {
						response: currentModelResponse.response,
						usage,
						finishReason: currentModelResponse.finishReason
					});
				},
				toolCalls: currentToolCalls,
				toolResults: currentToolResults,
				finishReason: currentModelResponse.finishReason,
				usage,
				warnings: currentModelResponse.warnings,
				request: (_l = currentModelResponse.request) != null ? _l : {},
				response: {
					...currentModelResponse.response,
					headers: (_m = currentModelResponse.rawResponse) == null ? void 0 : _m.headers,
					body: (_n = currentModelResponse.rawResponse) == null ? void 0 : _n.body,
					messages: responseMessages
				},
				logprobs: currentModelResponse.logprobs,
				steps,
				providerMetadata: currentModelResponse.providerMetadata
			});
		}
	});
}
async function executeTools({ toolCalls, tools, tracer, telemetry, messages, abortSignal }) {
	const toolResults = await Promise.all(toolCalls.map(async ({ toolCallId, toolName, args }) => {
		const tool2 = tools[toolName];
		if ((tool2 == null ? void 0 : tool2.execute) == null) return void 0;
		const result = await recordSpan({
			name: "ai.toolCall",
			attributes: selectTelemetryAttributes({
				telemetry,
				attributes: {
					...assembleOperationName({
						operationId: "ai.toolCall",
						telemetry
					}),
					"ai.toolCall.name": toolName,
					"ai.toolCall.id": toolCallId,
					"ai.toolCall.args": { output: () => JSON.stringify(args) }
				}
			}),
			tracer,
			fn: async (span) => {
				try {
					const result2 = await tool2.execute(args, {
						toolCallId,
						messages,
						abortSignal
					});
					try {
						span.setAttributes(selectTelemetryAttributes({
							telemetry,
							attributes: { "ai.toolCall.result": { output: () => JSON.stringify(result2) } }
						}));
					} catch (ignored) {}
					return result2;
				} catch (error) {
					throw new ToolExecutionError({
						toolCallId,
						toolName,
						toolArgs: args,
						cause: error
					});
				}
			}
		});
		return {
			type: "tool-result",
			toolCallId,
			toolName,
			args,
			result
		};
	}));
	return toolResults.filter((result) => result != null);
}
var DefaultGenerateTextResult = class {
	constructor(options) {
		this.text = options.text;
		this.files = options.files;
		this.reasoning = options.reasoning;
		this.reasoningDetails = options.reasoningDetails;
		this.toolCalls = options.toolCalls;
		this.toolResults = options.toolResults;
		this.finishReason = options.finishReason;
		this.usage = options.usage;
		this.warnings = options.warnings;
		this.request = options.request;
		this.response = options.response;
		this.steps = options.steps;
		this.experimental_providerMetadata = options.providerMetadata;
		this.providerMetadata = options.providerMetadata;
		this.logprobs = options.logprobs;
		this.outputResolver = options.outputResolver;
		this.sources = options.sources;
	}
	get experimental_output() {
		return this.outputResolver();
	}
};
function asReasoningDetails(reasoning) {
	if (reasoning == null) return [];
	if (typeof reasoning === "string") return [{
		type: "text",
		text: reasoning
	}];
	return reasoning;
}
function asFiles(files) {
	var _a17;
	return (_a17 = files == null ? void 0 : files.map((file) => new DefaultGeneratedFile(file))) != null ? _a17 : [];
}
var output_exports = {};
__export(output_exports, {
	object: () => object,
	text: () => text
});
var name14 = "AI_InvalidStreamPartError";
var marker14 = `vercel.ai.error.${name14}`;
var symbol14 = Symbol.for(marker14);
var _a14;
var InvalidStreamPartError = class extends AISDKError {
	constructor({ chunk, message }) {
		super({
			name: name14,
			message
		});
		this[_a14] = true;
		this.chunk = chunk;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker14);
	}
};
_a14 = symbol14;
var name15 = "AI_MCPClientError";
var marker15 = `vercel.ai.error.${name15}`;
var symbol15 = Symbol.for(marker15);
var _a15;
var MCPClientError = class extends AISDKError {
	constructor({ name: name17 = "MCPClientError", message, cause }) {
		super({
			name: name17,
			message,
			cause
		});
		this[_a15] = true;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker15);
	}
};
_a15 = symbol15;
var text = () => ({
	type: "text",
	responseFormat: () => ({ type: "text" }),
	injectIntoSystemPrompt({ system }) {
		return system;
	},
	parsePartial({ text: text2 }) {
		return { partial: text2 };
	},
	parseOutput({ text: text2 }) {
		return text2;
	}
});
var object = ({ schema: inputSchema }) => {
	const schema = asSchema(inputSchema);
	return {
		type: "object",
		responseFormat: ({ model }) => ({
			type: "json",
			schema: model.supportsStructuredOutputs ? schema.jsonSchema : void 0
		}),
		injectIntoSystemPrompt({ system, model }) {
			return model.supportsStructuredOutputs ? system : injectJsonInstruction({
				prompt: system,
				schema: schema.jsonSchema
			});
		},
		parsePartial({ text: text2 }) {
			const result = parsePartialJson(text2);
			switch (result.state) {
				case "failed-parse":
				case "undefined-input": return void 0;
				case "repaired-parse":
				case "successful-parse": return { partial: result.value };
				default: {
					const _exhaustiveCheck = result.state;
					throw new Error(`Unsupported parse state: ${_exhaustiveCheck}`);
				}
			}
		},
		parseOutput({ text: text2 }, context) {
			const parseResult = safeParseJSON({ text: text2 });
			if (!parseResult.success) throw new NoObjectGeneratedError({
				message: "No object generated: could not parse the response.",
				cause: parseResult.error,
				text: text2,
				response: context.response,
				usage: context.usage,
				finishReason: context.finishReason
			});
			const validationResult = safeValidateTypes({
				value: parseResult.value,
				schema
			});
			if (!validationResult.success) throw new NoObjectGeneratedError({
				message: "No object generated: response did not match schema.",
				cause: validationResult.error,
				text: text2,
				response: context.response,
				usage: context.usage,
				finishReason: context.finishReason
			});
			return validationResult.value;
		}
	};
};
var CHUNKING_REGEXPS = {
	word: /\S+\s+/m,
	line: /\n+/m
};
function smoothStream({ delayInMs = 10, chunking = "word", _internal: { delay: delay2 = delay } = {} } = {}) {
	let detectChunk;
	if (typeof chunking === "function") detectChunk = (buffer) => {
		const match = chunking(buffer);
		if (match == null) return null;
		if (!match.length) throw new Error(`Chunking function must return a non-empty string.`);
		if (!buffer.startsWith(match)) throw new Error(`Chunking function must return a match that is a prefix of the buffer. Received: "${match}" expected to start with "${buffer}"`);
		return match;
	};
	else {
		const chunkingRegex = typeof chunking === "string" ? CHUNKING_REGEXPS[chunking] : chunking;
		if (chunkingRegex == null) throw new InvalidArgumentError({
			argument: "chunking",
			message: `Chunking must be "word" or "line" or a RegExp. Received: ${chunking}`
		});
		detectChunk = (buffer) => {
			const match = chunkingRegex.exec(buffer);
			if (!match) return null;
			return buffer.slice(0, match.index) + (match == null ? void 0 : match[0]);
		};
	}
	return () => {
		let buffer = "";
		return new TransformStream({ async transform(chunk, controller) {
			if (chunk.type !== "text-delta") {
				if (buffer.length > 0) {
					controller.enqueue({
						type: "text-delta",
						textDelta: buffer
					});
					buffer = "";
				}
				controller.enqueue(chunk);
				return;
			}
			buffer += chunk.textDelta;
			let match;
			while ((match = detectChunk(buffer)) != null) {
				controller.enqueue({
					type: "text-delta",
					textDelta: match
				});
				buffer = buffer.slice(match.length);
				await delay2(delayInMs);
			}
		} });
	};
}
function asArray(value) {
	return value === void 0 ? [] : Array.isArray(value) ? value : [value];
}
async function consumeStream({ stream, onError }) {
	const reader = stream.getReader();
	try {
		while (true) {
			const { done } = await reader.read();
			if (done) break;
		}
	} catch (error) {
		onError == null || onError(error);
	} finally {
		reader.releaseLock();
	}
}
function mergeStreams(stream1, stream2) {
	const reader1 = stream1.getReader();
	const reader2 = stream2.getReader();
	let lastRead1 = void 0;
	let lastRead2 = void 0;
	let stream1Done = false;
	let stream2Done = false;
	async function readStream1(controller) {
		try {
			if (lastRead1 == null) lastRead1 = reader1.read();
			const result = await lastRead1;
			lastRead1 = void 0;
			if (!result.done) controller.enqueue(result.value);
			else controller.close();
		} catch (error) {
			controller.error(error);
		}
	}
	async function readStream2(controller) {
		try {
			if (lastRead2 == null) lastRead2 = reader2.read();
			const result = await lastRead2;
			lastRead2 = void 0;
			if (!result.done) controller.enqueue(result.value);
			else controller.close();
		} catch (error) {
			controller.error(error);
		}
	}
	return new ReadableStream({
		async pull(controller) {
			try {
				if (stream1Done) {
					await readStream2(controller);
					return;
				}
				if (stream2Done) {
					await readStream1(controller);
					return;
				}
				if (lastRead1 == null) lastRead1 = reader1.read();
				if (lastRead2 == null) lastRead2 = reader2.read();
				const { result, reader } = await Promise.race([lastRead1.then((result2) => ({
					result: result2,
					reader: reader1
				})), lastRead2.then((result2) => ({
					result: result2,
					reader: reader2
				}))]);
				if (!result.done) controller.enqueue(result.value);
				if (reader === reader1) {
					lastRead1 = void 0;
					if (result.done) {
						await readStream2(controller);
						stream1Done = true;
					}
				} else {
					lastRead2 = void 0;
					if (result.done) {
						stream2Done = true;
						await readStream1(controller);
					}
				}
			} catch (error) {
				controller.error(error);
			}
		},
		cancel() {
			reader1.cancel();
			reader2.cancel();
		}
	});
}
function runToolsTransformation({ tools, generatorStream, toolCallStreaming, tracer, telemetry, system, messages, abortSignal, repairToolCall }) {
	let toolResultsStreamController = null;
	const toolResultsStream = new ReadableStream({ start(controller) {
		toolResultsStreamController = controller;
	} });
	const activeToolCalls = {};
	const outstandingToolResults = /* @__PURE__ */ new Set();
	let canClose = false;
	let finishChunk = void 0;
	function attemptClose() {
		if (canClose && outstandingToolResults.size === 0) {
			if (finishChunk != null) toolResultsStreamController.enqueue(finishChunk);
			toolResultsStreamController.close();
		}
	}
	const forwardStream = new TransformStream({
		async transform(chunk, controller) {
			const chunkType = chunk.type;
			switch (chunkType) {
				case "text-delta":
				case "reasoning":
				case "reasoning-signature":
				case "redacted-reasoning":
				case "source":
				case "response-metadata":
				case "error": {
					controller.enqueue(chunk);
					break;
				}
				case "file": {
					controller.enqueue(new DefaultGeneratedFileWithType({
						data: chunk.data,
						mimeType: chunk.mimeType
					}));
					break;
				}
				case "tool-call-delta": {
					if (toolCallStreaming) {
						if (!activeToolCalls[chunk.toolCallId]) {
							controller.enqueue({
								type: "tool-call-streaming-start",
								toolCallId: chunk.toolCallId,
								toolName: chunk.toolName
							});
							activeToolCalls[chunk.toolCallId] = true;
						}
						controller.enqueue({
							type: "tool-call-delta",
							toolCallId: chunk.toolCallId,
							toolName: chunk.toolName,
							argsTextDelta: chunk.argsTextDelta
						});
					}
					break;
				}
				case "tool-call": {
					try {
						const toolCall = await parseToolCall({
							toolCall: chunk,
							tools,
							repairToolCall,
							system,
							messages
						});
						controller.enqueue(toolCall);
						const tool2 = tools[toolCall.toolName];
						if (tool2.execute != null) {
							const toolExecutionId = generateId();
							outstandingToolResults.add(toolExecutionId);
							recordSpan({
								name: "ai.toolCall",
								attributes: selectTelemetryAttributes({
									telemetry,
									attributes: {
										...assembleOperationName({
											operationId: "ai.toolCall",
											telemetry
										}),
										"ai.toolCall.name": toolCall.toolName,
										"ai.toolCall.id": toolCall.toolCallId,
										"ai.toolCall.args": { output: () => JSON.stringify(toolCall.args) }
									}
								}),
								tracer,
								fn: async (span) => tool2.execute(toolCall.args, {
									toolCallId: toolCall.toolCallId,
									messages,
									abortSignal
								}).then((result) => {
									toolResultsStreamController.enqueue({
										...toolCall,
										type: "tool-result",
										result
									});
									outstandingToolResults.delete(toolExecutionId);
									attemptClose();
									try {
										span.setAttributes(selectTelemetryAttributes({
											telemetry,
											attributes: { "ai.toolCall.result": { output: () => JSON.stringify(result) } }
										}));
									} catch (ignored) {}
								}, (error) => {
									toolResultsStreamController.enqueue({
										type: "error",
										error: new ToolExecutionError({
											toolCallId: toolCall.toolCallId,
											toolName: toolCall.toolName,
											toolArgs: toolCall.args,
											cause: error
										})
									});
									outstandingToolResults.delete(toolExecutionId);
									attemptClose();
								})
							});
						}
					} catch (error) {
						toolResultsStreamController.enqueue({
							type: "error",
							error
						});
					}
					break;
				}
				case "finish": {
					finishChunk = {
						type: "finish",
						finishReason: chunk.finishReason,
						logprobs: chunk.logprobs,
						usage: calculateLanguageModelUsage(chunk.usage),
						experimental_providerMetadata: chunk.providerMetadata
					};
					break;
				}
				default: {
					const _exhaustiveCheck = chunkType;
					throw new Error(`Unhandled chunk type: ${_exhaustiveCheck}`);
				}
			}
		},
		flush() {
			canClose = true;
			attemptClose();
		}
	});
	return new ReadableStream({ async start(controller) {
		return Promise.all([generatorStream.pipeThrough(forwardStream).pipeTo(new WritableStream({
			write(chunk) {
				controller.enqueue(chunk);
			},
			close() {}
		})), toolResultsStream.pipeTo(new WritableStream({
			write(chunk) {
				controller.enqueue(chunk);
			},
			close() {
				controller.close();
			}
		}))]);
	} });
}
var originalGenerateId4 = createIdGenerator({
	prefix: "aitxt",
	size: 24
});
var originalGenerateMessageId2 = createIdGenerator({
	prefix: "msg",
	size: 24
});
function streamText({ model, tools, toolChoice, system, prompt, messages, maxRetries, abortSignal, headers, maxSteps = 1, experimental_generateMessageId: generateMessageId = originalGenerateMessageId2, experimental_output: output, experimental_continueSteps: continueSteps = false, experimental_telemetry: telemetry, experimental_providerMetadata, providerOptions = experimental_providerMetadata, experimental_toolCallStreaming = false, toolCallStreaming = experimental_toolCallStreaming, experimental_activeTools: activeTools, experimental_repairToolCall: repairToolCall, experimental_transform: transform, onChunk, onError, onFinish, onStepFinish, _internal: { now: now2 = now, generateId: generateId3 = originalGenerateId4, currentDate = () => /* @__PURE__ */ new Date() } = {},...settings }) {
	return new DefaultStreamTextResult({
		model,
		telemetry,
		headers,
		settings,
		maxRetries,
		abortSignal,
		system,
		prompt,
		messages,
		tools,
		toolChoice,
		toolCallStreaming,
		transforms: asArray(transform),
		activeTools,
		repairToolCall,
		maxSteps,
		output,
		continueSteps,
		providerOptions,
		onChunk,
		onError,
		onFinish,
		onStepFinish,
		now: now2,
		currentDate,
		generateId: generateId3,
		generateMessageId
	});
}
function createOutputTransformStream(output) {
	if (!output) return new TransformStream({ transform(chunk, controller) {
		controller.enqueue({
			part: chunk,
			partialOutput: void 0
		});
	} });
	let text2 = "";
	let textChunk = "";
	let lastPublishedJson = "";
	function publishTextChunk({ controller, partialOutput = void 0 }) {
		controller.enqueue({
			part: {
				type: "text-delta",
				textDelta: textChunk
			},
			partialOutput
		});
		textChunk = "";
	}
	return new TransformStream({
		transform(chunk, controller) {
			if (chunk.type === "step-finish") publishTextChunk({ controller });
			if (chunk.type !== "text-delta") {
				controller.enqueue({
					part: chunk,
					partialOutput: void 0
				});
				return;
			}
			text2 += chunk.textDelta;
			textChunk += chunk.textDelta;
			const result = output.parsePartial({ text: text2 });
			if (result != null) {
				const currentJson = JSON.stringify(result.partial);
				if (currentJson !== lastPublishedJson) {
					publishTextChunk({
						controller,
						partialOutput: result.partial
					});
					lastPublishedJson = currentJson;
				}
			}
		},
		flush(controller) {
			if (textChunk.length > 0) publishTextChunk({ controller });
		}
	});
}
var DefaultStreamTextResult = class {
	constructor({ model, telemetry, headers, settings, maxRetries: maxRetriesArg, abortSignal, system, prompt, messages, tools, toolChoice, toolCallStreaming, transforms, activeTools, repairToolCall, maxSteps, output, continueSteps, providerOptions, now: now2, currentDate, generateId: generateId3, generateMessageId, onChunk, onError, onFinish, onStepFinish }) {
		this.warningsPromise = new DelayedPromise();
		this.usagePromise = new DelayedPromise();
		this.finishReasonPromise = new DelayedPromise();
		this.providerMetadataPromise = new DelayedPromise();
		this.textPromise = new DelayedPromise();
		this.reasoningPromise = new DelayedPromise();
		this.reasoningDetailsPromise = new DelayedPromise();
		this.sourcesPromise = new DelayedPromise();
		this.filesPromise = new DelayedPromise();
		this.toolCallsPromise = new DelayedPromise();
		this.toolResultsPromise = new DelayedPromise();
		this.requestPromise = new DelayedPromise();
		this.responsePromise = new DelayedPromise();
		this.stepsPromise = new DelayedPromise();
		var _a17;
		if (maxSteps < 1) throw new InvalidArgumentError$1({
			parameter: "maxSteps",
			value: maxSteps,
			message: "maxSteps must be at least 1"
		});
		this.output = output;
		let recordedStepText = "";
		let recordedContinuationText = "";
		let recordedFullText = "";
		let stepReasoning = [];
		let stepFiles = [];
		let activeReasoningText = void 0;
		let recordedStepSources = [];
		const recordedSources = [];
		const recordedResponse = {
			id: generateId3(),
			timestamp: currentDate(),
			modelId: model.modelId,
			messages: []
		};
		let recordedToolCalls = [];
		let recordedToolResults = [];
		let recordedFinishReason = void 0;
		let recordedUsage = void 0;
		let stepType = "initial";
		const recordedSteps = [];
		let rootSpan;
		const eventProcessor = new TransformStream({
			async transform(chunk, controller) {
				controller.enqueue(chunk);
				const { part } = chunk;
				if (part.type === "text-delta" || part.type === "reasoning" || part.type === "source" || part.type === "tool-call" || part.type === "tool-result" || part.type === "tool-call-streaming-start" || part.type === "tool-call-delta") await (onChunk == null ? void 0 : onChunk({ chunk: part }));
				if (part.type === "error") await (onError == null ? void 0 : onError({ error: part.error }));
				if (part.type === "text-delta") {
					recordedStepText += part.textDelta;
					recordedContinuationText += part.textDelta;
					recordedFullText += part.textDelta;
				}
				if (part.type === "reasoning") if (activeReasoningText == null) {
					activeReasoningText = {
						type: "text",
						text: part.textDelta
					};
					stepReasoning.push(activeReasoningText);
				} else activeReasoningText.text += part.textDelta;
				if (part.type === "reasoning-signature") {
					if (activeReasoningText == null) throw new AISDKError({
						name: "InvalidStreamPart",
						message: "reasoning-signature without reasoning"
					});
					activeReasoningText.signature = part.signature;
					activeReasoningText = void 0;
				}
				if (part.type === "redacted-reasoning") stepReasoning.push({
					type: "redacted",
					data: part.data
				});
				if (part.type === "file") stepFiles.push(part);
				if (part.type === "source") {
					recordedSources.push(part.source);
					recordedStepSources.push(part.source);
				}
				if (part.type === "tool-call") recordedToolCalls.push(part);
				if (part.type === "tool-result") recordedToolResults.push(part);
				if (part.type === "step-finish") {
					const stepMessages = toResponseMessages({
						text: recordedContinuationText,
						files: stepFiles,
						reasoning: stepReasoning,
						tools: tools != null ? tools : {},
						toolCalls: recordedToolCalls,
						toolResults: recordedToolResults,
						messageId: part.messageId,
						generateMessageId
					});
					const currentStep = recordedSteps.length;
					let nextStepType = "done";
					if (currentStep + 1 < maxSteps) {
						if (continueSteps && part.finishReason === "length" && recordedToolCalls.length === 0) nextStepType = "continue";
						else if (recordedToolCalls.length > 0 && recordedToolResults.length === recordedToolCalls.length) nextStepType = "tool-result";
					}
					const currentStepResult = {
						stepType,
						text: recordedStepText,
						reasoning: asReasoningText(stepReasoning),
						reasoningDetails: stepReasoning,
						files: stepFiles,
						sources: recordedStepSources,
						toolCalls: recordedToolCalls,
						toolResults: recordedToolResults,
						finishReason: part.finishReason,
						usage: part.usage,
						warnings: part.warnings,
						logprobs: part.logprobs,
						request: part.request,
						response: {
							...part.response,
							messages: [...recordedResponse.messages, ...stepMessages]
						},
						providerMetadata: part.experimental_providerMetadata,
						experimental_providerMetadata: part.experimental_providerMetadata,
						isContinued: part.isContinued
					};
					await (onStepFinish == null ? void 0 : onStepFinish(currentStepResult));
					recordedSteps.push(currentStepResult);
					recordedToolCalls = [];
					recordedToolResults = [];
					recordedStepText = "";
					recordedStepSources = [];
					stepReasoning = [];
					stepFiles = [];
					activeReasoningText = void 0;
					if (nextStepType !== "done") stepType = nextStepType;
					if (nextStepType !== "continue") {
						recordedResponse.messages.push(...stepMessages);
						recordedContinuationText = "";
					}
				}
				if (part.type === "finish") {
					recordedResponse.id = part.response.id;
					recordedResponse.timestamp = part.response.timestamp;
					recordedResponse.modelId = part.response.modelId;
					recordedResponse.headers = part.response.headers;
					recordedUsage = part.usage;
					recordedFinishReason = part.finishReason;
				}
			},
			async flush(controller) {
				var _a18;
				try {
					if (recordedSteps.length === 0) return;
					const lastStep = recordedSteps[recordedSteps.length - 1];
					self$1.warningsPromise.resolve(lastStep.warnings);
					self$1.requestPromise.resolve(lastStep.request);
					self$1.responsePromise.resolve(lastStep.response);
					self$1.toolCallsPromise.resolve(lastStep.toolCalls);
					self$1.toolResultsPromise.resolve(lastStep.toolResults);
					self$1.providerMetadataPromise.resolve(lastStep.experimental_providerMetadata);
					self$1.reasoningPromise.resolve(lastStep.reasoning);
					self$1.reasoningDetailsPromise.resolve(lastStep.reasoningDetails);
					const finishReason = recordedFinishReason != null ? recordedFinishReason : "unknown";
					const usage = recordedUsage != null ? recordedUsage : {
						completionTokens: NaN,
						promptTokens: NaN,
						totalTokens: NaN
					};
					self$1.finishReasonPromise.resolve(finishReason);
					self$1.usagePromise.resolve(usage);
					self$1.textPromise.resolve(recordedFullText);
					self$1.sourcesPromise.resolve(recordedSources);
					self$1.filesPromise.resolve(lastStep.files);
					self$1.stepsPromise.resolve(recordedSteps);
					await (onFinish == null ? void 0 : onFinish({
						finishReason,
						logprobs: void 0,
						usage,
						text: recordedFullText,
						reasoning: lastStep.reasoning,
						reasoningDetails: lastStep.reasoningDetails,
						files: lastStep.files,
						sources: lastStep.sources,
						toolCalls: lastStep.toolCalls,
						toolResults: lastStep.toolResults,
						request: (_a18 = lastStep.request) != null ? _a18 : {},
						response: lastStep.response,
						warnings: lastStep.warnings,
						providerMetadata: lastStep.providerMetadata,
						experimental_providerMetadata: lastStep.experimental_providerMetadata,
						steps: recordedSteps
					}));
					rootSpan.setAttributes(selectTelemetryAttributes({
						telemetry,
						attributes: {
							"ai.response.finishReason": finishReason,
							"ai.response.text": { output: () => recordedFullText },
							"ai.response.toolCalls": { output: () => {
								var _a19;
								return ((_a19 = lastStep.toolCalls) == null ? void 0 : _a19.length) ? JSON.stringify(lastStep.toolCalls) : void 0;
							} },
							"ai.usage.promptTokens": usage.promptTokens,
							"ai.usage.completionTokens": usage.completionTokens
						}
					}));
				} catch (error) {
					controller.error(error);
				} finally {
					rootSpan.end();
				}
			}
		});
		const stitchableStream = createStitchableStream();
		this.addStream = stitchableStream.addStream;
		this.closeStream = stitchableStream.close;
		let stream = stitchableStream.stream;
		for (const transform of transforms) stream = stream.pipeThrough(transform({
			tools,
			stopStream() {
				stitchableStream.terminate();
			}
		}));
		this.baseStream = stream.pipeThrough(createOutputTransformStream(output)).pipeThrough(eventProcessor);
		const { maxRetries, retry } = prepareRetries({ maxRetries: maxRetriesArg });
		const tracer = getTracer(telemetry);
		const baseTelemetryAttributes = getBaseTelemetryAttributes({
			model,
			telemetry,
			headers,
			settings: {
				...settings,
				maxRetries
			}
		});
		const initialPrompt = standardizePrompt({
			prompt: {
				system: (_a17 = output == null ? void 0 : output.injectIntoSystemPrompt({
					system,
					model
				})) != null ? _a17 : system,
				prompt,
				messages
			},
			tools
		});
		const self$1 = this;
		recordSpan({
			name: "ai.streamText",
			attributes: selectTelemetryAttributes({
				telemetry,
				attributes: {
					...assembleOperationName({
						operationId: "ai.streamText",
						telemetry
					}),
					...baseTelemetryAttributes,
					"ai.prompt": { input: () => JSON.stringify({
						system,
						prompt,
						messages
					}) },
					"ai.settings.maxSteps": maxSteps
				}
			}),
			tracer,
			endWhenDone: false,
			fn: async (rootSpanArg) => {
				rootSpan = rootSpanArg;
				async function streamStep({ currentStep, responseMessages, usage, stepType: stepType2, previousStepText, hasLeadingWhitespace, messageId }) {
					var _a18;
					const promptFormat = responseMessages.length === 0 ? initialPrompt.type : "messages";
					const stepInputMessages = [...initialPrompt.messages, ...responseMessages];
					const promptMessages = await convertToLanguageModelPrompt({
						prompt: {
							type: promptFormat,
							system: initialPrompt.system,
							messages: stepInputMessages
						},
						modelSupportsImageUrls: model.supportsImageUrls,
						modelSupportsUrl: (_a18 = model.supportsUrl) == null ? void 0 : _a18.bind(model)
					});
					const mode = {
						type: "regular",
						...prepareToolsAndToolChoice({
							tools,
							toolChoice,
							activeTools
						})
					};
					const { result: { stream: stream2, warnings, rawResponse, request }, doStreamSpan, startTimestampMs } = await retry(() => recordSpan({
						name: "ai.streamText.doStream",
						attributes: selectTelemetryAttributes({
							telemetry,
							attributes: {
								...assembleOperationName({
									operationId: "ai.streamText.doStream",
									telemetry
								}),
								...baseTelemetryAttributes,
								"ai.prompt.format": { input: () => promptFormat },
								"ai.prompt.messages": { input: () => stringifyForTelemetry(promptMessages) },
								"ai.prompt.tools": { input: () => {
									var _a19;
									return (_a19 = mode.tools) == null ? void 0 : _a19.map((tool2) => JSON.stringify(tool2));
								} },
								"ai.prompt.toolChoice": { input: () => mode.toolChoice != null ? JSON.stringify(mode.toolChoice) : void 0 },
								"gen_ai.system": model.provider,
								"gen_ai.request.model": model.modelId,
								"gen_ai.request.frequency_penalty": settings.frequencyPenalty,
								"gen_ai.request.max_tokens": settings.maxTokens,
								"gen_ai.request.presence_penalty": settings.presencePenalty,
								"gen_ai.request.stop_sequences": settings.stopSequences,
								"gen_ai.request.temperature": settings.temperature,
								"gen_ai.request.top_k": settings.topK,
								"gen_ai.request.top_p": settings.topP
							}
						}),
						tracer,
						endWhenDone: false,
						fn: async (doStreamSpan2) => ({
							startTimestampMs: now2(),
							doStreamSpan: doStreamSpan2,
							result: await model.doStream({
								mode,
								...prepareCallSettings(settings),
								inputFormat: promptFormat,
								responseFormat: output == null ? void 0 : output.responseFormat({ model }),
								prompt: promptMessages,
								providerMetadata: providerOptions,
								abortSignal,
								headers
							})
						})
					}));
					const transformedStream = runToolsTransformation({
						tools,
						generatorStream: stream2,
						toolCallStreaming,
						tracer,
						telemetry,
						system,
						messages: stepInputMessages,
						repairToolCall,
						abortSignal
					});
					const stepRequest = request != null ? request : {};
					const stepToolCalls = [];
					const stepToolResults = [];
					const stepReasoning2 = [];
					const stepFiles2 = [];
					let activeReasoningText2 = void 0;
					let stepFinishReason = "unknown";
					let stepUsage = {
						promptTokens: 0,
						completionTokens: 0,
						totalTokens: 0
					};
					let stepProviderMetadata;
					let stepFirstChunk = true;
					let stepText = "";
					let fullStepText = stepType2 === "continue" ? previousStepText : "";
					let stepLogProbs;
					let stepResponse = {
						id: generateId3(),
						timestamp: currentDate(),
						modelId: model.modelId
					};
					let chunkBuffer = "";
					let chunkTextPublished = false;
					let inWhitespacePrefix = true;
					let hasWhitespaceSuffix = false;
					async function publishTextChunk({ controller, chunk }) {
						controller.enqueue(chunk);
						stepText += chunk.textDelta;
						fullStepText += chunk.textDelta;
						chunkTextPublished = true;
						hasWhitespaceSuffix = chunk.textDelta.trimEnd() !== chunk.textDelta;
					}
					self$1.addStream(transformedStream.pipeThrough(new TransformStream({
						async transform(chunk, controller) {
							var _a19, _b, _c;
							if (stepFirstChunk) {
								const msToFirstChunk = now2() - startTimestampMs;
								stepFirstChunk = false;
								doStreamSpan.addEvent("ai.stream.firstChunk", { "ai.response.msToFirstChunk": msToFirstChunk });
								doStreamSpan.setAttributes({ "ai.response.msToFirstChunk": msToFirstChunk });
								controller.enqueue({
									type: "step-start",
									messageId,
									request: stepRequest,
									warnings: warnings != null ? warnings : []
								});
							}
							if (chunk.type === "text-delta" && chunk.textDelta.length === 0) return;
							const chunkType = chunk.type;
							switch (chunkType) {
								case "text-delta": {
									if (continueSteps) {
										const trimmedChunkText = inWhitespacePrefix && hasLeadingWhitespace ? chunk.textDelta.trimStart() : chunk.textDelta;
										if (trimmedChunkText.length === 0) break;
										inWhitespacePrefix = false;
										chunkBuffer += trimmedChunkText;
										const split = splitOnLastWhitespace(chunkBuffer);
										if (split != null) {
											chunkBuffer = split.suffix;
											await publishTextChunk({
												controller,
												chunk: {
													type: "text-delta",
													textDelta: split.prefix + split.whitespace
												}
											});
										}
									} else await publishTextChunk({
										controller,
										chunk
									});
									break;
								}
								case "reasoning": {
									controller.enqueue(chunk);
									if (activeReasoningText2 == null) {
										activeReasoningText2 = {
											type: "text",
											text: chunk.textDelta
										};
										stepReasoning2.push(activeReasoningText2);
									} else activeReasoningText2.text += chunk.textDelta;
									break;
								}
								case "reasoning-signature": {
									controller.enqueue(chunk);
									if (activeReasoningText2 == null) throw new InvalidStreamPartError({
										chunk,
										message: "reasoning-signature without reasoning"
									});
									activeReasoningText2.signature = chunk.signature;
									activeReasoningText2 = void 0;
									break;
								}
								case "redacted-reasoning": {
									controller.enqueue(chunk);
									stepReasoning2.push({
										type: "redacted",
										data: chunk.data
									});
									break;
								}
								case "tool-call": {
									controller.enqueue(chunk);
									stepToolCalls.push(chunk);
									break;
								}
								case "tool-result": {
									controller.enqueue(chunk);
									stepToolResults.push(chunk);
									break;
								}
								case "response-metadata": {
									stepResponse = {
										id: (_a19 = chunk.id) != null ? _a19 : stepResponse.id,
										timestamp: (_b = chunk.timestamp) != null ? _b : stepResponse.timestamp,
										modelId: (_c = chunk.modelId) != null ? _c : stepResponse.modelId
									};
									break;
								}
								case "finish": {
									stepUsage = chunk.usage;
									stepFinishReason = chunk.finishReason;
									stepProviderMetadata = chunk.experimental_providerMetadata;
									stepLogProbs = chunk.logprobs;
									const msToFinish = now2() - startTimestampMs;
									doStreamSpan.addEvent("ai.stream.finish");
									doStreamSpan.setAttributes({
										"ai.response.msToFinish": msToFinish,
										"ai.response.avgCompletionTokensPerSecond": 1e3 * stepUsage.completionTokens / msToFinish
									});
									break;
								}
								case "file": {
									stepFiles2.push(chunk);
									controller.enqueue(chunk);
									break;
								}
								case "source":
								case "tool-call-streaming-start":
								case "tool-call-delta": {
									controller.enqueue(chunk);
									break;
								}
								case "error": {
									controller.enqueue(chunk);
									stepFinishReason = "error";
									break;
								}
								default: {
									const exhaustiveCheck = chunkType;
									throw new Error(`Unknown chunk type: ${exhaustiveCheck}`);
								}
							}
						},
						async flush(controller) {
							const stepToolCallsJson = stepToolCalls.length > 0 ? JSON.stringify(stepToolCalls) : void 0;
							let nextStepType = "done";
							if (currentStep + 1 < maxSteps) {
								if (continueSteps && stepFinishReason === "length" && stepToolCalls.length === 0) nextStepType = "continue";
								else if (stepToolCalls.length > 0 && stepToolResults.length === stepToolCalls.length) nextStepType = "tool-result";
							}
							if (continueSteps && chunkBuffer.length > 0 && (nextStepType !== "continue" || stepType2 === "continue" && !chunkTextPublished)) {
								await publishTextChunk({
									controller,
									chunk: {
										type: "text-delta",
										textDelta: chunkBuffer
									}
								});
								chunkBuffer = "";
							}
							try {
								doStreamSpan.setAttributes(selectTelemetryAttributes({
									telemetry,
									attributes: {
										"ai.response.finishReason": stepFinishReason,
										"ai.response.text": { output: () => stepText },
										"ai.response.toolCalls": { output: () => stepToolCallsJson },
										"ai.response.id": stepResponse.id,
										"ai.response.model": stepResponse.modelId,
										"ai.response.timestamp": stepResponse.timestamp.toISOString(),
										"ai.usage.promptTokens": stepUsage.promptTokens,
										"ai.usage.completionTokens": stepUsage.completionTokens,
										"gen_ai.response.finish_reasons": [stepFinishReason],
										"gen_ai.response.id": stepResponse.id,
										"gen_ai.response.model": stepResponse.modelId,
										"gen_ai.usage.input_tokens": stepUsage.promptTokens,
										"gen_ai.usage.output_tokens": stepUsage.completionTokens
									}
								}));
							} catch (error) {} finally {
								doStreamSpan.end();
							}
							controller.enqueue({
								type: "step-finish",
								finishReason: stepFinishReason,
								usage: stepUsage,
								providerMetadata: stepProviderMetadata,
								experimental_providerMetadata: stepProviderMetadata,
								logprobs: stepLogProbs,
								request: stepRequest,
								response: {
									...stepResponse,
									headers: rawResponse == null ? void 0 : rawResponse.headers
								},
								warnings,
								isContinued: nextStepType === "continue",
								messageId
							});
							const combinedUsage = addLanguageModelUsage(usage, stepUsage);
							if (nextStepType === "done") {
								controller.enqueue({
									type: "finish",
									finishReason: stepFinishReason,
									usage: combinedUsage,
									providerMetadata: stepProviderMetadata,
									experimental_providerMetadata: stepProviderMetadata,
									logprobs: stepLogProbs,
									response: {
										...stepResponse,
										headers: rawResponse == null ? void 0 : rawResponse.headers
									}
								});
								self$1.closeStream();
							} else {
								if (stepType2 === "continue") {
									const lastMessage = responseMessages[responseMessages.length - 1];
									if (typeof lastMessage.content === "string") lastMessage.content += stepText;
									else lastMessage.content.push({
										text: stepText,
										type: "text"
									});
								} else responseMessages.push(...toResponseMessages({
									text: stepText,
									files: stepFiles2,
									reasoning: stepReasoning2,
									tools: tools != null ? tools : {},
									toolCalls: stepToolCalls,
									toolResults: stepToolResults,
									messageId,
									generateMessageId
								}));
								await streamStep({
									currentStep: currentStep + 1,
									responseMessages,
									usage: combinedUsage,
									stepType: nextStepType,
									previousStepText: fullStepText,
									hasLeadingWhitespace: hasWhitespaceSuffix,
									messageId: nextStepType === "continue" ? messageId : generateMessageId()
								});
							}
						}
					})));
				}
				await streamStep({
					currentStep: 0,
					responseMessages: [],
					usage: {
						promptTokens: 0,
						completionTokens: 0,
						totalTokens: 0
					},
					previousStepText: "",
					stepType: "initial",
					hasLeadingWhitespace: false,
					messageId: generateMessageId()
				});
			}
		}).catch((error) => {
			self$1.addStream(new ReadableStream({ start(controller) {
				controller.enqueue({
					type: "error",
					error
				});
				controller.close();
			} }));
			self$1.closeStream();
		});
	}
	get warnings() {
		return this.warningsPromise.value;
	}
	get usage() {
		return this.usagePromise.value;
	}
	get finishReason() {
		return this.finishReasonPromise.value;
	}
	get experimental_providerMetadata() {
		return this.providerMetadataPromise.value;
	}
	get providerMetadata() {
		return this.providerMetadataPromise.value;
	}
	get text() {
		return this.textPromise.value;
	}
	get reasoning() {
		return this.reasoningPromise.value;
	}
	get reasoningDetails() {
		return this.reasoningDetailsPromise.value;
	}
	get sources() {
		return this.sourcesPromise.value;
	}
	get files() {
		return this.filesPromise.value;
	}
	get toolCalls() {
		return this.toolCallsPromise.value;
	}
	get toolResults() {
		return this.toolResultsPromise.value;
	}
	get request() {
		return this.requestPromise.value;
	}
	get response() {
		return this.responsePromise.value;
	}
	get steps() {
		return this.stepsPromise.value;
	}
	/**
	Split out a new stream from the original stream.
	The original stream is replaced to allow for further splitting,
	since we do not know how many times the stream will be split.
	
	Note: this leads to buffering the stream content on the server.
	However, the LLM results are expected to be small enough to not cause issues.
	*/
	teeStream() {
		const [stream1, stream2] = this.baseStream.tee();
		this.baseStream = stream2;
		return stream1;
	}
	get textStream() {
		return createAsyncIterableStream(this.teeStream().pipeThrough(new TransformStream({ transform({ part }, controller) {
			if (part.type === "text-delta") controller.enqueue(part.textDelta);
		} })));
	}
	get fullStream() {
		return createAsyncIterableStream(this.teeStream().pipeThrough(new TransformStream({ transform({ part }, controller) {
			controller.enqueue(part);
		} })));
	}
	async consumeStream(options) {
		var _a17;
		try {
			await consumeStream({
				stream: this.fullStream,
				onError: options == null ? void 0 : options.onError
			});
		} catch (error) {
			(_a17 = options == null ? void 0 : options.onError) == null || _a17.call(options, error);
		}
	}
	get experimental_partialOutputStream() {
		if (this.output == null) throw new NoOutputSpecifiedError();
		return createAsyncIterableStream(this.teeStream().pipeThrough(new TransformStream({ transform({ partialOutput }, controller) {
			if (partialOutput != null) controller.enqueue(partialOutput);
		} })));
	}
	toDataStreamInternal({ getErrorMessage: getErrorMessage5 = () => "An error occurred.", sendUsage = true, sendReasoning = false, sendSources = false, experimental_sendFinish = true }) {
		return this.fullStream.pipeThrough(new TransformStream({ transform: async (chunk, controller) => {
			const chunkType = chunk.type;
			switch (chunkType) {
				case "text-delta": {
					controller.enqueue(formatDataStreamPart("text", chunk.textDelta));
					break;
				}
				case "reasoning": {
					if (sendReasoning) controller.enqueue(formatDataStreamPart("reasoning", chunk.textDelta));
					break;
				}
				case "redacted-reasoning": {
					if (sendReasoning) controller.enqueue(formatDataStreamPart("redacted_reasoning", { data: chunk.data }));
					break;
				}
				case "reasoning-signature": {
					if (sendReasoning) controller.enqueue(formatDataStreamPart("reasoning_signature", { signature: chunk.signature }));
					break;
				}
				case "file": {
					controller.enqueue(formatDataStreamPart("file", {
						mimeType: chunk.mimeType,
						data: chunk.base64
					}));
					break;
				}
				case "source": {
					if (sendSources) controller.enqueue(formatDataStreamPart("source", chunk.source));
					break;
				}
				case "tool-call-streaming-start": {
					controller.enqueue(formatDataStreamPart("tool_call_streaming_start", {
						toolCallId: chunk.toolCallId,
						toolName: chunk.toolName
					}));
					break;
				}
				case "tool-call-delta": {
					controller.enqueue(formatDataStreamPart("tool_call_delta", {
						toolCallId: chunk.toolCallId,
						argsTextDelta: chunk.argsTextDelta
					}));
					break;
				}
				case "tool-call": {
					controller.enqueue(formatDataStreamPart("tool_call", {
						toolCallId: chunk.toolCallId,
						toolName: chunk.toolName,
						args: chunk.args
					}));
					break;
				}
				case "tool-result": {
					controller.enqueue(formatDataStreamPart("tool_result", {
						toolCallId: chunk.toolCallId,
						result: chunk.result
					}));
					break;
				}
				case "error": {
					controller.enqueue(formatDataStreamPart("error", getErrorMessage5(chunk.error)));
					break;
				}
				case "step-start": {
					controller.enqueue(formatDataStreamPart("start_step", { messageId: chunk.messageId }));
					break;
				}
				case "step-finish": {
					controller.enqueue(formatDataStreamPart("finish_step", {
						finishReason: chunk.finishReason,
						usage: sendUsage ? {
							promptTokens: chunk.usage.promptTokens,
							completionTokens: chunk.usage.completionTokens
						} : void 0,
						isContinued: chunk.isContinued
					}));
					break;
				}
				case "finish": {
					if (experimental_sendFinish) controller.enqueue(formatDataStreamPart("finish_message", {
						finishReason: chunk.finishReason,
						usage: sendUsage ? {
							promptTokens: chunk.usage.promptTokens,
							completionTokens: chunk.usage.completionTokens
						} : void 0
					}));
					break;
				}
				default: {
					const exhaustiveCheck = chunkType;
					throw new Error(`Unknown chunk type: ${exhaustiveCheck}`);
				}
			}
		} }));
	}
	pipeDataStreamToResponse(response, { status, statusText, headers, data, getErrorMessage: getErrorMessage5, sendUsage, sendReasoning, sendSources, experimental_sendFinish } = {}) {
		writeToServerResponse({
			response,
			status,
			statusText,
			headers: prepareOutgoingHttpHeaders(headers, {
				contentType: "text/plain; charset=utf-8",
				dataStreamVersion: "v1"
			}),
			stream: this.toDataStream({
				data,
				getErrorMessage: getErrorMessage5,
				sendUsage,
				sendReasoning,
				sendSources,
				experimental_sendFinish
			})
		});
	}
	pipeTextStreamToResponse(response, init) {
		writeToServerResponse({
			response,
			status: init == null ? void 0 : init.status,
			statusText: init == null ? void 0 : init.statusText,
			headers: prepareOutgoingHttpHeaders(init == null ? void 0 : init.headers, { contentType: "text/plain; charset=utf-8" }),
			stream: this.textStream.pipeThrough(new TextEncoderStream())
		});
	}
	toDataStream(options) {
		const stream = this.toDataStreamInternal({
			getErrorMessage: options == null ? void 0 : options.getErrorMessage,
			sendUsage: options == null ? void 0 : options.sendUsage,
			sendReasoning: options == null ? void 0 : options.sendReasoning,
			sendSources: options == null ? void 0 : options.sendSources,
			experimental_sendFinish: options == null ? void 0 : options.experimental_sendFinish
		}).pipeThrough(new TextEncoderStream());
		return (options == null ? void 0 : options.data) ? mergeStreams(options == null ? void 0 : options.data.stream, stream) : stream;
	}
	mergeIntoDataStream(writer, options) {
		writer.merge(this.toDataStreamInternal({
			getErrorMessage: writer.onError,
			sendUsage: options == null ? void 0 : options.sendUsage,
			sendReasoning: options == null ? void 0 : options.sendReasoning,
			sendSources: options == null ? void 0 : options.sendSources,
			experimental_sendFinish: options == null ? void 0 : options.experimental_sendFinish
		}));
	}
	toDataStreamResponse({ headers, status, statusText, data, getErrorMessage: getErrorMessage5, sendUsage, sendReasoning, sendSources, experimental_sendFinish } = {}) {
		return new Response(this.toDataStream({
			data,
			getErrorMessage: getErrorMessage5,
			sendUsage,
			sendReasoning,
			sendSources,
			experimental_sendFinish
		}), {
			status,
			statusText,
			headers: prepareResponseHeaders(headers, {
				contentType: "text/plain; charset=utf-8",
				dataStreamVersion: "v1"
			})
		});
	}
	toTextStreamResponse(init) {
		var _a17;
		return new Response(this.textStream.pipeThrough(new TextEncoderStream()), {
			status: (_a17 = init == null ? void 0 : init.status) != null ? _a17 : 200,
			headers: prepareResponseHeaders(init == null ? void 0 : init.headers, { contentType: "text/plain; charset=utf-8" })
		});
	}
};
var NoSpeechGeneratedError = class extends AISDKError {
	constructor(options) {
		super({
			name: "AI_NoSpeechGeneratedError",
			message: "No speech audio generated."
		});
		this.responses = options.responses;
	}
};
var DefaultGeneratedAudioFile = class extends DefaultGeneratedFile {
	constructor({ data, mimeType }) {
		super({
			data,
			mimeType
		});
		let format = "mp3";
		if (mimeType) {
			const mimeTypeParts = mimeType.split("/");
			if (mimeTypeParts.length === 2) {
				if (mimeType !== "audio/mpeg") format = mimeTypeParts[1];
			}
		}
		if (!format) throw new Error("Audio format must be provided or determinable from mimeType");
		this.format = format;
	}
};
async function generateSpeech({ model, text: text2, voice, outputFormat, instructions, speed, providerOptions = {}, maxRetries: maxRetriesArg, abortSignal, headers }) {
	var _a17;
	const { retry } = prepareRetries({ maxRetries: maxRetriesArg });
	const result = await retry(() => model.doGenerate({
		text: text2,
		voice,
		outputFormat,
		instructions,
		speed,
		abortSignal,
		headers,
		providerOptions
	}));
	if (!result.audio || result.audio.length === 0) throw new NoSpeechGeneratedError({ responses: [result.response] });
	return new DefaultSpeechResult({
		audio: new DefaultGeneratedAudioFile({
			data: result.audio,
			mimeType: (_a17 = detectMimeType({
				data: result.audio,
				signatures: audioMimeTypeSignatures
			})) != null ? _a17 : "audio/mp3"
		}),
		warnings: result.warnings,
		responses: [result.response],
		providerMetadata: result.providerMetadata
	});
}
var DefaultSpeechResult = class {
	constructor(options) {
		var _a17;
		this.audio = options.audio;
		this.warnings = options.warnings;
		this.responses = options.responses;
		this.providerMetadata = (_a17 = options.providerMetadata) != null ? _a17 : {};
	}
};
var NoTranscriptGeneratedError = class extends AISDKError {
	constructor(options) {
		super({
			name: "AI_NoTranscriptGeneratedError",
			message: "No transcript generated."
		});
		this.responses = options.responses;
	}
};
async function transcribe({ model, audio, providerOptions = {}, maxRetries: maxRetriesArg, abortSignal, headers }) {
	const { retry } = prepareRetries({ maxRetries: maxRetriesArg });
	const audioData = audio instanceof URL ? (await download({ url: audio })).data : convertDataContentToUint8Array(audio);
	const result = await retry(() => {
		var _a17;
		return model.doGenerate({
			audio: audioData,
			abortSignal,
			headers,
			providerOptions,
			mediaType: (_a17 = detectMimeType({
				data: audioData,
				signatures: audioMimeTypeSignatures
			})) != null ? _a17 : "audio/wav"
		});
	});
	if (!result.text) throw new NoTranscriptGeneratedError({ responses: [result.response] });
	return new DefaultTranscriptionResult({
		text: result.text,
		segments: result.segments,
		language: result.language,
		durationInSeconds: result.durationInSeconds,
		warnings: result.warnings,
		responses: [result.response],
		providerMetadata: result.providerMetadata
	});
}
var DefaultTranscriptionResult = class {
	constructor(options) {
		var _a17;
		this.text = options.text;
		this.segments = options.segments;
		this.language = options.language;
		this.durationInSeconds = options.durationInSeconds;
		this.warnings = options.warnings;
		this.responses = options.responses;
		this.providerMetadata = (_a17 = options.providerMetadata) != null ? _a17 : {};
	}
};
function mergeObjects(target, source) {
	if (target === void 0 && source === void 0) return void 0;
	if (target === void 0) return source;
	if (source === void 0) return target;
	const result = { ...target };
	for (const key in source) if (Object.prototype.hasOwnProperty.call(source, key)) {
		const sourceValue = source[key];
		if (sourceValue === void 0) continue;
		const targetValue = key in target ? target[key] : void 0;
		const isSourceObject = sourceValue !== null && typeof sourceValue === "object" && !Array.isArray(sourceValue) && !(sourceValue instanceof Date) && !(sourceValue instanceof RegExp);
		const isTargetObject = targetValue !== null && targetValue !== void 0 && typeof targetValue === "object" && !Array.isArray(targetValue) && !(targetValue instanceof Date) && !(targetValue instanceof RegExp);
		if (isSourceObject && isTargetObject) result[key] = mergeObjects(targetValue, sourceValue);
		else result[key] = sourceValue;
	}
	return result;
}
function defaultSettingsMiddleware({ settings }) {
	return {
		middlewareVersion: "v1",
		transformParams: async ({ params }) => {
			var _a17;
			return {
				...settings,
				...params,
				providerMetadata: mergeObjects(settings.providerMetadata, params.providerMetadata),
				temperature: params.temperature === 0 || params.temperature == null ? (_a17 = settings.temperature) != null ? _a17 : 0 : params.temperature
			};
		}
	};
}
function getPotentialStartIndex(text2, searchedText) {
	if (searchedText.length === 0) return null;
	const directIndex = text2.indexOf(searchedText);
	if (directIndex !== -1) return directIndex;
	for (let i = text2.length - 1; i >= 0; i--) {
		const suffix = text2.substring(i);
		if (searchedText.startsWith(suffix)) return i;
	}
	return null;
}
function extractReasoningMiddleware({ tagName, separator = "\n", startWithReasoning = false }) {
	const openingTag = `<${tagName}>`;
	const closingTag = `</${tagName}>`;
	return {
		middlewareVersion: "v1",
		wrapGenerate: async ({ doGenerate }) => {
			const { text: rawText,...rest } = await doGenerate();
			if (rawText == null) return {
				text: rawText,
				...rest
			};
			const text2 = startWithReasoning ? openingTag + rawText : rawText;
			const regexp = new RegExp(`${openingTag}(.*?)${closingTag}`, "gs");
			const matches = Array.from(text2.matchAll(regexp));
			if (!matches.length) return {
				text: text2,
				...rest
			};
			const reasoning = matches.map((match) => match[1]).join(separator);
			let textWithoutReasoning = text2;
			for (let i = matches.length - 1; i >= 0; i--) {
				const match = matches[i];
				const beforeMatch = textWithoutReasoning.slice(0, match.index);
				const afterMatch = textWithoutReasoning.slice(match.index + match[0].length);
				textWithoutReasoning = beforeMatch + (beforeMatch.length > 0 && afterMatch.length > 0 ? separator : "") + afterMatch;
			}
			return {
				...rest,
				text: textWithoutReasoning,
				reasoning
			};
		},
		wrapStream: async ({ doStream }) => {
			const { stream,...rest } = await doStream();
			let isFirstReasoning = true;
			let isFirstText = true;
			let afterSwitch = false;
			let isReasoning = startWithReasoning;
			let buffer = "";
			return {
				stream: stream.pipeThrough(new TransformStream({ transform: (chunk, controller) => {
					if (chunk.type !== "text-delta") {
						controller.enqueue(chunk);
						return;
					}
					buffer += chunk.textDelta;
					function publish(text2) {
						if (text2.length > 0) {
							const prefix = afterSwitch && (isReasoning ? !isFirstReasoning : !isFirstText) ? separator : "";
							controller.enqueue({
								type: isReasoning ? "reasoning" : "text-delta",
								textDelta: prefix + text2
							});
							afterSwitch = false;
							if (isReasoning) isFirstReasoning = false;
							else isFirstText = false;
						}
					}
					do {
						const nextTag = isReasoning ? closingTag : openingTag;
						const startIndex = getPotentialStartIndex(buffer, nextTag);
						if (startIndex == null) {
							publish(buffer);
							buffer = "";
							break;
						}
						publish(buffer.slice(0, startIndex));
						const foundFullMatch = startIndex + nextTag.length <= buffer.length;
						if (foundFullMatch) {
							buffer = buffer.slice(startIndex + nextTag.length);
							isReasoning = !isReasoning;
							afterSwitch = true;
						} else {
							buffer = buffer.slice(startIndex);
							break;
						}
					} while (true);
				} })),
				...rest
			};
		}
	};
}
function simulateStreamingMiddleware() {
	return {
		middlewareVersion: "v1",
		wrapStream: async ({ doGenerate }) => {
			const result = await doGenerate();
			const simulatedStream = new ReadableStream({ start(controller) {
				controller.enqueue({
					type: "response-metadata",
					...result.response
				});
				if (result.reasoning) if (typeof result.reasoning === "string") controller.enqueue({
					type: "reasoning",
					textDelta: result.reasoning
				});
				else for (const reasoning of result.reasoning) switch (reasoning.type) {
					case "text": {
						controller.enqueue({
							type: "reasoning",
							textDelta: reasoning.text
						});
						if (reasoning.signature != null) controller.enqueue({
							type: "reasoning-signature",
							signature: reasoning.signature
						});
						break;
					}
					case "redacted": {
						controller.enqueue({
							type: "redacted-reasoning",
							data: reasoning.data
						});
						break;
					}
				}
				if (result.text) controller.enqueue({
					type: "text-delta",
					textDelta: result.text
				});
				if (result.toolCalls) for (const toolCall of result.toolCalls) {
					controller.enqueue({
						type: "tool-call-delta",
						toolCallType: "function",
						toolCallId: toolCall.toolCallId,
						toolName: toolCall.toolName,
						argsTextDelta: toolCall.args
					});
					controller.enqueue({
						type: "tool-call",
						...toolCall
					});
				}
				controller.enqueue({
					type: "finish",
					finishReason: result.finishReason,
					usage: result.usage,
					logprobs: result.logprobs,
					providerMetadata: result.providerMetadata
				});
				controller.close();
			} });
			return {
				stream: simulatedStream,
				rawCall: result.rawCall,
				rawResponse: result.rawResponse,
				warnings: result.warnings
			};
		}
	};
}
var wrapLanguageModel = ({ model, middleware: middlewareArg, modelId, providerId }) => {
	return asArray(middlewareArg).reverse().reduce((wrappedModel, middleware) => {
		return doWrap({
			model: wrappedModel,
			middleware,
			modelId,
			providerId
		});
	}, model);
};
var doWrap = ({ model, middleware: { transformParams, wrapGenerate, wrapStream }, modelId, providerId }) => {
	var _a17;
	async function doTransform({ params, type }) {
		return transformParams ? await transformParams({
			params,
			type
		}) : params;
	}
	return {
		specificationVersion: "v1",
		provider: providerId != null ? providerId : model.provider,
		modelId: modelId != null ? modelId : model.modelId,
		defaultObjectGenerationMode: model.defaultObjectGenerationMode,
		supportsImageUrls: model.supportsImageUrls,
		supportsUrl: (_a17 = model.supportsUrl) == null ? void 0 : _a17.bind(model),
		supportsStructuredOutputs: model.supportsStructuredOutputs,
		async doGenerate(params) {
			const transformedParams = await doTransform({
				params,
				type: "generate"
			});
			const doGenerate = async () => model.doGenerate(transformedParams);
			const doStream = async () => model.doStream(transformedParams);
			return wrapGenerate ? wrapGenerate({
				doGenerate,
				doStream,
				params: transformedParams,
				model
			}) : doGenerate();
		},
		async doStream(params) {
			const transformedParams = await doTransform({
				params,
				type: "stream"
			});
			const doGenerate = async () => model.doGenerate(transformedParams);
			const doStream = async () => model.doStream(transformedParams);
			return wrapStream ? wrapStream({
				doGenerate,
				doStream,
				params: transformedParams,
				model
			}) : doStream();
		}
	};
};
var experimental_wrapLanguageModel = wrapLanguageModel;
function appendClientMessage({ messages, message }) {
	return [...messages.length > 0 && messages[messages.length - 1].id === message.id ? messages.slice(0, -1) : messages, message];
}
function appendResponseMessages({ messages, responseMessages, _internal: { currentDate = () => /* @__PURE__ */ new Date() } = {} }) {
	var _a17, _b, _c, _d;
	const clonedMessages = structuredClone(messages);
	for (const message of responseMessages) {
		const role = message.role;
		const lastMessage = clonedMessages[clonedMessages.length - 1];
		const isLastMessageAssistant = lastMessage.role === "assistant";
		switch (role) {
			case "assistant": {
				let getToolInvocations2 = function(step) {
					return (typeof message.content === "string" ? [] : message.content.filter((part) => part.type === "tool-call")).map((call) => ({
						state: "call",
						step,
						args: call.args,
						toolCallId: call.toolCallId,
						toolName: call.toolName
					}));
				};
				var getToolInvocations = getToolInvocations2;
				const parts = [{ type: "step-start" }];
				let textContent = "";
				let reasoningTextContent = void 0;
				if (typeof message.content === "string") {
					textContent = message.content;
					parts.push({
						type: "text",
						text: message.content
					});
				} else {
					let reasoningPart = void 0;
					for (const part of message.content) switch (part.type) {
						case "text": {
							reasoningPart = void 0;
							textContent += part.text;
							parts.push({
								type: "text",
								text: part.text
							});
							break;
						}
						case "reasoning": {
							if (reasoningPart == null) {
								reasoningPart = {
									type: "reasoning",
									reasoning: "",
									details: []
								};
								parts.push(reasoningPart);
							}
							reasoningTextContent = (reasoningTextContent != null ? reasoningTextContent : "") + part.text;
							reasoningPart.reasoning += part.text;
							reasoningPart.details.push({
								type: "text",
								text: part.text,
								signature: part.signature
							});
							break;
						}
						case "redacted-reasoning": {
							if (reasoningPart == null) {
								reasoningPart = {
									type: "reasoning",
									reasoning: "",
									details: []
								};
								parts.push(reasoningPart);
							}
							reasoningPart.details.push({
								type: "redacted",
								data: part.data
							});
							break;
						}
						case "tool-call": break;
						case "file":
							if (part.data instanceof URL) throw new AISDKError({
								name: "InvalidAssistantFileData",
								message: "File data cannot be a URL"
							});
							parts.push({
								type: "file",
								mimeType: part.mimeType,
								data: convertDataContentToBase64String(part.data)
							});
							break;
					}
				}
				if (isLastMessageAssistant) {
					const maxStep = extractMaxToolInvocationStep(lastMessage.toolInvocations);
					(_a17 = lastMessage.parts) != null || (lastMessage.parts = []);
					lastMessage.content = textContent;
					lastMessage.reasoning = reasoningTextContent;
					lastMessage.parts.push(...parts);
					lastMessage.toolInvocations = [...(_b = lastMessage.toolInvocations) != null ? _b : [], ...getToolInvocations2(maxStep === void 0 ? 0 : maxStep + 1)];
					getToolInvocations2(maxStep === void 0 ? 0 : maxStep + 1).map((call) => ({
						type: "tool-invocation",
						toolInvocation: call
					})).forEach((part) => {
						lastMessage.parts.push(part);
					});
				} else clonedMessages.push({
					role: "assistant",
					id: message.id,
					createdAt: currentDate(),
					content: textContent,
					reasoning: reasoningTextContent,
					toolInvocations: getToolInvocations2(0),
					parts: [...parts, ...getToolInvocations2(0).map((call) => ({
						type: "tool-invocation",
						toolInvocation: call
					}))]
				});
				break;
			}
			case "tool": {
				(_c = lastMessage.toolInvocations) != null || (lastMessage.toolInvocations = []);
				if (lastMessage.role !== "assistant") throw new Error(`Tool result must follow an assistant message: ${lastMessage.role}`);
				(_d = lastMessage.parts) != null || (lastMessage.parts = []);
				for (const contentPart of message.content) {
					const toolCall = lastMessage.toolInvocations.find((call) => call.toolCallId === contentPart.toolCallId);
					const toolCallPart = lastMessage.parts.find((part) => part.type === "tool-invocation" && part.toolInvocation.toolCallId === contentPart.toolCallId);
					if (!toolCall) throw new Error("Tool call not found in previous message");
					toolCall.state = "result";
					const toolResult = toolCall;
					toolResult.result = contentPart.result;
					if (toolCallPart) toolCallPart.toolInvocation = toolResult;
					else lastMessage.parts.push({
						type: "tool-invocation",
						toolInvocation: toolResult
					});
				}
				break;
			}
			default: {
				const _exhaustiveCheck = role;
				throw new Error(`Unsupported message role: ${_exhaustiveCheck}`);
			}
		}
	}
	return clonedMessages;
}
function customProvider({ languageModels, textEmbeddingModels, imageModels, fallbackProvider }) {
	return {
		languageModel(modelId) {
			if (languageModels != null && modelId in languageModels) return languageModels[modelId];
			if (fallbackProvider) return fallbackProvider.languageModel(modelId);
			throw new NoSuchModelError({
				modelId,
				modelType: "languageModel"
			});
		},
		textEmbeddingModel(modelId) {
			if (textEmbeddingModels != null && modelId in textEmbeddingModels) return textEmbeddingModels[modelId];
			if (fallbackProvider) return fallbackProvider.textEmbeddingModel(modelId);
			throw new NoSuchModelError({
				modelId,
				modelType: "textEmbeddingModel"
			});
		},
		imageModel(modelId) {
			if (imageModels != null && modelId in imageModels) return imageModels[modelId];
			if (fallbackProvider == null ? void 0 : fallbackProvider.imageModel) return fallbackProvider.imageModel(modelId);
			throw new NoSuchModelError({
				modelId,
				modelType: "imageModel"
			});
		}
	};
}
var experimental_customProvider = customProvider;
var name16 = "AI_NoSuchProviderError";
var marker16 = `vercel.ai.error.${name16}`;
var symbol16 = Symbol.for(marker16);
var _a16;
var NoSuchProviderError = class extends NoSuchModelError {
	constructor({ modelId, modelType, providerId, availableProviders, message = `No such provider: ${providerId} (available providers: ${availableProviders.join()})` }) {
		super({
			errorName: name16,
			modelId,
			modelType,
			message
		});
		this[_a16] = true;
		this.providerId = providerId;
		this.availableProviders = availableProviders;
	}
	static isInstance(error) {
		return AISDKError.hasMarker(error, marker16);
	}
};
_a16 = symbol16;
function createProviderRegistry(providers, { separator = ":" } = {}) {
	const registry = new DefaultProviderRegistry({ separator });
	for (const [id, provider] of Object.entries(providers)) registry.registerProvider({
		id,
		provider
	});
	return registry;
}
var experimental_createProviderRegistry = createProviderRegistry;
var DefaultProviderRegistry = class {
	constructor({ separator }) {
		this.providers = {};
		this.separator = separator;
	}
	registerProvider({ id, provider }) {
		this.providers[id] = provider;
	}
	getProvider(id) {
		const provider = this.providers[id];
		if (provider == null) throw new NoSuchProviderError({
			modelId: id,
			modelType: "languageModel",
			providerId: id,
			availableProviders: Object.keys(this.providers)
		});
		return provider;
	}
	splitId(id, modelType) {
		const index = id.indexOf(this.separator);
		if (index === -1) throw new NoSuchModelError({
			modelId: id,
			modelType,
			message: `Invalid ${modelType} id for registry: ${id} (must be in the format "providerId${this.separator}modelId")`
		});
		return [id.slice(0, index), id.slice(index + this.separator.length)];
	}
	languageModel(id) {
		var _a17, _b;
		const [providerId, modelId] = this.splitId(id, "languageModel");
		const model = (_b = (_a17 = this.getProvider(providerId)).languageModel) == null ? void 0 : _b.call(_a17, modelId);
		if (model == null) throw new NoSuchModelError({
			modelId: id,
			modelType: "languageModel"
		});
		return model;
	}
	textEmbeddingModel(id) {
		var _a17;
		const [providerId, modelId] = this.splitId(id, "textEmbeddingModel");
		const provider = this.getProvider(providerId);
		const model = (_a17 = provider.textEmbeddingModel) == null ? void 0 : _a17.call(provider, modelId);
		if (model == null) throw new NoSuchModelError({
			modelId: id,
			modelType: "textEmbeddingModel"
		});
		return model;
	}
	imageModel(id) {
		var _a17;
		const [providerId, modelId] = this.splitId(id, "imageModel");
		const provider = this.getProvider(providerId);
		const model = (_a17 = provider.imageModel) == null ? void 0 : _a17.call(provider, modelId);
		if (model == null) throw new NoSuchModelError({
			modelId: id,
			modelType: "imageModel"
		});
		return model;
	}
};
function tool(tool2) {
	return tool2;
}
var LATEST_PROTOCOL_VERSION = "2024-11-05";
var SUPPORTED_PROTOCOL_VERSIONS = [LATEST_PROTOCOL_VERSION, "2024-10-07"];
var ClientOrServerImplementationSchema = objectType({
	name: stringType(),
	version: stringType()
}).passthrough();
var BaseParamsSchema = objectType({ _meta: optionalType(objectType({}).passthrough()) }).passthrough();
var ResultSchema = BaseParamsSchema;
var RequestSchema = objectType({
	method: stringType(),
	params: optionalType(BaseParamsSchema)
});
var ServerCapabilitiesSchema = objectType({
	experimental: optionalType(objectType({}).passthrough()),
	logging: optionalType(objectType({}).passthrough()),
	prompts: optionalType(objectType({ listChanged: optionalType(booleanType()) }).passthrough()),
	resources: optionalType(objectType({
		subscribe: optionalType(booleanType()),
		listChanged: optionalType(booleanType())
	}).passthrough()),
	tools: optionalType(objectType({ listChanged: optionalType(booleanType()) }).passthrough())
}).passthrough();
var InitializeResultSchema = ResultSchema.extend({
	protocolVersion: stringType(),
	capabilities: ServerCapabilitiesSchema,
	serverInfo: ClientOrServerImplementationSchema,
	instructions: optionalType(stringType())
});
var PaginatedResultSchema = ResultSchema.extend({ nextCursor: optionalType(stringType()) });
var ToolSchema = objectType({
	name: stringType(),
	description: optionalType(stringType()),
	inputSchema: objectType({
		type: literalType("object"),
		properties: optionalType(objectType({}).passthrough())
	}).passthrough()
}).passthrough();
var ListToolsResultSchema = PaginatedResultSchema.extend({ tools: arrayType(ToolSchema) });
var TextContentSchema = objectType({
	type: literalType("text"),
	text: stringType()
}).passthrough();
var ImageContentSchema = objectType({
	type: literalType("image"),
	data: stringType().base64(),
	mimeType: stringType()
}).passthrough();
var ResourceContentsSchema = objectType({
	uri: stringType(),
	mimeType: optionalType(stringType())
}).passthrough();
var TextResourceContentsSchema = ResourceContentsSchema.extend({ text: stringType() });
var BlobResourceContentsSchema = ResourceContentsSchema.extend({ blob: stringType().base64() });
var EmbeddedResourceSchema = objectType({
	type: literalType("resource"),
	resource: unionType([TextResourceContentsSchema, BlobResourceContentsSchema])
}).passthrough();
var CallToolResultSchema = ResultSchema.extend({
	content: arrayType(unionType([
		TextContentSchema,
		ImageContentSchema,
		EmbeddedResourceSchema
	])),
	isError: booleanType().default(false).optional()
}).or(ResultSchema.extend({ toolResult: unknownType() }));
var JSONRPC_VERSION = "2.0";
var JSONRPCRequestSchema = objectType({
	jsonrpc: literalType(JSONRPC_VERSION),
	id: unionType([stringType(), numberType().int()])
}).merge(RequestSchema).strict();
var JSONRPCResponseSchema = objectType({
	jsonrpc: literalType(JSONRPC_VERSION),
	id: unionType([stringType(), numberType().int()]),
	result: ResultSchema
}).strict();
var JSONRPCErrorSchema = objectType({
	jsonrpc: literalType(JSONRPC_VERSION),
	id: unionType([stringType(), numberType().int()]),
	error: objectType({
		code: numberType().int(),
		message: stringType(),
		data: optionalType(unknownType())
	})
}).strict();
var JSONRPCNotificationSchema = objectType({ jsonrpc: literalType(JSONRPC_VERSION) }).merge(objectType({
	method: stringType(),
	params: optionalType(BaseParamsSchema)
})).strict();
var JSONRPCMessageSchema = unionType([
	JSONRPCRequestSchema,
	JSONRPCNotificationSchema,
	JSONRPCResponseSchema,
	JSONRPCErrorSchema
]);
var SseMCPTransport = class {
	constructor({ url, headers }) {
		this.connected = false;
		this.url = new URL(url);
		this.headers = headers;
	}
	async start() {
		return new Promise((resolve, reject) => {
			if (this.connected) return resolve();
			this.abortController = new AbortController();
			const establishConnection = async () => {
				var _a17, _b, _c;
				try {
					const headers = new Headers(this.headers);
					headers.set("Accept", "text/event-stream");
					const response = await fetch(this.url.href, {
						headers,
						signal: (_a17 = this.abortController) == null ? void 0 : _a17.signal
					});
					if (!response.ok || !response.body) {
						const error = new MCPClientError({ message: `MCP SSE Transport Error: ${response.status} ${response.statusText}` });
						(_b = this.onerror) == null || _b.call(this, error);
						return reject(error);
					}
					const stream = response.body.pipeThrough(new TextDecoderStream()).pipeThrough(createEventSourceParserStream());
					const reader = stream.getReader();
					const processEvents = async () => {
						var _a18, _b2, _c2;
						try {
							while (true) {
								const { done, value } = await reader.read();
								if (done) {
									if (this.connected) {
										this.connected = false;
										throw new MCPClientError({ message: "MCP SSE Transport Error: Connection closed unexpectedly" });
									}
									return;
								}
								const { event, data } = value;
								if (event === "endpoint") {
									this.endpoint = new URL(data, this.url);
									if (this.endpoint.origin !== this.url.origin) throw new MCPClientError({ message: `MCP SSE Transport Error: Endpoint origin does not match connection origin: ${this.endpoint.origin}` });
									this.connected = true;
									resolve();
								} else if (event === "message") try {
									const message = JSONRPCMessageSchema.parse(JSON.parse(data));
									(_a18 = this.onmessage) == null || _a18.call(this, message);
								} catch (error) {
									const e = new MCPClientError({
										message: "MCP SSE Transport Error: Failed to parse message",
										cause: error
									});
									(_b2 = this.onerror) == null || _b2.call(this, e);
								}
							}
						} catch (error) {
							if (error instanceof Error && error.name === "AbortError") return;
							(_c2 = this.onerror) == null || _c2.call(this, error);
							reject(error);
						}
					};
					this.sseConnection = { close: () => reader.cancel() };
					processEvents();
				} catch (error) {
					if (error instanceof Error && error.name === "AbortError") return;
					(_c = this.onerror) == null || _c.call(this, error);
					reject(error);
				}
			};
			establishConnection();
		});
	}
	async close() {
		var _a17, _b, _c;
		this.connected = false;
		(_a17 = this.sseConnection) == null || _a17.close();
		(_b = this.abortController) == null || _b.abort();
		(_c = this.onclose) == null || _c.call(this);
	}
	async send(message) {
		var _a17, _b, _c;
		if (!this.endpoint || !this.connected) throw new MCPClientError({ message: "MCP SSE Transport Error: Not connected" });
		try {
			const headers = new Headers(this.headers);
			headers.set("Content-Type", "application/json");
			const init = {
				method: "POST",
				headers,
				body: JSON.stringify(message),
				signal: (_a17 = this.abortController) == null ? void 0 : _a17.signal
			};
			const response = await fetch(this.endpoint, init);
			if (!response.ok) {
				const text2 = await response.text().catch(() => null);
				const error = new MCPClientError({ message: `MCP SSE Transport Error: POSTing to endpoint (HTTP ${response.status}): ${text2}` });
				(_b = this.onerror) == null || _b.call(this, error);
				return;
			}
		} catch (error) {
			(_c = this.onerror) == null || _c.call(this, error);
			return;
		}
	}
};
function createMcpTransport(config) {
	if (config.type !== "sse") throw new MCPClientError({ message: "Unsupported or invalid transport configuration. If you are using a custom transport, make sure it implements the MCPTransport interface." });
	return new SseMCPTransport(config);
}
function isCustomMcpTransport(transport) {
	return "start" in transport && typeof transport.start === "function" && "send" in transport && typeof transport.send === "function" && "close" in transport && typeof transport.close === "function";
}
var CLIENT_VERSION = "1.0.0";
async function createMCPClient(config) {
	const client = new MCPClient(config);
	await client.init();
	return client;
}
var MCPClient = class {
	constructor({ transport: transportConfig, name: name17 = "ai-sdk-mcp-client", onUncaughtError }) {
		this.requestMessageId = 0;
		this.responseHandlers = /* @__PURE__ */ new Map();
		this.serverCapabilities = {};
		this.isClosed = true;
		this.onUncaughtError = onUncaughtError;
		if (isCustomMcpTransport(transportConfig)) this.transport = transportConfig;
		else this.transport = createMcpTransport(transportConfig);
		this.transport.onclose = () => this.onClose();
		this.transport.onerror = (error) => this.onError(error);
		this.transport.onmessage = (message) => {
			if ("method" in message) {
				this.onError(new MCPClientError({ message: "Unsupported message type" }));
				return;
			}
			this.onResponse(message);
		};
		this.clientInfo = {
			name: name17,
			version: CLIENT_VERSION
		};
	}
	async init() {
		try {
			await this.transport.start();
			this.isClosed = false;
			const result = await this.request({
				request: {
					method: "initialize",
					params: {
						protocolVersion: LATEST_PROTOCOL_VERSION,
						capabilities: {},
						clientInfo: this.clientInfo
					}
				},
				resultSchema: InitializeResultSchema
			});
			if (result === void 0) throw new MCPClientError({ message: "Server sent invalid initialize result" });
			if (!SUPPORTED_PROTOCOL_VERSIONS.includes(result.protocolVersion)) throw new MCPClientError({ message: `Server's protocol version is not supported: ${result.protocolVersion}` });
			this.serverCapabilities = result.capabilities;
			await this.notification({ method: "notifications/initialized" });
			return this;
		} catch (error) {
			await this.close();
			throw error;
		}
	}
	async close() {
		var _a17;
		if (this.isClosed) return;
		await ((_a17 = this.transport) == null ? void 0 : _a17.close());
		this.onClose();
	}
	assertCapability(method) {
		switch (method) {
			case "initialize": break;
			case "tools/list":
			case "tools/call":
				if (!this.serverCapabilities.tools) throw new MCPClientError({ message: `Server does not support tools` });
				break;
			default: throw new MCPClientError({ message: `Unsupported method: ${method}` });
		}
	}
	async request({ request, resultSchema, options }) {
		return new Promise((resolve, reject) => {
			if (this.isClosed) return reject(new MCPClientError({ message: "Attempted to send a request from a closed client" }));
			this.assertCapability(request.method);
			const signal = options == null ? void 0 : options.signal;
			signal == null || signal.throwIfAborted();
			const messageId = this.requestMessageId++;
			const jsonrpcRequest = {
				...request,
				jsonrpc: "2.0",
				id: messageId
			};
			const cleanup = () => {
				this.responseHandlers.delete(messageId);
			};
			this.responseHandlers.set(messageId, (response) => {
				if (signal == null ? void 0 : signal.aborted) return reject(new MCPClientError({
					message: "Request was aborted",
					cause: signal.reason
				}));
				if (response instanceof Error) return reject(response);
				try {
					const result = resultSchema.parse(response.result);
					resolve(result);
				} catch (error) {
					const parseError = new MCPClientError({
						message: "Failed to parse server response",
						cause: error
					});
					reject(parseError);
				}
			});
			this.transport.send(jsonrpcRequest).catch((error) => {
				cleanup();
				reject(error);
			});
		});
	}
	async listTools({ params, options } = {}) {
		try {
			return this.request({
				request: {
					method: "tools/list",
					params
				},
				resultSchema: ListToolsResultSchema,
				options
			});
		} catch (error) {
			throw error;
		}
	}
	async callTool({ name: name17, args, options }) {
		try {
			return this.request({
				request: {
					method: "tools/call",
					params: {
						name: name17,
						arguments: args
					}
				},
				resultSchema: CallToolResultSchema,
				options: { signal: options == null ? void 0 : options.abortSignal }
			});
		} catch (error) {
			throw error;
		}
	}
	async notification(notification) {
		const jsonrpcNotification = {
			...notification,
			jsonrpc: "2.0"
		};
		await this.transport.send(jsonrpcNotification);
	}
	/**
	* Returns a set of AI SDK tools from the MCP server
	* @returns A record of tool names to their implementations
	*/
	async tools({ schemas = "automatic" } = {}) {
		var _a17;
		const tools = {};
		try {
			const listToolsResult = await this.listTools();
			for (const { name: name17, description, inputSchema } of listToolsResult.tools) {
				if (schemas !== "automatic" && !(name17 in schemas)) continue;
				const parameters = schemas === "automatic" ? jsonSchema({
					...inputSchema,
					properties: (_a17 = inputSchema.properties) != null ? _a17 : {},
					additionalProperties: false
				}) : schemas[name17].parameters;
				const self$1 = this;
				const toolWithExecute = tool({
					description,
					parameters,
					execute: async (args, options) => {
						var _a18;
						(_a18 = options == null ? void 0 : options.abortSignal) == null || _a18.throwIfAborted();
						return self$1.callTool({
							name: name17,
							args,
							options
						});
					}
				});
				tools[name17] = toolWithExecute;
			}
			return tools;
		} catch (error) {
			throw error;
		}
	}
	onClose() {
		if (this.isClosed) return;
		this.isClosed = true;
		const error = new MCPClientError({ message: "Connection closed" });
		for (const handler of this.responseHandlers.values()) handler(error);
		this.responseHandlers.clear();
	}
	onError(error) {
		if (this.onUncaughtError) this.onUncaughtError(error);
	}
	onResponse(response) {
		const messageId = Number(response.id);
		const handler = this.responseHandlers.get(messageId);
		if (handler === void 0) throw new MCPClientError({ message: `Protocol error: Received a response for an unknown message ID: ${JSON.stringify(response)}` });
		this.responseHandlers.delete(messageId);
		handler("result" in response ? response : new MCPClientError({
			message: response.error.message,
			cause: response.error
		}));
	}
};
function cosineSimilarity(vector1, vector2, options) {
	if (vector1.length !== vector2.length) throw new InvalidArgumentError$1({
		parameter: "vector1,vector2",
		value: {
			vector1Length: vector1.length,
			vector2Length: vector2.length
		},
		message: `Vectors must have the same length`
	});
	const n = vector1.length;
	if (n === 0) {
		if (options == null ? void 0 : options.throwErrorForEmptyVectors) throw new InvalidArgumentError$1({
			parameter: "vector1",
			value: vector1,
			message: "Vectors cannot be empty"
		});
		return 0;
	}
	let magnitudeSquared1 = 0;
	let magnitudeSquared2 = 0;
	let dotProduct = 0;
	for (let i = 0; i < n; i++) {
		const value1 = vector1[i];
		const value2 = vector2[i];
		magnitudeSquared1 += value1 * value1;
		magnitudeSquared2 += value2 * value2;
		dotProduct += value1 * value2;
	}
	return magnitudeSquared1 === 0 || magnitudeSquared2 === 0 ? 0 : dotProduct / (Math.sqrt(magnitudeSquared1) * Math.sqrt(magnitudeSquared2));
}
function simulateReadableStream({ chunks, initialDelayInMs = 0, chunkDelayInMs = 0, _internal }) {
	var _a17;
	const delay2 = (_a17 = _internal == null ? void 0 : _internal.delay) != null ? _a17 : delay;
	let index = 0;
	return new ReadableStream({ async pull(controller) {
		if (index < chunks.length) {
			await delay2(index === 0 ? initialDelayInMs : chunkDelayInMs);
			controller.enqueue(chunks[index++]);
		} else controller.close();
	} });
}
function AssistantResponse({ threadId, messageId }, process2) {
	const stream = new ReadableStream({
		async start(controller) {
			var _a17;
			const textEncoder = new TextEncoder();
			const sendMessage = (message) => {
				controller.enqueue(textEncoder.encode(formatAssistantStreamPart("assistant_message", message)));
			};
			const sendDataMessage = (message) => {
				controller.enqueue(textEncoder.encode(formatAssistantStreamPart("data_message", message)));
			};
			const sendError = (errorMessage) => {
				controller.enqueue(textEncoder.encode(formatAssistantStreamPart("error", errorMessage)));
			};
			const forwardStream = async (stream2) => {
				var _a18, _b;
				let result = void 0;
				for await (const value of stream2) switch (value.event) {
					case "thread.message.created": {
						controller.enqueue(textEncoder.encode(formatAssistantStreamPart("assistant_message", {
							id: value.data.id,
							role: "assistant",
							content: [{
								type: "text",
								text: { value: "" }
							}]
						})));
						break;
					}
					case "thread.message.delta": {
						const content = (_a18 = value.data.delta.content) == null ? void 0 : _a18[0];
						if ((content == null ? void 0 : content.type) === "text" && ((_b = content.text) == null ? void 0 : _b.value) != null) controller.enqueue(textEncoder.encode(formatAssistantStreamPart("text", content.text.value)));
						break;
					}
					case "thread.run.completed":
					case "thread.run.requires_action": {
						result = value.data;
						break;
					}
				}
				return result;
			};
			controller.enqueue(textEncoder.encode(formatAssistantStreamPart("assistant_control_data", {
				threadId,
				messageId
			})));
			try {
				await process2({
					sendMessage,
					sendDataMessage,
					forwardStream
				});
			} catch (error) {
				sendError((_a17 = error.message) != null ? _a17 : `${error}`);
			} finally {
				controller.close();
			}
		},
		pull(controller) {},
		cancel() {}
	});
	return new Response(stream, {
		status: 200,
		headers: { "Content-Type": "text/plain; charset=utf-8" }
	});
}
var langchain_adapter_exports = {};
__export(langchain_adapter_exports, {
	mergeIntoDataStream: () => mergeIntoDataStream,
	toDataStream: () => toDataStream,
	toDataStreamResponse: () => toDataStreamResponse
});
function createCallbacksTransformer(callbacks = {}) {
	const textEncoder = new TextEncoder();
	let aggregatedResponse = "";
	return new TransformStream({
		async start() {
			if (callbacks.onStart) await callbacks.onStart();
		},
		async transform(message, controller) {
			controller.enqueue(textEncoder.encode(message));
			aggregatedResponse += message;
			if (callbacks.onToken) await callbacks.onToken(message);
			if (callbacks.onText && typeof message === "string") await callbacks.onText(message);
		},
		async flush() {
			if (callbacks.onCompletion) await callbacks.onCompletion(aggregatedResponse);
			if (callbacks.onFinal) await callbacks.onFinal(aggregatedResponse);
		}
	});
}
function toDataStreamInternal(stream, callbacks) {
	return stream.pipeThrough(new TransformStream({ transform: async (value, controller) => {
		var _a17;
		if (typeof value === "string") {
			controller.enqueue(value);
			return;
		}
		if ("event" in value) {
			if (value.event === "on_chat_model_stream") forwardAIMessageChunk((_a17 = value.data) == null ? void 0 : _a17.chunk, controller);
			return;
		}
		forwardAIMessageChunk(value, controller);
	} })).pipeThrough(createCallbacksTransformer(callbacks)).pipeThrough(new TextDecoderStream()).pipeThrough(new TransformStream({ transform: async (chunk, controller) => {
		controller.enqueue(formatDataStreamPart("text", chunk));
	} }));
}
function toDataStream(stream, callbacks) {
	return toDataStreamInternal(stream, callbacks).pipeThrough(new TextEncoderStream());
}
function toDataStreamResponse(stream, options) {
	var _a17;
	const dataStream = toDataStreamInternal(stream, options == null ? void 0 : options.callbacks).pipeThrough(new TextEncoderStream());
	const data = options == null ? void 0 : options.data;
	const init = options == null ? void 0 : options.init;
	const responseStream = data ? mergeStreams(data.stream, dataStream) : dataStream;
	return new Response(responseStream, {
		status: (_a17 = init == null ? void 0 : init.status) != null ? _a17 : 200,
		statusText: init == null ? void 0 : init.statusText,
		headers: prepareResponseHeaders(init == null ? void 0 : init.headers, {
			contentType: "text/plain; charset=utf-8",
			dataStreamVersion: "v1"
		})
	});
}
function mergeIntoDataStream(stream, options) {
	options.dataStream.merge(toDataStreamInternal(stream, options.callbacks));
}
function forwardAIMessageChunk(chunk, controller) {
	if (typeof chunk.content === "string") controller.enqueue(chunk.content);
	else {
		const content = chunk.content;
		for (const item of content) if (item.type === "text") controller.enqueue(item.text);
	}
}
var llamaindex_adapter_exports = {};
__export(llamaindex_adapter_exports, {
	mergeIntoDataStream: () => mergeIntoDataStream2,
	toDataStream: () => toDataStream2,
	toDataStreamResponse: () => toDataStreamResponse2
});
function toDataStreamInternal2(stream, callbacks) {
	const trimStart = trimStartOfStream();
	return convertAsyncIteratorToReadableStream(stream[Symbol.asyncIterator]()).pipeThrough(new TransformStream({ async transform(message, controller) {
		controller.enqueue(trimStart(message.delta));
	} })).pipeThrough(createCallbacksTransformer(callbacks)).pipeThrough(new TextDecoderStream()).pipeThrough(new TransformStream({ transform: async (chunk, controller) => {
		controller.enqueue(formatDataStreamPart("text", chunk));
	} }));
}
function toDataStream2(stream, callbacks) {
	return toDataStreamInternal2(stream, callbacks).pipeThrough(new TextEncoderStream());
}
function toDataStreamResponse2(stream, options = {}) {
	var _a17;
	const { init, data, callbacks } = options;
	const dataStream = toDataStreamInternal2(stream, callbacks).pipeThrough(new TextEncoderStream());
	const responseStream = data ? mergeStreams(data.stream, dataStream) : dataStream;
	return new Response(responseStream, {
		status: (_a17 = init == null ? void 0 : init.status) != null ? _a17 : 200,
		statusText: init == null ? void 0 : init.statusText,
		headers: prepareResponseHeaders(init == null ? void 0 : init.headers, {
			contentType: "text/plain; charset=utf-8",
			dataStreamVersion: "v1"
		})
	});
}
function mergeIntoDataStream2(stream, options) {
	options.dataStream.merge(toDataStreamInternal2(stream, options.callbacks));
}
function trimStartOfStream() {
	let isStreamStart = true;
	return (text2) => {
		if (isStreamStart) {
			text2 = text2.trimStart();
			if (text2) isStreamStart = false;
		}
		return text2;
	};
}
var HANGING_STREAM_WARNING_TIME_MS = 15 * 1e3;
var StreamData = class {
	constructor() {
		this.encoder = new TextEncoder();
		this.controller = null;
		this.isClosed = false;
		this.warningTimeout = null;
		const self$1 = this;
		this.stream = new ReadableStream({
			start: async (controller) => {
				self$1.controller = controller;
				self$1.warningTimeout = setTimeout(() => {
					console.warn("The data stream is hanging. Did you forget to close it with `data.close()`?");
				}, HANGING_STREAM_WARNING_TIME_MS);
			},
			pull: (controller) => {},
			cancel: (reason) => {
				this.isClosed = true;
			}
		});
	}
	async close() {
		if (this.isClosed) throw new Error("Data Stream has already been closed.");
		if (!this.controller) throw new Error("Stream controller is not initialized.");
		this.controller.close();
		this.isClosed = true;
		if (this.warningTimeout) clearTimeout(this.warningTimeout);
	}
	append(value) {
		if (this.isClosed) throw new Error("Data Stream has already been closed.");
		if (!this.controller) throw new Error("Stream controller is not initialized.");
		this.controller.enqueue(this.encoder.encode(formatDataStreamPart("data", [value])));
	}
	appendMessageAnnotation(value) {
		if (this.isClosed) throw new Error("Data Stream has already been closed.");
		if (!this.controller) throw new Error("Stream controller is not initialized.");
		this.controller.enqueue(this.encoder.encode(formatDataStreamPart("message_annotations", [value])));
	}
};

//#endregion
export { AISDKError, APICallError, AssistantResponse, DownloadError, EmptyResponseBodyError, InvalidArgumentError$1 as InvalidArgumentError, InvalidDataContentError, InvalidMessageRoleError, InvalidPromptError, InvalidResponseDataError, InvalidStreamPartError, InvalidToolArgumentsError, JSONParseError, langchain_adapter_exports as LangChainAdapter, llamaindex_adapter_exports as LlamaIndexAdapter, LoadAPIKeyError, MCPClientError, MessageConversionError, NoContentGeneratedError, NoImageGeneratedError, NoObjectGeneratedError, NoOutputSpecifiedError, NoSuchModelError, NoSuchProviderError, NoSuchToolError, output_exports as Output, RetryError, StreamData, ToolCallRepairError, ToolExecutionError, TypeValidationError, UnsupportedFunctionalityError, appendClientMessage, appendResponseMessages, convertToCoreMessages, coreAssistantMessageSchema, coreMessageSchema, coreSystemMessageSchema, coreToolMessageSchema, coreUserMessageSchema, cosineSimilarity, createDataStream, createDataStreamResponse, createIdGenerator, createProviderRegistry, customProvider, defaultSettingsMiddleware, embed, embedMany, createMCPClient as experimental_createMCPClient, experimental_createProviderRegistry, experimental_customProvider, generateImage as experimental_generateImage, generateSpeech as experimental_generateSpeech, transcribe as experimental_transcribe, experimental_wrapLanguageModel, extractReasoningMiddleware, formatAssistantStreamPart, formatDataStreamPart, generateId, generateObject, generateText, jsonSchema, parseAssistantStreamPart, parseDataStreamPart, pipeDataStreamToResponse, processDataStream, processTextStream, simulateReadableStream, simulateStreamingMiddleware, smoothStream, streamObject, streamText, tool, wrapLanguageModel, zodSchema };
//# sourceMappingURL=ai.js.map