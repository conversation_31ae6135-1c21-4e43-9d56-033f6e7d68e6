{"version": 3, "file": "@radix-ui_react-dialog.js", "names": ["React", "React", "count", "useCallbackRef", "Slot", "isSlottable", "SlotClone", "Slottable", "Fragment", "mergeProps", "getElementRef", "Node", "Slot", "PORTAL_NAME", "Portal", "getElementRef", "React", "cbs", "options", "SideCar", "React", "Style", "React", "SideCar", "Fragment", "PortalPrimitive", "RemoveScroll", "Fragment"], "sources": ["../../@radix-ui/primitive/dist/index.mjs", "../../@radix-ui/react-compose-refs/dist/index.mjs", "../../@radix-ui/react-context/dist/index.mjs", "../../@radix-ui/react-use-layout-effect/dist/index.mjs", "../../@radix-ui/react-id/dist/index.mjs", "../../@radix-ui/react-use-callback-ref/dist/index.mjs", "../../@radix-ui/react-use-controllable-state/dist/index.mjs", "../../@radix-ui/react-primitive/node_modules/@radix-ui/react-slot/dist/index.mjs", "../../@radix-ui/react-primitive/dist/index.mjs", "../../@radix-ui/react-use-escape-keydown/dist/index.mjs", "../../@radix-ui/react-dismissable-layer/dist/index.mjs", "../../@radix-ui/react-focus-scope/dist/index.mjs", "../../@radix-ui/react-portal/dist/index.mjs", "../../@radix-ui/react-presence/dist/index.mjs", "../../@radix-ui/react-focus-guards/dist/index.mjs", "../../react-remove-scroll-bar/dist/es2015/constants.js", "../../use-callback-ref/dist/es2015/assignRef.js", "../../use-callback-ref/dist/es2015/useRef.js", "../../use-callback-ref/dist/es2015/useMergeRef.js", "../../use-sidecar/dist/es2015/medium.js", "../../use-sidecar/dist/es2015/exports.js", "../../react-remove-scroll/dist/es2015/medium.js", "../../react-remove-scroll/dist/es2015/UI.js", "../../get-nonce/dist/es2015/index.js", "../../react-style-singleton/dist/es2015/singleton.js", "../../react-style-singleton/dist/es2015/hook.js", "../../react-style-singleton/dist/es2015/component.js", "../../react-remove-scroll-bar/dist/es2015/utils.js", "../../react-remove-scroll-bar/dist/es2015/component.js", "../../react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../react-remove-scroll/dist/es2015/handleScroll.js", "../../react-remove-scroll/dist/es2015/SideEffect.js", "../../react-remove-scroll/dist/es2015/sidecar.js", "../../react-remove-scroll/dist/es2015/Combination.js", "../../aria-hidden/dist/es2015/index.js", "../../@radix-ui/react-dialog/node_modules/@radix-ui/react-slot/dist/index.mjs", "../../@radix-ui/react-dialog/dist/index.mjs"], "sourcesContent": ["// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/composeRefs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/createContext.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/useLayoutEffect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = Boolean(globalThis?.document) ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\"useId\".toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/useCallbackRef.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-controllable-state/src/useControllableState.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  }\n}) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue;\n        const value2 = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n        if (value2 !== prop) handleChange(value2);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const uncontrolledState = React.useState(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\nexport {\n  useControllableState\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/slot/src/Slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment, jsx } from \"react/jsx-runtime\";\nvar Slot = React.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = React.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (React.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return React.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ jsx(Fragment, { children });\n};\nfunction isSlottable(child) {\n  return React.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\nexport {\n  Root,\n  Slot,\n  Slottable\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/primitive/src/Primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-escape-keydown/src/useEscapeKeydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/dismissable-layer/src/DismissableLayer.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = React.createContext({\n  layers: /* @__PURE__ */ new Set(),\n  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n  branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = \"none\";\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n          pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n          ...props.style\n        },\n        onFocusCapture: composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )\n      }\n    );\n  }\n);\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = React.forwardRef((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref: composedRefs });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {\n  });\n  React.useEffect(() => {\n    const handlePointerDown = (event) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        let handleAndDispatchPointerDownOutsideEvent2 = function() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        };\n        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n        const eventDetail = { originalEvent: event };\n        if (event.pointerType === \"touch\") {\n          ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n          ownerDocument.addEventListener(\"click\", handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent2();\n        }\n      } else {\n        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n      ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside);\n  const isFocusInsideReactTreeRef = React.useRef(false);\n  React.useEffect(() => {\n    const handleFocus = (event) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener(\"focusin\", handleFocus);\n    return () => ownerDocument.removeEventListener(\"focusin\", handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\nexport {\n  Branch,\n  DismissableLayer,\n  DismissableLayerBranch,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/focus-scope/src/FocusScope.tsx\nimport * as React from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { jsx } from \"react/jsx-runtime\";\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = { bubbles: false, cancelable: true };\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = React.forwardRef((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    }\n  }).current;\n  React.useEffect(() => {\n    if (trapped) {\n      let handleFocusIn2 = function(event) {\n        if (focusScope.paused || !container) return;\n        const target = event.target;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }, handleFocusOut2 = function(event) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget;\n        if (relatedTarget === null) return;\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }, handleMutations2 = function(mutations) {\n        const focusedElement = document.activeElement;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      };\n      var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n      document.addEventListener(\"focusin\", handleFocusIn2);\n      document.addEventListener(\"focusout\", handleFocusOut2);\n      const mutationObserver = new MutationObserver(handleMutations2);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n      return () => {\n        document.removeEventListener(\"focusin\", handleFocusIn2);\n        document.removeEventListener(\"focusout\", handleFocusOut2);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n  const handleKeyDown = React.useCallback(\n    (event) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n      const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement;\n      if (isTabKey && focusedElement) {\n        const container2 = event.currentTarget;\n        const [first, last] = getTabbableEdges(container2);\n        const hasTabbableElementsInside = first && last;\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container2) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n  return /* @__PURE__ */ jsx(Primitive.div, { tabIndex: -1, ...scopeProps, ref: composedRefs, onKeyDown: handleKeyDown });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\nfunction getTabbableEdges(container) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last];\n}\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction findVisible(elements, container) {\n  for (const element of elements) {\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\nfunction isHidden(node, { upTo }) {\n  if (getComputedStyle(node).visibility === \"hidden\") return true;\n  while (node) {\n    if (upTo !== void 0 && node === upTo) return false;\n    if (getComputedStyle(node).display === \"none\") return true;\n    node = node.parentElement;\n  }\n  return false;\n}\nfunction isSelectableInput(element) {\n  return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    element.focus({ preventScroll: true });\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n  let stack = [];\n  return {\n    add(focusScope) {\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n    remove(focusScope) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    }\n  };\n}\nfunction arrayRemove(array, item) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\nfunction removeLinks(items) {\n  return items.filter((item) => item.tagName !== \"A\");\n}\nvar Root = FocusScope;\nexport {\n  FocusScope,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/portal/src/Portal.tsx\nimport * as React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PORTAL_NAME = \"Portal\";\nvar Portal = React.forwardRef((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || mounted && globalThis?.document?.body;\n  return container ? ReactDOM.createPortal(/* @__PURE__ */ jsx(Primitive.div, { ...portalProps, ref: forwardedRef }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\nexport {\n  Portal,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/presence/src/Presence.tsx\nimport * as React2 from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\n\n// packages/react/presence/src/useStateMachine.tsx\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : React2.Children.only(children);\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? React2.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = React2.useState();\n  const stylesRef = React2.useRef({});\n  const prevPresentRef = React2.useRef(present);\n  const prevAnimationNameRef = React2.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  React2.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: React2.useCallback((node2) => {\n      if (node2) stylesRef.current = getComputedStyle(node2);\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Presence\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/focus-guards/src/FocusGuards.tsx\nimport * as React from \"react\";\nvar count = 0;\nfunction FocusGuards(props) {\n  useFocusGuards();\n  return props.children;\n}\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n    document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n    count++;\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\nfunction createFocusGuard() {\n  const element = document.createElement(\"span\");\n  element.setAttribute(\"data-radix-focus-guard\", \"\");\n  element.tabIndex = 0;\n  element.style.outline = \"none\";\n  element.style.opacity = \"0\";\n  element.style.position = \"fixed\";\n  element.style.pointerEvents = \"none\";\n  return element;\n}\nvar Root = FocusGuards;\nexport {\n  FocusGuards,\n  Root,\n  useFocusGuards\n};\n//# sourceMappingURL=index.mjs.map\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n    // and script elements, as they have no impact on accessibility.\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n", "// packages/react/slot/src/Slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment, jsx } from \"react/jsx-runtime\";\nvar Slot = React.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = React.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (React.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return React.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ jsx(Fragment, { children });\n};\nfunction isSlottable(child) {\n  return React.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\nexport {\n  Root,\n  Slot,\n  Slottable\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/dialog/src/Dialog.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContext, createContextScope } from \"@radix-ui/react-context\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { FocusScope } from \"@radix-ui/react-focus-scope\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useFocusGuards } from \"@radix-ui/react-focus-guards\";\nimport { RemoveScroll } from \"react-remove-scroll\";\nimport { hideOthers } from \"aria-hidden\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true\n  } = props;\n  const triggerRef = React.useRef(null);\n  const contentRef = React.useRef(null);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange\n  });\n  return /* @__PURE__ */ jsx(\n    DialogProvider,\n    {\n      scope: __scopeDialog,\n      triggerRef,\n      contentRef,\n      contentId: useId(),\n      titleId: useId(),\n      descriptionId: useId(),\n      open,\n      onOpenChange: setOpen,\n      onOpenToggle: React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen]),\n      modal,\n      children\n    }\n  );\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: composeEventHandlers(props.onClick, context.onOpenToggle)\n      }\n    );\n  }\n);\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar DialogPortal = (props) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopeDialog, forceMount, children: React.Children.map(children, (child) => /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children: child }) })) });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(DialogOverlayImpl, { ...overlayProps, ref: forwardedRef }) }) : null;\n  }\n);\nDialogOverlay.displayName = OVERLAY_NAME;\nvar DialogOverlayImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      /* @__PURE__ */ jsx(RemoveScroll, { as: Slot, allowPinchZoom: true, shards: [context.contentRef], children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          \"data-state\": getState(context.open),\n          ...overlayProps,\n          ref: forwardedRef,\n          style: { pointerEvents: \"auto\", ...overlayProps.style }\n        }\n      ) })\n    );\n  }\n);\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: context.modal ? /* @__PURE__ */ jsx(DialogContentModal, { ...contentProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(DialogContentNonModal, { ...contentProps, ref: forwardedRef }) });\n  }\n);\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      DialogContentImpl,\n      {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault()\n        )\n      }\n    );\n  }\n);\nvar DialogContentNonModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n    return /* @__PURE__ */ jsx(\n      DialogContentImpl,\n      {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event) => {\n          props.onCloseAutoFocus?.(event);\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            event.preventDefault();\n          }\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event) => {\n          props.onInteractOutside?.(event);\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === \"pointerdown\") {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n          const target = event.target;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n          if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }\n      }\n    );\n  }\n);\nvar DialogContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    useFocusGuards();\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        FocusScope,\n        {\n          asChild: true,\n          loop: true,\n          trapped: trapFocus,\n          onMountAutoFocus: onOpenAutoFocus,\n          onUnmountAutoFocus: onCloseAutoFocus,\n          children: /* @__PURE__ */ jsx(\n            DismissableLayer,\n            {\n              role: \"dialog\",\n              id: context.contentId,\n              \"aria-describedby\": context.descriptionId,\n              \"aria-labelledby\": context.titleId,\n              \"data-state\": getState(context.open),\n              ...contentProps,\n              ref: composedRefs,\n              onDismiss: () => context.onOpenChange(false)\n            }\n          )\n        }\n      ),\n      /* @__PURE__ */ jsxs(Fragment, { children: [\n        /* @__PURE__ */ jsx(TitleWarning, { titleId: context.titleId }),\n        /* @__PURE__ */ jsx(DescriptionWarning, { contentRef, descriptionId: context.descriptionId })\n      ] })\n    ] });\n  }\n);\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ jsx(Primitive.h2, { id: context.titleId, ...titleProps, ref: forwardedRef });\n  }\n);\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ jsx(Primitive.p, { id: context.descriptionId, ...descriptionProps, ref: forwardedRef });\n  }\n);\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, () => context.onOpenChange(false))\n      }\n    );\n  }\n);\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n  return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n  return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n  return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\nexport {\n  Close,\n  Content,\n  Description,\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n  Overlay,\n  Portal,\n  Root,\n  Title,\n  Trigger,\n  WarningProvider,\n  createDialogScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "x_google_ignoreList": [7, 35], "mappings": ";;;;;;;;;;AACA,SAAS,qBAAqB,sBAAsB,iBAAiB,EAAE,2BAA2B,MAAM,GAAG,CAAE,GAAE;AAC7G,QAAO,SAAS,YAAY,OAAO;AACjC,yBAAuB,MAAM;AAC7B,MAAI,6BAA6B,UAAU,MAAM,iBAC/C,QAAO,kBAAkB,MAAM;CAElC;AACF;;;;;ACND,SAAS,OAAO,KAAK,OAAO;AAC1B,YAAW,QAAQ,WACjB,QAAO,IAAI,MAAM;UACR,QAAQ,QAAQ,aAAa,EACtC,KAAI,UAAU;AAEjB;AACD,SAAS,YAAY,GAAG,MAAM;AAC5B,QAAO,CAAC,SAAS;EACf,IAAI,aAAa;EACjB,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;GACjC,MAAM,UAAU,OAAO,KAAK,KAAK;AACjC,QAAK,qBAAqB,WAAW,WACnC,cAAa;AAEf,UAAO;EACR,EAAC;AACF,MAAI,WACF,QAAO,MAAM;AACX,QAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;IACxC,MAAM,UAAU,SAAS;AACzB,eAAW,WAAW,WACpB,UAAS;QAET,QAAO,KAAK,IAAI,KAAK;GAExB;EACF;CAEJ;AACF;AACD,SAAS,gBAAgB,GAAG,MAAM;AAChC,QAAO,aAAM,YAAY,YAAY,GAAG,KAAK,EAAE,KAAK;AACrD;;;;;AChCD,SAAS,eAAe,mBAAmB,gBAAgB;CACzD,MAAM,UAAU,aAAM,cAAc,eAAe;CACnD,MAAM,WAAW,CAAC,UAAU;EAC1B,MAAM,EAAE,SAAU,GAAG,SAAS,GAAG;EACjC,MAAM,QAAQ,aAAM,QAAQ,MAAM,SAAS,OAAO,OAAO,QAAQ,CAAC;AAClE,yBAAuB,4BAAI,QAAQ,UAAU;GAAE;GAAO;EAAU,EAAC;CAClE;AACD,UAAS,cAAc,oBAAoB;CAC3C,SAAS,YAAY,cAAc;EACjC,MAAM,UAAU,aAAM,WAAW,QAAQ;AACzC,MAAI,QAAS,QAAO;AACpB,MAAI,wBAAwB,EAAG,QAAO;AACtC,QAAM,IAAI,OAAO,IAAI,aAAa,2BAA2B,kBAAkB;CAChF;AACD,QAAO,CAAC,UAAU,WAAY;AAC/B;AACD,SAAS,mBAAmB,WAAW,yBAAyB,CAAE,GAAE;CAClE,IAAI,kBAAkB,CAAE;CACxB,SAAS,eAAe,mBAAmB,gBAAgB;EACzD,MAAM,cAAc,aAAM,cAAc,eAAe;EACvD,MAAM,QAAQ,gBAAgB;AAC9B,oBAAkB,CAAC,GAAG,iBAAiB,cAAe;EACtD,MAAM,WAAW,CAAC,UAAU;GAC1B,MAAM,EAAE,OAAO,SAAU,GAAG,SAAS,GAAG;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,QAAQ,aAAM,QAAQ,MAAM,SAAS,OAAO,OAAO,QAAQ,CAAC;AAClE,0BAAuB,4BAAI,QAAQ,UAAU;IAAE;IAAO;GAAU,EAAC;EAClE;AACD,WAAS,cAAc,oBAAoB;EAC3C,SAAS,YAAY,cAAc,OAAO;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,UAAU,aAAM,WAAW,QAAQ;AACzC,OAAI,QAAS,QAAO;AACpB,OAAI,wBAAwB,EAAG,QAAO;AACtC,SAAM,IAAI,OAAO,IAAI,aAAa,2BAA2B,kBAAkB;EAChF;AACD,SAAO,CAAC,UAAU,WAAY;CAC/B;CACD,MAAM,cAAc,MAAM;EACxB,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,UAAO,aAAM,cAAc,eAAe;EAC3C,EAAC;AACF,SAAO,SAAS,SAAS,OAAO;GAC9B,MAAM,WAAW,QAAQ,cAAc;AACvC,UAAO,aAAM,QACX,OAAO,IAAI,SAAS,cAAc;IAAE,GAAG;KAAQ,YAAY;GAAU,EAAE,IACvE,CAAC,OAAO,QAAS,EAClB;EACF;CACF;AACD,aAAY,YAAY;AACxB,QAAO,CAAC,gBAAgB,qBAAqB,aAAa,GAAG,uBAAuB,AAAC;AACtF;AACD,SAAS,qBAAqB,GAAG,QAAQ;CACvC,MAAM,YAAY,OAAO;AACzB,KAAI,OAAO,WAAW,EAAG,QAAO;CAChC,MAAM,cAAc,MAAM;EACxB,MAAM,aAAa,OAAO,IAAI,CAAC,kBAAkB;GAC/C,UAAU,cAAc;GACxB,WAAW,aAAa;EACzB,GAAE;AACH,SAAO,SAAS,kBAAkB,gBAAgB;GAChD,MAAM,aAAa,WAAW,OAAO,CAAC,aAAa,EAAE,UAAU,WAAW,KAAK;IAC7E,MAAM,aAAa,SAAS,eAAe;IAC3C,MAAM,eAAe,YAAY,SAAS;AAC1C,WAAO;KAAE,GAAG;KAAa,GAAG;IAAc;GAC3C,GAAE,CAAE,EAAC;AACN,UAAO,aAAM,QAAQ,OAAO,IAAI,SAAS,UAAU,cAAc,WAAY,IAAG,CAAC,UAAW,EAAC;EAC9F;CACF;AACD,aAAY,YAAY,UAAU;AAClC,QAAO;AACR;;;;ACzED,IAAI,mBAAmB,QAAQ,YAAY,SAAS,GAAGA,aAAM,kBAAkB,MAAM,CACpF;;;;ACAD,IAAI,aAAaC,aAAM,QAAQ,UAAU,MAAM,WAAW;AAC1D,IAAIC,UAAQ;AACZ,SAAS,MAAM,iBAAiB;CAC9B,MAAM,CAAC,IAAI,MAAM,GAAG,aAAM,SAAS,YAAY,CAAC;AAChD,kBAAgB,MAAM;AACpB,OAAK,gBAAiB,OAAM,CAAC,YAAY,WAAW,OAAOA,UAAQ,CAAC;CACrE,GAAE,CAAC,eAAgB,EAAC;AACrB,QAAO,oBAAoB,MAAM,QAAQ,OAAO;AACjD;;;;ACTD,SAASC,iBAAe,UAAU;CAChC,MAAM,cAAc,aAAM,OAAO,SAAS;AAC1C,cAAM,UAAU,MAAM;AACpB,cAAY,UAAU;CACvB,EAAC;AACF,QAAO,aAAM,QAAQ,MAAM,CAAC,GAAG,SAAS,YAAY,UAAU,GAAG,KAAK,EAAE,CAAE,EAAC;AAC5E;;;;ACLD,SAAS,qBAAqB,EAC5B,MACA,aACA,WAAW,MAAM,CAChB,GACF,EAAE;CACD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,qBAAqB;EAAE;EAAa;CAAU,EAAC;CAC/F,MAAM,eAAe,cAAc;CACnC,MAAM,QAAQ,eAAe,OAAO;CACpC,MAAM,eAAe,iBAAe,SAAS;CAC7C,MAAM,WAAW,aAAM,YACrB,CAAC,cAAc;AACb,MAAI,cAAc;GAChB,MAAM,SAAS;GACf,MAAM,gBAAgB,cAAc,aAAa,OAAO,KAAK,GAAG;AAChE,OAAI,WAAW,KAAM,cAAa,OAAO;EAC1C,MACC,qBAAoB,UAAU;CAEjC,GACD;EAAC;EAAc;EAAM;EAAqB;CAAa,EACxD;AACD,QAAO,CAAC,OAAO,QAAS;AACzB;AACD,SAAS,qBAAqB,EAC5B,aACA,UACD,EAAE;CACD,MAAM,oBAAoB,aAAM,SAAS,YAAY;CACrD,MAAM,CAAC,MAAM,GAAG;CAChB,MAAM,eAAe,aAAM,OAAO,MAAM;CACxC,MAAM,eAAe,iBAAe,SAAS;AAC7C,cAAM,UAAU,MAAM;AACpB,MAAI,aAAa,YAAY,OAAO;AAClC,gBAAa,MAAM;AACnB,gBAAa,UAAU;EACxB;CACF,GAAE;EAAC;EAAO;EAAc;CAAa,EAAC;AACvC,QAAO;AACR;;;;ACtCD,IAAIC,SAAO,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACnD,MAAM,EAAE,SAAU,GAAG,WAAW,GAAG;CACnC,MAAM,gBAAgB,aAAM,SAAS,QAAQ,SAAS;CACtD,MAAM,YAAY,cAAc,KAAKC,cAAY;AACjD,KAAI,WAAW;EACb,MAAM,aAAa,UAAU,MAAM;EACnC,MAAM,cAAc,cAAc,IAAI,CAAC,UAAU;AAC/C,OAAI,UAAU,WAAW;AACvB,QAAI,aAAM,SAAS,MAAM,WAAW,GAAG,EAAG,QAAO,aAAM,SAAS,KAAK,KAAK;AAC1E,WAAO,aAAM,eAAe,WAAW,GAAG,WAAW,MAAM,WAAW;GACvE,MACC,QAAO;EAEV,EAAC;AACF,yBAAuB,4BAAIC,aAAW;GAAE,GAAG;GAAW,KAAK;GAAc,UAAU,aAAM,eAAe,WAAW,GAAG,aAAM,aAAa,iBAAiB,GAAG,YAAY,GAAG;EAAM,EAAC;CACpL;AACD,wBAAuB,4BAAIA,aAAW;EAAE,GAAG;EAAW,KAAK;EAAc;CAAU,EAAC;AACrF,EAAC;AACF,OAAK,cAAc;AACnB,IAAIA,cAAY,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACxD,MAAM,EAAE,SAAU,GAAG,WAAW,GAAG;AACnC,KAAI,aAAM,eAAe,SAAS,EAAE;EAClC,MAAM,cAAc,gBAAc,SAAS;AAC3C,SAAO,aAAM,aAAa,UAAU;GAClC,GAAG,aAAW,WAAW,SAAS,MAAM;GAExC,KAAK,eAAe,YAAY,cAAc,YAAY,GAAG;EAC9D,EAAC;CACH;AACD,QAAO,aAAM,SAAS,MAAM,SAAS,GAAG,IAAI,aAAM,SAAS,KAAK,KAAK,GAAG;AACzE,EAAC;AACF,YAAU,cAAc;AACxB,IAAIC,cAAY,CAAC,EAAE,UAAU,KAAK;AAChC,wBAAuB,4BAAIC,6BAAU,EAAE,SAAU,EAAC;AACnD;AACD,SAASH,cAAY,OAAO;AAC1B,QAAO,aAAM,eAAe,MAAM,IAAI,MAAM,SAASE;AACtD;AACD,SAASE,aAAW,WAAW,YAAY;CACzC,MAAM,gBAAgB,EAAE,GAAG,WAAY;AACvC,MAAK,MAAM,YAAY,YAAY;EACjC,MAAM,gBAAgB,UAAU;EAChC,MAAM,iBAAiB,WAAW;EAClC,MAAM,YAAY,WAAW,KAAK,SAAS;AAC3C,MAAI,WACF;OAAI,iBAAiB,eACnB,eAAc,YAAY,CAAC,GAAG,SAAS;AACrC,mBAAe,GAAG,KAAK;AACvB,kBAAc,GAAG,KAAK;GACvB;YACQ,cACT,eAAc,YAAY;EAC3B,WACQ,aAAa,QACtB,eAAc,YAAY;GAAE,GAAG;GAAe,GAAG;EAAgB;WACxD,aAAa,YACtB,eAAc,YAAY,CAAC,eAAe,cAAe,EAAC,OAAO,QAAQ,CAAC,KAAK,IAAI;CAEtF;AACD,QAAO;EAAE,GAAG;EAAW,GAAG;CAAe;AAC1C;AACD,SAASC,gBAAc,SAAS;CAC9B,IAAI,SAAS,OAAO,yBAAyB,QAAQ,OAAO,MAAM,EAAE;CACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,KAAI,QACF,QAAO,QAAQ;AAEjB,UAAS,OAAO,yBAAyB,SAAS,MAAM,EAAE;AAC1D,WAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,KAAI,QACF,QAAO,QAAQ,MAAM;AAEvB,QAAO,QAAQ,MAAM,OAAO,QAAQ;AACrC;;;;;ACxED,IAAI,QAAQ;CACV;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACD;AACD,IAAI,YAAY,MAAM,OAAO,CAAC,WAAW,SAAS;CAChD,MAAMC,SAAO,aAAM,WAAW,CAAC,OAAO,iBAAiB;EACrD,MAAM,EAAE,QAAS,GAAG,gBAAgB,GAAG;EACvC,MAAM,OAAO,UAAUC,SAAO;AAC9B,aAAW,WAAW,YACpB,QAAO,OAAO,IAAI,WAAW,IAAI;AAEnC,yBAAuB,4BAAI,MAAM;GAAE,GAAG;GAAgB,KAAK;EAAc,EAAC;CAC3E,EAAC;AACF,QAAK,eAAe,YAAY;AAChC,QAAO;EAAE,GAAG;GAAY,OAAOD;CAAM;AACtC,GAAE,CAAE,EAAC;AACN,SAAS,4BAA4B,QAAQ,OAAO;AAClD,KAAI,OAAQ,oBAAS,UAAU,MAAM,OAAO,cAAc,MAAM,CAAC;AAClE;;;;AClCD,SAAS,iBAAiB,qBAAqB,gBAAgB,YAAY,UAAU;CACnF,MAAM,kBAAkB,iBAAe,oBAAoB;AAC3D,cAAM,UAAU,MAAM;EACpB,MAAM,gBAAgB,CAAC,UAAU;AAC/B,OAAI,MAAM,QAAQ,SAChB,iBAAgB,MAAM;EAEzB;AACD,gBAAc,iBAAiB,WAAW,eAAe,EAAE,SAAS,KAAM,EAAC;AAC3E,SAAO,MAAM,cAAc,oBAAoB,WAAW,eAAe,EAAE,SAAS,KAAM,EAAC;CAC5F,GAAE,CAAC,iBAAiB,aAAc,EAAC;AACrC;;;;ACJD,IAAI,yBAAyB;AAC7B,IAAI,iBAAiB;AACrB,IAAI,uBAAuB;AAC3B,IAAI,gBAAgB;AACpB,IAAI;AACJ,IAAI,0BAA0B,aAAM,cAAc;CAChD,wBAAwB,IAAI;CAC5B,wDAAwD,IAAI;CAC5D,0BAA0B,IAAI;AAC/B,EAAC;AACF,IAAI,mBAAmB,aAAM,WAC3B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,8BAA8B,OAC9B,iBACA,sBACA,gBACA,mBACA,UACA,GAAG,YACJ,GAAG;CACJ,MAAM,UAAU,aAAM,WAAW,wBAAwB;CACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,aAAM,SAAS,KAAK;CAC5C,MAAM,gBAAgB,MAAM,iBAAiB,YAAY;CACzD,MAAM,GAAG,MAAM,GAAG,aAAM,SAAS,CAAE,EAAC;CACpC,MAAM,eAAe,gBAAgB,cAAc,CAAC,UAAU,QAAQ,MAAM,CAAC;CAC7E,MAAM,SAAS,MAAM,KAAK,QAAQ,OAAO;CACzC,MAAM,CAAC,6CAA6C,GAAG,CAAC,GAAG,QAAQ,sCAAuC,EAAC,MAAM,GAAG;CACpH,MAAM,oDAAoD,OAAO,QAAQ,6CAA6C;CACtH,MAAM,QAAQ,OAAO,OAAO,QAAQ,KAAK,GAAG;CAC5C,MAAM,8BAA8B,QAAQ,uCAAuC,OAAO;CAC1F,MAAM,yBAAyB,SAAS;CACxC,MAAM,qBAAqB,sBAAsB,CAAC,UAAU;EAC1D,MAAM,SAAS,MAAM;EACrB,MAAM,wBAAwB,CAAC,GAAG,QAAQ,QAAS,EAAC,KAAK,CAAC,WAAW,OAAO,SAAS,OAAO,CAAC;AAC7F,OAAK,0BAA0B,sBAAuB;AACtD,yBAAuB,MAAM;AAC7B,sBAAoB,MAAM;AAC1B,OAAK,MAAM,iBAAkB,cAAa;CAC3C,GAAE,cAAc;CACjB,MAAM,eAAe,gBAAgB,CAAC,UAAU;EAC9C,MAAM,SAAS,MAAM;EACrB,MAAM,kBAAkB,CAAC,GAAG,QAAQ,QAAS,EAAC,KAAK,CAAC,WAAW,OAAO,SAAS,OAAO,CAAC;AACvF,MAAI,gBAAiB;AACrB,mBAAiB,MAAM;AACvB,sBAAoB,MAAM;AAC1B,OAAK,MAAM,iBAAkB,cAAa;CAC3C,GAAE,cAAc;AACjB,kBAAiB,CAAC,UAAU;EAC1B,MAAM,iBAAiB,UAAU,QAAQ,OAAO,OAAO;AACvD,OAAK,eAAgB;AACrB,oBAAkB,MAAM;AACxB,OAAK,MAAM,oBAAoB,WAAW;AACxC,SAAM,gBAAgB;AACtB,cAAW;EACZ;CACF,GAAE,cAAc;AACjB,cAAM,UAAU,MAAM;AACpB,OAAK,KAAM;AACX,MAAI,6BAA6B;AAC/B,OAAI,QAAQ,uCAAuC,SAAS,GAAG;AAC7D,gCAA4B,cAAc,KAAK,MAAM;AACrD,kBAAc,KAAK,MAAM,gBAAgB;GAC1C;AACD,WAAQ,uCAAuC,IAAI,KAAK;EACzD;AACD,UAAQ,OAAO,IAAI,KAAK;AACxB,kBAAgB;AAChB,SAAO,MAAM;AACX,OAAI,+BAA+B,QAAQ,uCAAuC,SAAS,EACzF,eAAc,KAAK,MAAM,gBAAgB;EAE5C;CACF,GAAE;EAAC;EAAM;EAAe;EAA6B;CAAQ,EAAC;AAC/D,cAAM,UAAU,MAAM;AACpB,SAAO,MAAM;AACX,QAAK,KAAM;AACX,WAAQ,OAAO,OAAO,KAAK;AAC3B,WAAQ,uCAAuC,OAAO,KAAK;AAC3D,mBAAgB;EACjB;CACF,GAAE,CAAC,MAAM,OAAQ,EAAC;AACnB,cAAM,UAAU,MAAM;EACpB,MAAM,eAAe,MAAM,MAAM,CAAE,EAAC;AACpC,WAAS,iBAAiB,gBAAgB,aAAa;AACvD,SAAO,MAAM,SAAS,oBAAoB,gBAAgB,aAAa;CACxE,GAAE,CAAE,EAAC;AACN,wBAAuB,4BACrB,UAAU,KACV;EACE,GAAG;EACH,KAAK;EACL,OAAO;GACL,eAAe,8BAA8B,yBAAyB,SAAS,cAAc;GAC7F,GAAG,MAAM;EACV;EACD,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,eAAe;EACvF,eAAe,qBAAqB,MAAM,eAAe,aAAa,cAAc;EACpF,sBAAsB,qBACpB,MAAM,sBACN,mBAAmB,qBACpB;CACF,EACF;AACF,EACF;AACD,iBAAiB,cAAc;AAC/B,IAAI,cAAc;AAClB,IAAI,yBAAyB,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACrE,MAAM,UAAU,aAAM,WAAW,wBAAwB;CACzD,MAAM,MAAM,aAAM,OAAO,KAAK;CAC9B,MAAM,eAAe,gBAAgB,cAAc,IAAI;AACvD,cAAM,UAAU,MAAM;EACpB,MAAM,OAAO,IAAI;AACjB,MAAI,MAAM;AACR,WAAQ,SAAS,IAAI,KAAK;AAC1B,UAAO,MAAM;AACX,YAAQ,SAAS,OAAO,KAAK;GAC9B;EACF;CACF,GAAE,CAAC,QAAQ,QAAS,EAAC;AACtB,wBAAuB,4BAAI,UAAU,KAAK;EAAE,GAAG;EAAO,KAAK;CAAc,EAAC;AAC3E,EAAC;AACF,uBAAuB,cAAc;AACrC,SAAS,sBAAsB,sBAAsB,gBAAgB,YAAY,UAAU;CACzF,MAAM,2BAA2B,iBAAe,qBAAqB;CACrE,MAAM,8BAA8B,aAAM,OAAO,MAAM;CACvD,MAAM,iBAAiB,aAAM,OAAO,MAAM,CACzC,EAAC;AACF,cAAM,UAAU,MAAM;EACpB,MAAM,oBAAoB,CAAC,UAAU;AACnC,OAAI,MAAM,WAAW,4BAA4B,SAAS;IACxD,IAAI,4CAA4C,WAAW;AACzD,kCACE,sBACA,0BACA,aACA,EAAE,UAAU,KAAM,EACnB;IACF;IACD,IAAI,2CAA2C;IAC/C,MAAM,cAAc,EAAE,eAAe,MAAO;AAC5C,QAAI,MAAM,gBAAgB,SAAS;AACjC,mBAAc,oBAAoB,SAAS,eAAe,QAAQ;AAClE,oBAAe,UAAU;AACzB,mBAAc,iBAAiB,SAAS,eAAe,SAAS,EAAE,MAAM,KAAM,EAAC;IAChF,MACC,4CAA2C;GAE9C,MACC,eAAc,oBAAoB,SAAS,eAAe,QAAQ;AAEpE,+BAA4B,UAAU;EACvC;EACD,MAAM,UAAU,OAAO,WAAW,MAAM;AACtC,iBAAc,iBAAiB,eAAe,kBAAkB;EACjE,GAAE,EAAE;AACL,SAAO,MAAM;AACX,UAAO,aAAa,QAAQ;AAC5B,iBAAc,oBAAoB,eAAe,kBAAkB;AACnE,iBAAc,oBAAoB,SAAS,eAAe,QAAQ;EACnE;CACF,GAAE,CAAC,eAAe,wBAAyB,EAAC;AAC7C,QAAO,EAEL,sBAAsB,MAAM,4BAA4B,UAAU,KACnE;AACF;AACD,SAAS,gBAAgB,gBAAgB,gBAAgB,YAAY,UAAU;CAC7E,MAAM,qBAAqB,iBAAe,eAAe;CACzD,MAAM,4BAA4B,aAAM,OAAO,MAAM;AACrD,cAAM,UAAU,MAAM;EACpB,MAAM,cAAc,CAAC,UAAU;AAC7B,OAAI,MAAM,WAAW,0BAA0B,SAAS;IACtD,MAAM,cAAc,EAAE,eAAe,MAAO;AAC5C,iCAA6B,eAAe,oBAAoB,aAAa,EAC3E,UAAU,MACX,EAAC;GACH;EACF;AACD,gBAAc,iBAAiB,WAAW,YAAY;AACtD,SAAO,MAAM,cAAc,oBAAoB,WAAW,YAAY;CACvE,GAAE,CAAC,eAAe,kBAAmB,EAAC;AACvC,QAAO;EACL,gBAAgB,MAAM,0BAA0B,UAAU;EAC1D,eAAe,MAAM,0BAA0B,UAAU;CAC1D;AACF;AACD,SAAS,iBAAiB;CACxB,MAAM,QAAQ,IAAI,YAAY;AAC9B,UAAS,cAAc,MAAM;AAC9B;AACD,SAAS,6BAA6B,MAAM,SAAS,QAAQ,EAAE,UAAU,EAAE;CACzE,MAAM,SAAS,OAAO,cAAc;CACpC,MAAM,QAAQ,IAAI,YAAY,MAAM;EAAE,SAAS;EAAO,YAAY;EAAM;CAAQ;AAChF,KAAI,QAAS,QAAO,iBAAiB,MAAM,SAAS,EAAE,MAAM,KAAM,EAAC;AACnE,KAAI,SACF,6BAA4B,QAAQ,MAAM;KAE1C,QAAO,cAAc,MAAM;AAE9B;;;;AC3MD,IAAI,qBAAqB;AACzB,IAAI,uBAAuB;AAC3B,IAAI,gBAAgB;CAAE,SAAS;CAAO,YAAY;AAAM;AACxD,IAAI,mBAAmB;AACvB,IAAI,aAAa,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACzD,MAAM,EACJ,OAAO,OACP,UAAU,OACV,kBAAkB,sBAClB,oBAAoB,uBACpB,GAAG,YACJ,GAAG;CACJ,MAAM,CAAC,WAAW,aAAa,GAAG,aAAM,SAAS,KAAK;CACtD,MAAM,mBAAmB,iBAAe,qBAAqB;CAC7D,MAAM,qBAAqB,iBAAe,uBAAuB;CACjE,MAAM,wBAAwB,aAAM,OAAO,KAAK;CAChD,MAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,aAAa,KAAK,CAAC;CAChF,MAAM,aAAa,aAAM,OAAO;EAC9B,QAAQ;EACR,QAAQ;AACN,QAAK,SAAS;EACf;EACD,SAAS;AACP,QAAK,SAAS;EACf;CACF,EAAC,CAAC;AACH,cAAM,UAAU,MAAM;AACpB,MAAI,SAAS;GACX,IAAI,iBAAiB,SAAS,OAAO;AACnC,QAAI,WAAW,WAAW,UAAW;IACrC,MAAM,SAAS,MAAM;AACrB,QAAI,UAAU,SAAS,OAAO,CAC5B,uBAAsB,UAAU;QAEhC,OAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAM,EAAC;GAEzD,GAAE,kBAAkB,SAAS,OAAO;AACnC,QAAI,WAAW,WAAW,UAAW;IACrC,MAAM,gBAAgB,MAAM;AAC5B,QAAI,kBAAkB,KAAM;AAC5B,SAAK,UAAU,SAAS,cAAc,CACpC,OAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAM,EAAC;GAEzD,GAAE,mBAAmB,SAAS,WAAW;IACxC,MAAM,iBAAiB,SAAS;AAChC,QAAI,mBAAmB,SAAS,KAAM;AACtC,SAAK,MAAM,YAAY,UACrB,KAAI,SAAS,aAAa,SAAS,EAAG,OAAM,UAAU;GAEzD;GACD,IAAI,gBAAgB,gBAAgB,iBAAiB,iBAAiB,kBAAkB;AACxF,YAAS,iBAAiB,WAAW,eAAe;AACpD,YAAS,iBAAiB,YAAY,gBAAgB;GACtD,MAAM,mBAAmB,IAAI,iBAAiB;AAC9C,OAAI,UAAW,kBAAiB,QAAQ,WAAW;IAAE,WAAW;IAAM,SAAS;GAAM,EAAC;AACtF,UAAO,MAAM;AACX,aAAS,oBAAoB,WAAW,eAAe;AACvD,aAAS,oBAAoB,YAAY,gBAAgB;AACzD,qBAAiB,YAAY;GAC9B;EACF;CACF,GAAE;EAAC;EAAS;EAAW,WAAW;CAAO,EAAC;AAC3C,cAAM,UAAU,MAAM;AACpB,MAAI,WAAW;AACb,oBAAiB,IAAI,WAAW;GAChC,MAAM,2BAA2B,SAAS;GAC1C,MAAM,sBAAsB,UAAU,SAAS,yBAAyB;AACxE,QAAK,qBAAqB;IACxB,MAAM,aAAa,IAAI,YAAY,oBAAoB;AACvD,cAAU,iBAAiB,oBAAoB,iBAAiB;AAChE,cAAU,cAAc,WAAW;AACnC,SAAK,WAAW,kBAAkB;AAChC,gBAAW,YAAY,sBAAsB,UAAU,CAAC,EAAE,EAAE,QAAQ,KAAM,EAAC;AAC3E,SAAI,SAAS,kBAAkB,yBAC7B,OAAM,UAAU;IAEnB;GACF;AACD,UAAO,MAAM;AACX,cAAU,oBAAoB,oBAAoB,iBAAiB;AACnE,eAAW,MAAM;KACf,MAAM,eAAe,IAAI,YAAY,sBAAsB;AAC3D,eAAU,iBAAiB,sBAAsB,mBAAmB;AACpE,eAAU,cAAc,aAAa;AACrC,UAAK,aAAa,iBAChB,OAAM,4BAA4B,SAAS,MAAM,EAAE,QAAQ,KAAM,EAAC;AAEpE,eAAU,oBAAoB,sBAAsB,mBAAmB;AACvE,sBAAiB,OAAO,WAAW;IACpC,GAAE,EAAE;GACN;EACF;CACF,GAAE;EAAC;EAAW;EAAkB;EAAoB;CAAW,EAAC;CACjE,MAAM,gBAAgB,aAAM,YAC1B,CAAC,UAAU;AACT,OAAK,SAAS,QAAS;AACvB,MAAI,WAAW,OAAQ;EACvB,MAAM,WAAW,MAAM,QAAQ,UAAU,MAAM,WAAW,MAAM,YAAY,MAAM;EAClF,MAAM,iBAAiB,SAAS;AAChC,MAAI,YAAY,gBAAgB;GAC9B,MAAM,aAAa,MAAM;GACzB,MAAM,CAAC,OAAO,KAAK,GAAG,iBAAiB,WAAW;GAClD,MAAM,4BAA4B,SAAS;AAC3C,QAAK,2BACH;QAAI,mBAAmB,WAAY,OAAM,gBAAgB;GAAC,YAErD,MAAM,YAAY,mBAAmB,MAAM;AAC9C,UAAM,gBAAgB;AACtB,QAAI,KAAM,OAAM,OAAO,EAAE,QAAQ,KAAM,EAAC;GACzC,WAAU,MAAM,YAAY,mBAAmB,OAAO;AACrD,UAAM,gBAAgB;AACtB,QAAI,KAAM,OAAM,MAAM,EAAE,QAAQ,KAAM,EAAC;GACxC;EAEJ;CACF,GACD;EAAC;EAAM;EAAS,WAAW;CAAO,EACnC;AACD,wBAAuB,4BAAI,UAAU,KAAK;EAAE,UAAU;EAAI,GAAG;EAAY,KAAK;EAAc,WAAW;CAAe,EAAC;AACxH,EAAC;AACF,WAAW,cAAc;AACzB,SAAS,WAAW,YAAY,EAAE,SAAS,OAAO,GAAG,CAAE,GAAE;CACvD,MAAM,2BAA2B,SAAS;AAC1C,MAAK,MAAM,aAAa,YAAY;AAClC,QAAM,WAAW,EAAE,OAAQ,EAAC;AAC5B,MAAI,SAAS,kBAAkB,yBAA0B;CAC1D;AACF;AACD,SAAS,iBAAiB,WAAW;CACnC,MAAM,aAAa,sBAAsB,UAAU;CACnD,MAAM,QAAQ,YAAY,YAAY,UAAU;CAChD,MAAM,OAAO,YAAY,WAAW,SAAS,EAAE,UAAU;AACzD,QAAO,CAAC,OAAO,IAAK;AACrB;AACD,SAAS,sBAAsB,WAAW;CACxC,MAAM,QAAQ,CAAE;CAChB,MAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc,EAC3E,YAAY,CAAC,SAAS;EACpB,MAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,MAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AACrE,SAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;CACnE,EACF,EAAC;AACF,QAAO,OAAO,UAAU,CAAE,OAAM,KAAK,OAAO,YAAY;AACxD,QAAO;AACR;AACD,SAAS,YAAY,UAAU,WAAW;AACxC,MAAK,MAAM,WAAW,SACpB,MAAK,SAAS,SAAS,EAAE,MAAM,UAAW,EAAC,CAAE,QAAO;AAEvD;AACD,SAAS,SAAS,MAAM,EAAE,MAAM,EAAE;AAChC,KAAI,iBAAiB,KAAK,CAAC,eAAe,SAAU,QAAO;AAC3D,QAAO,MAAM;AACX,MAAI,cAAc,KAAK,SAAS,KAAM,QAAO;AAC7C,MAAI,iBAAiB,KAAK,CAAC,YAAY,OAAQ,QAAO;AACtD,SAAO,KAAK;CACb;AACD,QAAO;AACR;AACD,SAAS,kBAAkB,SAAS;AAClC,QAAO,mBAAmB,oBAAoB,YAAY;AAC3D;AACD,SAAS,MAAM,SAAS,EAAE,SAAS,OAAO,GAAG,CAAE,GAAE;AAC/C,KAAI,WAAW,QAAQ,OAAO;EAC5B,MAAM,2BAA2B,SAAS;AAC1C,UAAQ,MAAM,EAAE,eAAe,KAAM,EAAC;AACtC,MAAI,YAAY,4BAA4B,kBAAkB,QAAQ,IAAI,OACxE,SAAQ,QAAQ;CACnB;AACF;AACD,IAAI,mBAAmB,wBAAwB;AAC/C,SAAS,yBAAyB;CAChC,IAAI,QAAQ,CAAE;AACd,QAAO;EACL,IAAI,YAAY;GACd,MAAM,mBAAmB,MAAM;AAC/B,OAAI,eAAe,iBACjB,mBAAkB,OAAO;AAE3B,WAAQ,YAAY,OAAO,WAAW;AACtC,SAAM,QAAQ,WAAW;EAC1B;EACD,OAAO,YAAY;AACjB,WAAQ,YAAY,OAAO,WAAW;AACtC,SAAM,IAAI,QAAQ;EACnB;CACF;AACF;AACD,SAAS,YAAY,OAAO,MAAM;CAChC,MAAM,eAAe,CAAC,GAAG,KAAM;CAC/B,MAAM,QAAQ,aAAa,QAAQ,KAAK;AACxC,KAAI,UAAU,GACZ,cAAa,OAAO,OAAO,EAAE;AAE/B,QAAO;AACR;AACD,SAAS,YAAY,OAAO;AAC1B,QAAO,MAAM,OAAO,CAAC,SAAS,KAAK,YAAY,IAAI;AACpD;;;;;ACvMD,IAAIE,gBAAc;AAClB,IAAIC,WAAS,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACrD,MAAM,EAAE,WAAW,cAAe,GAAG,aAAa,GAAG;CACrD,MAAM,CAAC,SAAS,WAAW,GAAG,aAAM,SAAS,MAAM;AACnD,kBAAgB,MAAM,WAAW,KAAK,EAAE,CAAE,EAAC;CAC3C,MAAM,YAAY,iBAAiB,WAAW,YAAY,UAAU;AACpE,QAAO,YAAY,iBAAS,6BAA6B,4BAAI,UAAU,KAAK;EAAE,GAAG;EAAa,KAAK;CAAc,EAAC,EAAE,UAAU,GAAG;AAClI,EAAC;AACF,SAAO,cAAcD;;;;ACPrB,SAAS,gBAAgB,cAAc,SAAS;AAC9C,QAAO,aAAM,WAAW,CAAC,OAAO,UAAU;EACxC,MAAM,YAAY,QAAQ,OAAO;AACjC,SAAO,aAAa;CACrB,GAAE,aAAa;AACjB;AAGD,IAAI,WAAW,CAAC,UAAU;CACxB,MAAM,EAAE,SAAS,UAAU,GAAG;CAC9B,MAAM,WAAW,YAAY,QAAQ;CACrC,MAAM,eAAe,aAAa,aAAa,SAAS,EAAE,SAAS,SAAS,UAAW,EAAC,GAAG,aAAO,SAAS,KAAK,SAAS;CACzH,MAAM,MAAM,gBAAgB,SAAS,KAAK,gBAAc,MAAM,CAAC;CAC/D,MAAM,oBAAoB,aAAa;AACvC,QAAO,cAAc,SAAS,YAAY,aAAO,aAAa,OAAO,EAAE,IAAK,EAAC,GAAG;AACjF;AACD,SAAS,cAAc;AACvB,SAAS,YAAY,SAAS;CAC5B,MAAM,CAAC,MAAM,QAAQ,GAAG,aAAO,UAAU;CACzC,MAAM,YAAY,aAAO,OAAO,CAAE,EAAC;CACnC,MAAM,iBAAiB,aAAO,OAAO,QAAQ;CAC7C,MAAM,uBAAuB,aAAO,OAAO,OAAO;CAClD,MAAM,eAAe,UAAU,YAAY;CAC3C,MAAM,CAAC,OAAO,KAAK,GAAG,gBAAgB,cAAc;EAClD,SAAS;GACP,SAAS;GACT,eAAe;EAChB;EACD,kBAAkB;GAChB,OAAO;GACP,eAAe;EAChB;EACD,WAAW,EACT,OAAO,UACR;CACF,EAAC;AACF,cAAO,UAAU,MAAM;EACrB,MAAM,uBAAuB,iBAAiB,UAAU,QAAQ;AAChE,uBAAqB,UAAU,UAAU,YAAY,uBAAuB;CAC7E,GAAE,CAAC,KAAM,EAAC;AACX,kBAAgB,MAAM;EACpB,MAAM,SAAS,UAAU;EACzB,MAAM,aAAa,eAAe;EAClC,MAAM,oBAAoB,eAAe;AACzC,MAAI,mBAAmB;GACrB,MAAM,oBAAoB,qBAAqB;GAC/C,MAAM,uBAAuB,iBAAiB,OAAO;AACrD,OAAI,QACF,MAAK,QAAQ;YACJ,yBAAyB,UAAU,QAAQ,YAAY,OAChE,MAAK,UAAU;QACV;IACL,MAAM,cAAc,sBAAsB;AAC1C,QAAI,cAAc,YAChB,MAAK,gBAAgB;QAErB,MAAK,UAAU;GAElB;AACD,kBAAe,UAAU;EAC1B;CACF,GAAE,CAAC,SAAS,IAAK,EAAC;AACnB,kBAAgB,MAAM;AACpB,MAAI,MAAM;GACR,IAAI;GACJ,MAAM,cAAc,KAAK,cAAc,eAAe;GACtD,MAAM,qBAAqB,CAAC,UAAU;IACpC,MAAM,uBAAuB,iBAAiB,UAAU,QAAQ;IAChE,MAAM,qBAAqB,qBAAqB,SAAS,MAAM,cAAc;AAC7E,QAAI,MAAM,WAAW,QAAQ,oBAAoB;AAC/C,UAAK,gBAAgB;AACrB,UAAK,eAAe,SAAS;MAC3B,MAAM,kBAAkB,KAAK,MAAM;AACnC,WAAK,MAAM,oBAAoB;AAC/B,kBAAY,YAAY,WAAW,MAAM;AACvC,WAAI,KAAK,MAAM,sBAAsB,WACnC,MAAK,MAAM,oBAAoB;MAElC,EAAC;KACH;IACF;GACF;GACD,MAAM,uBAAuB,CAAC,UAAU;AACtC,QAAI,MAAM,WAAW,KACnB,sBAAqB,UAAU,iBAAiB,UAAU,QAAQ;GAErE;AACD,QAAK,iBAAiB,kBAAkB,qBAAqB;AAC7D,QAAK,iBAAiB,mBAAmB,mBAAmB;AAC5D,QAAK,iBAAiB,gBAAgB,mBAAmB;AACzD,UAAO,MAAM;AACX,gBAAY,aAAa,UAAU;AACnC,SAAK,oBAAoB,kBAAkB,qBAAqB;AAChE,SAAK,oBAAoB,mBAAmB,mBAAmB;AAC/D,SAAK,oBAAoB,gBAAgB,mBAAmB;GAC7D;EACF,MACC,MAAK,gBAAgB;CAExB,GAAE,CAAC,MAAM,IAAK,EAAC;AAChB,QAAO;EACL,WAAW,CAAC,WAAW,kBAAmB,EAAC,SAAS,MAAM;EAC1D,KAAK,aAAO,YAAY,CAAC,UAAU;AACjC,OAAI,MAAO,WAAU,UAAU,iBAAiB,MAAM;AACtD,WAAQ,MAAM;EACf,GAAE,CAAE,EAAC;CACP;AACF;AACD,SAAS,iBAAiB,QAAQ;AAChC,QAAO,QAAQ,iBAAiB;AACjC;AACD,SAASE,gBAAc,SAAS;CAC9B,IAAI,SAAS,OAAO,yBAAyB,QAAQ,OAAO,MAAM,EAAE;CACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,KAAI,QACF,QAAO,QAAQ;AAEjB,UAAS,OAAO,yBAAyB,SAAS,MAAM,EAAE;AAC1D,WAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,KAAI,QACF,QAAO,QAAQ,MAAM;AAEvB,QAAO,QAAQ,MAAM,OAAO,QAAQ;AACrC;;;;AChID,IAAI,QAAQ;AAKZ,SAAS,iBAAiB;AACxB,cAAM,UAAU,MAAM;EACpB,MAAM,aAAa,SAAS,iBAAiB,2BAA2B;AACxE,WAAS,KAAK,sBAAsB,cAAc,WAAW,MAAM,kBAAkB,CAAC;AACtF,WAAS,KAAK,sBAAsB,aAAa,WAAW,MAAM,kBAAkB,CAAC;AACrF;AACA,SAAO,MAAM;AACX,OAAI,UAAU,EACZ,UAAS,iBAAiB,2BAA2B,CAAC,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC;AAExF;EACD;CACF,GAAE,CAAE,EAAC;AACP;AACD,SAAS,mBAAmB;CAC1B,MAAM,UAAU,SAAS,cAAc,OAAO;AAC9C,SAAQ,aAAa,0BAA0B,GAAG;AAClD,SAAQ,WAAW;AACnB,SAAQ,MAAM,UAAU;AACxB,SAAQ,MAAM,UAAU;AACxB,SAAQ,MAAM,WAAW;AACzB,SAAQ,MAAM,gBAAgB;AAC9B,QAAO;AACR;;;;AChCD,IAAW,qBAAqB;AAChC,IAAW,qBAAqB;AAChC,IAAW,wBAAwB;;;;;AAKnC,IAAW,yBAAyB;;;;;;;;;;;;;;;;;ACMpC,SAAgB,UAAU,KAAK,OAAO;AAClC,YAAW,QAAQ,WACf,KAAI,MAAM;UAEL,IACL,KAAI,UAAU;AAElB,QAAO;AACV;;;;;;;;;;;;;;;;;;ACND,SAAgB,eAAe,cAAc,UAAU;CACnD,IAAI,MAAM,2BAAS,WAAY;AAAE,SAAQ;GAErC,OAAO;GAEG;GAEV,QAAQ;IACJ,IAAI,UAAU;AACV,YAAO,IAAI;IACd;IACD,IAAI,QAAQ,OAAO;KACf,IAAI,OAAO,IAAI;AACf,SAAI,SAAS,OAAO;AAChB,UAAI,QAAQ;AACZ,UAAI,SAAS,OAAO,KAAK;KAC5B;IACJ;GACJ;EACJ;CAAI,EAAC,CAAC;AAEP,KAAI,WAAW;AACf,QAAO,IAAI;AACd;;;;ACnCD,IAAI,mCAAmC,WAAW,cAAcC,aAAM,kBAAkBA,aAAM;AAC9F,IAAI,gCAAgB,IAAI;;;;;;;;;;;;;;;AAexB,SAAgB,aAAa,MAAM,cAAc;CAC7C,IAAI,cAAc,eAAe,gBAAgB,MAAM,SAAU,UAAU;AACvE,SAAO,KAAK,QAAQ,SAAU,KAAK;AAAE,UAAO,UAAU,KAAK,SAAS;EAAG,EAAC;CAC3E,EAAC;AAEF,2BAA0B,WAAY;EAClC,IAAI,WAAW,cAAc,IAAI,YAAY;AAC7C,MAAI,UAAU;GACV,IAAI,aAAa,IAAI,IAAI;GACzB,IAAI,aAAa,IAAI,IAAI;GACzB,IAAI,YAAY,YAAY;AAC5B,cAAW,QAAQ,SAAU,KAAK;AAC9B,SAAK,WAAW,IAAI,IAAI,CACpB,WAAU,KAAK,KAAK;GAE3B,EAAC;AACF,cAAW,QAAQ,SAAU,KAAK;AAC9B,SAAK,WAAW,IAAI,IAAI,CACpB,WAAU,KAAK,UAAU;GAEhC,EAAC;EACL;AACD,gBAAc,IAAI,aAAa,KAAK;CACvC,GAAE,CAAC,IAAK,EAAC;AACV,QAAO;AACV;;;;AC3CD,SAAS,KAAK,GAAG;AACb,QAAO;AACV;AACD,SAAS,kBAAkB,UAAU,YAAY;AAC7C,KAAI,oBAAoB,EAAK,cAAa;CAC1C,IAAI,SAAS,CAAE;CACf,IAAI,WAAW;CACf,IAAI,SAAS;EACT,MAAM,WAAY;AACd,OAAI,SACA,OAAM,IAAI,MAAM;AAEpB,OAAI,OAAO,OACP,QAAO,OAAO,OAAO,SAAS;AAElC,UAAO;EACV;EACD,WAAW,SAAU,MAAM;GACvB,IAAI,OAAO,WAAW,MAAM,SAAS;AACrC,UAAO,KAAK,KAAK;AACjB,UAAO,WAAY;AACf,aAAS,OAAO,OAAO,SAAU,GAAG;AAAE,YAAO,MAAM;IAAO,EAAC;GAC9D;EACJ;EACD,kBAAkB,SAAU,IAAI;AAC5B,cAAW;AACX,UAAO,OAAO,QAAQ;IAClB,IAAI,MAAM;AACV,aAAS,CAAE;AACX,QAAI,QAAQ,GAAG;GAClB;AACD,YAAS;IACL,MAAM,SAAU,GAAG;AAAE,YAAO,GAAG,EAAE;IAAG;IACpC,QAAQ,WAAY;AAAE,YAAO;IAAS;GACzC;EACJ;EACD,cAAc,SAAU,IAAI;AACxB,cAAW;GACX,IAAI,eAAe,CAAE;AACrB,OAAI,OAAO,QAAQ;IACf,IAAI,MAAM;AACV,aAAS,CAAE;AACX,QAAI,QAAQ,GAAG;AACf,mBAAe;GAClB;GACD,IAAI,eAAe,WAAY;IAC3B,IAAIC,QAAM;AACV,mBAAe,CAAE;AACjB,UAAI,QAAQ,GAAG;GAClB;GACD,IAAI,QAAQ,WAAY;AAAE,WAAO,QAAQ,SAAS,CAAC,KAAK,aAAa;GAAG;AACxE,UAAO;AACP,YAAS;IACL,MAAM,SAAU,GAAG;AACf,kBAAa,KAAK,EAAE;AACpB,YAAO;IACV;IACD,QAAQ,SAAU,QAAQ;AACtB,oBAAe,aAAa,OAAO,OAAO;AAC1C,YAAO;IACV;GACJ;EACJ;CACJ;AACD,QAAO;AACV;AAMD,SAAgB,oBAAoBC,WAAS;AACzC,KAAIA,mBAAiB,EAAK,aAAU,CAAE;CACtC,IAAI,SAAS,kBAAkB,KAAK;AACpC,QAAO,UAAU,SAAS;EAAE,OAAO;EAAM,KAAK;CAAO,GAAEA,UAAQ;AAC/D,QAAO;AACV;;;;AC3ED,IAAI,UAAU,SAAU,IAAI;CACxB,IAAI,UAAU,GAAG,SAAS,OAAO,OAAO,IAAI,CAAC,SAAU,EAAC;AACxD,MAAK,QACD,OAAM,IAAI,MAAM;CAEpB,IAAI,SAAS,QAAQ,MAAM;AAC3B,MAAK,OACD,OAAM,IAAI,MAAM;AAEpB,QAAO,aAAM,cAAc,QAAQ,SAAS,CAAE,GAAE,KAAK,CAAC;AACzD;AACD,QAAQ,kBAAkB;AAC1B,SAAgB,cAAc,QAAQ,UAAU;AAC5C,QAAO,UAAU,SAAS;AAC1B,QAAO;AACV;;;;AChBD,IAAW,YAAY,qBAAqB;;;;ACI5C,IAAI,UAAU,WAAY;AACtB;AACH;;;;AAID,IAAI,eAAe,aAAM,WAAW,SAAU,OAAO,WAAW;CAC5D,IAAI,MAAM,aAAM,OAAO,KAAK;CAC5B,IAAI,KAAK,aAAM,SAAS;EACpB,iBAAiB;EACjB,gBAAgB;EAChB,oBAAoB;CACvB,EAAC,EAAE,YAAY,GAAG,IAAI,eAAe,GAAG;CACzC,IAAI,eAAe,MAAM,cAAc,WAAW,MAAM,UAAU,YAAY,MAAM,WAAW,kBAAkB,MAAM,iBAAiB,UAAU,MAAM,SAAS,SAAS,MAAM,QAAQ,UAAU,MAAM,SAAS,aAAa,MAAM,YAAY,cAAc,MAAM,aAAa,QAAQ,MAAM,OAAO,iBAAiB,MAAM,gBAAgB,KAAK,MAAM,IAAI,YAAY,YAAY,IAAI,QAAQ,IAAI,UAAU,MAAM,SAAS,OAAO,OAAO,OAAO;EAAC;EAAgB;EAAY;EAAa;EAAmB;EAAW;EAAU;EAAW;EAAc;EAAe;EAAS;EAAkB;EAAM;CAAU,EAAC;CACxlB,IAAIC,YAAU;CACd,IAAI,eAAe,aAAa,CAAC,KAAK,SAAU,EAAC;CACjD,IAAI,iBAAiB,SAAS,SAAS,CAAE,GAAE,KAAK,EAAE,UAAU;AAC5D,QAAQ,aAAM,cAAcC,aAAM,UAAU,MACxC,WAAY,aAAM,cAAcD,WAAS;EAAE,SAAS;EAA4B;EAAyB;EAAoB;EAAyB;EAAoB;EAAqB;EAAc,kBAAkB;EAAgB,SAAS;EAAc;CAAS,EAAC,EAChR,eAAgB,aAAM,aAAa,aAAM,SAAS,KAAK,SAAS,EAAE,SAAS,SAAS,CAAE,GAAE,eAAe,EAAE,EAAE,KAAK,aAAc,EAAC,CAAC,GAAK,aAAM,cAAc,WAAW,SAAS,CAAE,GAAE,gBAAgB;EAAa;EAAW,KAAK;CAAc,EAAC,EAAE,SAAS,CAAE;AACjQ,EAAC;AACF,aAAa,eAAe;CACxB,SAAS;CACT,iBAAiB;CACjB,OAAO;AACV;AACD,aAAa,aAAa;CACtB,WAAW;CACX,WAAW;AACd;;;;AClCD,IAAI;AAIJ,IAAW,WAAW,WAAY;AAC9B,KAAI,aACA,QAAO;AAEX,YAAW,sBAAsB,YAC7B,QAAO;AAEX;AACH;;;;ACXD,SAAS,eAAe;AACpB,MAAK,SACD,QAAO;CACX,IAAI,MAAM,SAAS,cAAc,QAAQ;AACzC,KAAI,OAAO;CACX,IAAI,QAAQ,UAAU;AACtB,KAAI,MACA,KAAI,aAAa,SAAS,MAAM;AAEpC,QAAO;AACV;AACD,SAAS,aAAa,KAAK,KAAK;AAE5B,KAAI,IAAI,WAEJ,KAAI,WAAW,UAAU;KAGzB,KAAI,YAAY,SAAS,eAAe,IAAI,CAAC;AAEpD;AACD,SAAS,eAAe,KAAK;CACzB,IAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,OAAO,CAAC;AAClE,MAAK,YAAY,IAAI;AACxB;AACD,IAAW,sBAAsB,WAAY;CACzC,IAAI,UAAU;CACd,IAAI,aAAa;AACjB,QAAO;EACH,KAAK,SAAU,OAAO;AAClB,OAAI,WAAW,GACX;QAAK,aAAa,cAAc,EAAG;AAC/B,kBAAa,YAAY,MAAM;AAC/B,oBAAe,WAAW;IAC7B;;AAEL;EACH;EACD,QAAQ,WAAY;AAChB;AACA,QAAK,WAAW,YAAY;AACxB,eAAW,cAAc,WAAW,WAAW,YAAY,WAAW;AACtE,iBAAa;GAChB;EACJ;CACJ;AACJ;;;;;;;;;;;;;ACpCD,IAAW,qBAAqB,WAAY;CACxC,IAAI,QAAQ,qBAAqB;AACjC,QAAO,SAAU,QAAQ,WAAW;AAChC,eAAM,UAAU,WAAY;AACxB,SAAM,IAAI,OAAO;AACjB,UAAO,WAAY;AACf,UAAM,QAAQ;GACjB;EACJ,GAAE,CAAC,UAAU,SAAU,EAAC;CAC5B;AACJ;;;;;;;;;;ACdD,IAAW,iBAAiB,WAAY;CACpC,IAAI,WAAW,oBAAoB;CACnC,IAAI,QAAQ,SAAU,IAAI;EACtB,IAAI,SAAS,GAAG,QAAQ,UAAU,GAAG;AACrC,WAAS,QAAQ,QAAQ;AACzB,SAAO;CACV;AACD,QAAO;AACV;;;;ACfD,IAAW,UAAU;CACjB,MAAM;CACN,KAAK;CACL,OAAO;CACP,KAAK;AACR;AACD,IAAI,QAAQ,SAAU,GAAG;AAAE,QAAO,SAAS,KAAK,IAAI,GAAG,IAAI;AAAI;AAC/D,IAAI,YAAY,SAAU,SAAS;CAC/B,IAAI,KAAK,OAAO,iBAAiB,SAAS,KAAK;CAC/C,IAAI,OAAO,GAAG,YAAY,YAAY,gBAAgB;CACtD,IAAI,MAAM,GAAG,YAAY,YAAY,eAAe;CACpD,IAAI,QAAQ,GAAG,YAAY,YAAY,iBAAiB;AACxD,QAAO;EAAC,MAAM,KAAK;EAAE,MAAM,IAAI;EAAE,MAAM,MAAM;CAAC;AACjD;AACD,IAAW,cAAc,SAAU,SAAS;AACxC,KAAI,iBAAiB,EAAK,WAAU;AACpC,YAAW,WAAW,YAClB,QAAO;CAEX,IAAI,UAAU,UAAU,QAAQ;CAChC,IAAI,gBAAgB,SAAS,gBAAgB;CAC7C,IAAI,cAAc,OAAO;AACzB,QAAO;EACH,MAAM,QAAQ;EACd,KAAK,QAAQ;EACb,OAAO,QAAQ;EACf,KAAK,KAAK,IAAI,GAAG,cAAc,gBAAgB,QAAQ,KAAK,QAAQ,GAAG;CAC1E;AACJ;;;;ACxBD,IAAI,QAAQ,gBAAgB;AAC5B,IAAW,gBAAgB;AAI3B,IAAI,YAAY,SAAU,IAAI,eAAe,SAAS,WAAW;CAC7D,IAAI,OAAO,GAAG,MAAM,MAAM,GAAG,KAAK,QAAQ,GAAG,OAAO,MAAM,GAAG;AAC7D,KAAI,iBAAiB,EAAK,WAAU;AACpC,QAAO,QAAQ,OAAO,uBAAuB,2BAA2B,CAAC,OAAO,WAAW,wBAAwB,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,WAAW,kBAAkB,CAAC,OAAO,eAAe,6BAA6B,CAAC,OAAO,WAAW,6CAA6C,CAAC,OAAO;EACnS,iBAAiB,sBAAsB,OAAO,WAAW,IAAI;EAC7D,YAAY,YACR,uBAAuB,OAAO,MAAM,yBAAyB,CAAC,OAAO,KAAK,2BAA2B,CAAC,OAAO,OAAO,iEAAiE,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,WAAW,UAAU;EACzO,YAAY,aAAa,kBAAkB,OAAO,KAAK,MAAM,CAAC,OAAO,WAAW,IAAI;CACvF,EACI,OAAO,QAAQ,CACf,KAAK,GAAG,EAAE,iBAAiB,CAAC,OAAO,oBAAoB,kBAAkB,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,WAAW,kBAAkB,CAAC,OAAO,oBAAoB,yBAAyB,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,WAAW,kBAAkB,CAAC,OAAO,oBAAoB,KAAK,CAAC,OAAO,oBAAoB,oBAAoB,CAAC,OAAO,WAAW,kBAAkB,CAAC,OAAO,oBAAoB,KAAK,CAAC,OAAO,oBAAoB,2BAA2B,CAAC,OAAO,WAAW,sBAAsB,CAAC,OAAO,eAAe,YAAY,CAAC,OAAO,wBAAwB,KAAK,CAAC,OAAO,KAAK,aAAa;AAC/kB;AACD,IAAI,uBAAuB,WAAY;CACnC,IAAI,UAAU,SAAS,SAAS,KAAK,aAAa,cAAc,IAAI,KAAK,GAAG;AAC5E,QAAO,SAAS,QAAQ,GAAG,UAAU;AACxC;AACD,IAAW,mBAAmB,WAAY;AACtC,cAAM,UAAU,WAAY;AACxB,WAAS,KAAK,aAAa,eAAe,CAAC,sBAAsB,GAAG,GAAG,UAAU,CAAC;AAClF,SAAO,WAAY;GACf,IAAI,aAAa,sBAAsB,GAAG;AAC1C,OAAI,cAAc,EACd,UAAS,KAAK,gBAAgB,cAAc;OAG5C,UAAS,KAAK,aAAa,eAAe,WAAW,UAAU,CAAC;EAEvE;CACJ,GAAE,CAAE,EAAC;AACT;;;;AAID,IAAW,kBAAkB,SAAU,IAAI;CACvC,IAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,KAAK,GAAG,SAAS,UAAU,YAAY,IAAI,WAAW;AACpH,mBAAkB;CAMlB,IAAI,MAAM,aAAM,QAAQ,WAAY;AAAE,SAAO,YAAY,QAAQ;CAAG,GAAE,CAAC,OAAQ,EAAC;AAChF,QAAO,aAAM,cAAc,OAAO,EAAE,QAAQ,UAAU,MAAM,YAAY,UAAU,cAAc,eAAe,GAAG,CAAE,EAAC;AACxH;;;;ACpDD,IAAI,mBAAmB;AACvB,WAAW,WAAW,YAClB,KAAI;CACA,IAAI,UAAU,OAAO,eAAe,CAAE,GAAE,WAAW,EAC/C,KAAK,WAAY;AACb,qBAAmB;AACnB,SAAO;CACV,EACJ,EAAC;AAEF,QAAO,iBAAiB,QAAQ,SAAS,QAAQ;AAEjD,QAAO,oBAAoB,QAAQ,SAAS,QAAQ;AACvD,SACM,KAAK;AACR,oBAAmB;AACtB;AAEL,IAAW,aAAa,mBAAmB,EAAE,SAAS,MAAO,IAAG;;;;AClBhE,IAAI,uBAAuB,SAAU,MAAM;AAEvC,QAAO,KAAK,YAAY;AAC3B;AACD,IAAI,uBAAuB,SAAU,MAAM,UAAU;AACjD,OAAM,gBAAgB,SAClB,QAAO;CAEX,IAAI,SAAS,OAAO,iBAAiB,KAAK;AAC1C,QAEA,OAAO,cAAc,cAEf,OAAO,cAAc,OAAO,cAAc,qBAAqB,KAAK,IAAI,OAAO,cAAc;AACtG;AACD,IAAI,0BAA0B,SAAU,MAAM;AAAE,QAAO,qBAAqB,MAAM,YAAY;AAAG;AACjG,IAAI,0BAA0B,SAAU,MAAM;AAAE,QAAO,qBAAqB,MAAM,YAAY;AAAG;AACjG,IAAW,0BAA0B,SAAU,MAAM,MAAM;CACvD,IAAI,gBAAgB,KAAK;CACzB,IAAI,UAAU;AACd,IAAG;AAEC,aAAW,eAAe,eAAe,mBAAmB,WACxD,WAAU,QAAQ;EAEtB,IAAI,eAAe,uBAAuB,MAAM,QAAQ;AACxD,MAAI,cAAc;GACd,IAAI,KAAK,mBAAmB,MAAM,QAAQ,EAAE,eAAe,GAAG,IAAI,eAAe,GAAG;AACpF,OAAI,eAAe,aACf,QAAO;EAEd;AACD,YAAU,QAAQ;CACrB,SAAQ,WAAW,YAAY,cAAc;AAC9C,QAAO;AACV;AACD,IAAI,sBAAsB,SAAU,IAAI;CACpC,IAAI,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,eAAe,GAAG;AAChF,QAAO;EACH;EACA;EACA;CACH;AACJ;AACD,IAAI,sBAAsB,SAAU,IAAI;CACpC,IAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,cAAc,GAAG;AAC/E,QAAO;EACH;EACA;EACA;CACH;AACJ;AACD,IAAI,yBAAyB,SAAU,MAAM,MAAM;AAC/C,QAAO,SAAS,MAAM,wBAAwB,KAAK,GAAG,wBAAwB,KAAK;AACtF;AACD,IAAI,qBAAqB,SAAU,MAAM,MAAM;AAC3C,QAAO,SAAS,MAAM,oBAAoB,KAAK,GAAG,oBAAoB,KAAK;AAC9E;AACD,IAAI,qBAAqB,SAAU,MAAM,WAAW;;;;;;AAMhD,QAAO,SAAS,OAAO,cAAc,QAAQ,KAAK;AACrD;AACD,IAAW,eAAe,SAAU,MAAM,WAAW,OAAO,aAAa,cAAc;CACnF,IAAI,kBAAkB,mBAAmB,MAAM,OAAO,iBAAiB,UAAU,CAAC,UAAU;CAC5F,IAAI,QAAQ,kBAAkB;CAE9B,IAAI,SAAS,MAAM;CACnB,IAAI,eAAe,UAAU,SAAS,OAAO;CAC7C,IAAI,qBAAqB;CACzB,IAAI,kBAAkB,QAAQ;CAC9B,IAAI,kBAAkB;CACtB,IAAI,qBAAqB;AACzB,IAAG;AACC,OAAK,OACD;EAEJ,IAAI,KAAK,mBAAmB,MAAM,OAAO,EAAE,WAAW,GAAG,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG;EAC7F,IAAI,gBAAgB,WAAW,WAAW,kBAAkB;AAC5D,MAAI,YAAY,eACZ;OAAI,uBAAuB,MAAM,OAAO,EAAE;AACtC,uBAAmB;AACnB,0BAAsB;GACzB;;EAEL,IAAI,WAAW,OAAO;AAGtB,WAAU,YAAY,SAAS,aAAa,KAAK,yBAAyB,SAAS,OAAO;CAC7F,UAEC,gBAAgB,WAAW,SAAS,QAEjC,iBAAiB,UAAU,SAAS,OAAO,IAAI,cAAc;AAElE,KAAI,oBACE,gBAAgB,KAAK,IAAI,gBAAgB,GAAG,MAAQ,gBAAgB,QAAQ,iBAC9E,sBAAqB;WAEf,oBACJ,gBAAgB,KAAK,IAAI,mBAAmB,GAAG,MAAQ,iBAAiB,QAAQ,oBAClF,sBAAqB;AAEzB,QAAO;AACV;;;;ACrGD,IAAW,aAAa,SAAU,OAAO;AACrC,QAAO,oBAAoB,QAAQ,CAAC,MAAM,eAAe,GAAG,SAAS,MAAM,eAAe,GAAG,OAAQ,IAAG,CAAC,GAAG,CAAE;AACjH;AACD,IAAW,aAAa,SAAU,OAAO;AAAE,QAAO,CAAC,MAAM,QAAQ,MAAM,MAAO;AAAG;AACjF,IAAI,aAAa,SAAU,KAAK;AAC5B,QAAO,OAAO,aAAa,MAAM,IAAI,UAAU;AAClD;AACD,IAAI,eAAe,SAAU,GAAG,GAAG;AAAE,QAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE;AAAK;AAC7E,IAAI,gBAAgB,SAAU,IAAI;AAAE,QAAO,4BAA4B,OAAO,IAAI,oDAAoD,CAAC,OAAO,IAAI,4BAA4B;AAAG;AACjL,IAAI,YAAY;AAChB,IAAI,YAAY,CAAE;AAClB,SAAgB,oBAAoB,OAAO;CACvC,IAAI,qBAAqB,aAAM,OAAO,CAAE,EAAC;CACzC,IAAI,gBAAgB,aAAM,OAAO,CAAC,GAAG,CAAE,EAAC;CACxC,IAAI,aAAa,aAAM,QAAQ;CAC/B,IAAI,KAAK,aAAM,SAAS,YAAY,CAAC;CACrC,IAAIE,UAAQ,aAAM,SAAS,eAAe,CAAC;CAC3C,IAAI,YAAY,aAAM,OAAO,MAAM;AACnC,cAAM,UAAU,WAAY;AACxB,YAAU,UAAU;CACvB,GAAE,CAAC,KAAM,EAAC;AACX,cAAM,UAAU,WAAY;AACxB,MAAI,MAAM,OAAO;AACb,YAAS,KAAK,UAAU,IAAI,uBAAuB,OAAO,GAAG,CAAC;GAC9D,IAAI,UAAU,cAAc,CAAC,MAAM,QAAQ,OAAQ,GAAE,CAAC,MAAM,UAAU,CAAE,GAAE,IAAI,WAAW,EAAE,KAAK,CAAC,OAAO,QAAQ;AAChH,WAAQ,QAAQ,SAAU,IAAI;AAAE,WAAO,GAAG,UAAU,IAAI,uBAAuB,OAAO,GAAG,CAAC;GAAG,EAAC;AAC9F,UAAO,WAAY;AACf,aAAS,KAAK,UAAU,OAAO,uBAAuB,OAAO,GAAG,CAAC;AACjE,YAAQ,QAAQ,SAAU,IAAI;AAAE,YAAO,GAAG,UAAU,OAAO,uBAAuB,OAAO,GAAG,CAAC;IAAG,EAAC;GACpG;EACJ;AACD;CACH,GAAE;EAAC,MAAM;EAAO,MAAM,QAAQ;EAAS,MAAM;CAAO,EAAC;CACtD,IAAI,oBAAoB,aAAM,YAAY,SAAU,OAAO,QAAQ;AAC/D,MAAK,aAAa,SAAS,MAAM,QAAQ,WAAW,KAAO,MAAM,SAAS,WAAW,MAAM,QACvF,SAAQ,UAAU,QAAQ;EAE9B,IAAI,QAAQ,WAAW,MAAM;EAC7B,IAAI,aAAa,cAAc;EAC/B,IAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,KAAK,MAAM;EACtE,IAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,KAAK,MAAM;EACtE,IAAI;EACJ,IAAI,SAAS,MAAM;EACnB,IAAI,gBAAgB,KAAK,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,GAAG,MAAM;AAEhE,MAAI,aAAa,SAAS,kBAAkB,OAAO,OAAO,SAAS,QAC/D,QAAO;EAEX,IAAI,+BAA+B,wBAAwB,eAAe,OAAO;AACjF,OAAK,6BACD,QAAO;AAEX,MAAI,6BACA,eAAc;OAEb;AACD,iBAAc,kBAAkB,MAAM,MAAM;AAC5C,kCAA+B,wBAAwB,eAAe,OAAO;EAEhF;AACD,OAAK,6BACD,QAAO;AAEX,OAAK,WAAW,WAAW,oBAAoB,UAAU,UAAU,QAC/D,YAAW,UAAU;AAEzB,OAAK,YACD,QAAO;EAEX,IAAI,gBAAgB,WAAW,WAAW;AAC1C,SAAO,aAAa,eAAe,QAAQ,OAAO,kBAAkB,MAAM,SAAS,QAAQ,KAAK;CACnG,GAAE,CAAE,EAAC;CACN,IAAI,gBAAgB,aAAM,YAAY,SAAU,QAAQ;EACpD,IAAI,QAAQ;AACZ,OAAK,UAAU,UAAU,UAAU,UAAU,SAAS,OAAOA,QAEzD;EAEJ,IAAI,QAAQ,YAAY,QAAQ,WAAW,MAAM,GAAG,WAAW,MAAM;EACrE,IAAI,cAAc,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,UAAO,EAAE,SAAS,MAAM,SAAS,EAAE,WAAW,MAAM,UAAU,MAAM,WAAW,EAAE,iBAAiB,aAAa,EAAE,OAAO,MAAM;EAAG,EAAC,CAAC;AAEtM,MAAI,eAAe,YAAY,QAAQ;AACnC,OAAI,MAAM,WACN,OAAM,gBAAgB;AAE1B;EACH;AAED,OAAK,aAAa;GACd,IAAI,aAAa,CAAC,UAAU,QAAQ,UAAU,CAAE,GAC3C,IAAI,WAAW,CACf,OAAO,QAAQ,CACf,OAAO,SAAU,MAAM;AAAE,WAAO,KAAK,SAAS,MAAM,OAAO;GAAG,EAAC;GACpE,IAAI,aAAa,WAAW,SAAS,IAAI,kBAAkB,OAAO,WAAW,GAAG,IAAI,UAAU,QAAQ;AACtG,OAAI,YACA;QAAI,MAAM,WACN,OAAM,gBAAgB;GACzB;EAER;CACJ,GAAE,CAAE,EAAC;CACN,IAAI,eAAe,aAAM,YAAY,SAAU,MAAM,OAAO,QAAQ,QAAQ;EACxE,IAAI,QAAQ;GAAQ;GAAa;GAAe;GAAgB;GAAQ,cAAc,yBAAyB,OAAO;EAAE;AACxH,qBAAmB,QAAQ,KAAK,MAAM;AACtC,aAAW,WAAY;AACnB,sBAAmB,UAAU,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,WAAO,MAAM;GAAQ,EAAC;EACvG,GAAE,EAAE;CACR,GAAE,CAAE,EAAC;CACN,IAAI,mBAAmB,aAAM,YAAY,SAAU,OAAO;AACtD,gBAAc,UAAU,WAAW,MAAM;AACzC,aAAW;CACd,GAAE,CAAE,EAAC;CACN,IAAI,cAAc,aAAM,YAAY,SAAU,OAAO;AACjD,eAAa,MAAM,MAAM,WAAW,MAAM,EAAE,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,QAAQ,CAAC;CAC7G,GAAE,CAAE,EAAC;CACN,IAAI,kBAAkB,aAAM,YAAY,SAAU,OAAO;AACrD,eAAa,MAAM,MAAM,WAAW,MAAM,EAAE,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,QAAQ,CAAC;CAC7G,GAAE,CAAE,EAAC;AACN,cAAM,UAAU,WAAY;AACxB,YAAU,KAAKA,QAAM;AACrB,QAAM,aAAa;GACf,iBAAiB;GACjB,gBAAgB;GAChB,oBAAoB;EACvB,EAAC;AACF,WAAS,iBAAiB,SAAS,eAAe,WAAW;AAC7D,WAAS,iBAAiB,aAAa,eAAe,WAAW;AACjE,WAAS,iBAAiB,cAAc,kBAAkB,WAAW;AACrE,SAAO,WAAY;AACf,eAAY,UAAU,OAAO,SAAU,MAAM;AAAE,WAAO,SAASA;GAAQ,EAAC;AACxE,YAAS,oBAAoB,SAAS,eAAe,WAAW;AAChE,YAAS,oBAAoB,aAAa,eAAe,WAAW;AACpE,YAAS,oBAAoB,cAAc,kBAAkB,WAAW;EAC3E;CACJ,GAAE,CAAE,EAAC;CACN,IAAI,kBAAkB,MAAM,iBAAiB,QAAQ,MAAM;AAC3D,QAAQ,aAAM,cAAcC,aAAM,UAAU,MACxC,QAAQ,aAAM,cAAcD,SAAO,EAAE,QAAQ,cAAc,GAAG,CAAE,EAAC,GAAG,MACpE,kBAAkB,aAAM,cAAc,iBAAiB;EAAE,YAAY,MAAM;EAAY,SAAS,MAAM;CAAS,EAAC,GAAG,KAAK;AAC/H;AACD,SAAS,yBAAyB,MAAM;CACpC,IAAI,eAAe;AACnB,QAAO,SAAS,MAAM;AAClB,MAAI,gBAAgB,YAAY;AAC5B,kBAAe,KAAK;AACpB,UAAO,KAAK;EACf;AACD,SAAO,KAAK;CACf;AACD,QAAO;AACV;;;;ACzJD,sBAAe,cAAc,WAAW,oBAAoB;;;;ACC5D,IAAI,oBAAoB,aAAM,WAAW,SAAU,OAAO,KAAK;AAAE,QAAQ,aAAM,cAAc,cAAc,SAAS,CAAE,GAAE,OAAO;EAAO;EAAK,SAASE;CAAS,EAAC,CAAC;AAAI,EAAC;AACpK,kBAAkB,aAAa,aAAa;AAC5C,0BAAe;;;;ACNf,IAAI,mBAAmB,SAAU,gBAAgB;AAC7C,YAAW,aAAa,YACpB,QAAO;CAEX,IAAI,eAAe,MAAM,QAAQ,eAAe,GAAG,eAAe,KAAK;AACvE,QAAO,aAAa,cAAc;AACrC;AACD,IAAI,6BAAa,IAAI;AACrB,IAAI,oCAAoB,IAAI;AAC5B,IAAI,YAAY,CAAE;AAClB,IAAI,YAAY;AAChB,IAAI,aAAa,SAAU,MAAM;AAC7B,QAAO,SAAS,KAAK,QAAQ,WAAW,KAAK,WAAW;AAC3D;AACD,IAAI,iBAAiB,SAAU,QAAQ,SAAS;AAC5C,QAAO,QACF,IAAI,SAAU,QAAQ;AACvB,MAAI,OAAO,SAAS,OAAO,CACvB,QAAO;EAEX,IAAI,kBAAkB,WAAW,OAAO;AACxC,MAAI,mBAAmB,OAAO,SAAS,gBAAgB,CACnD,QAAO;AAEX,UAAQ,MAAM,eAAe,QAAQ,2BAA2B,QAAQ,kBAAkB;AAC1F,SAAO;CACV,EAAC,CACG,OAAO,SAAU,GAAG;AAAE,SAAO,QAAQ,EAAE;CAAG,EAAC;AACnD;;;;;;;;;AASD,IAAI,yBAAyB,SAAU,gBAAgB,YAAY,YAAY,kBAAkB;CAC7F,IAAI,UAAU,eAAe,YAAY,MAAM,QAAQ,eAAe,GAAG,iBAAiB,CAAC,cAAe,EAAC;AAC3G,MAAK,UAAU,YACX,WAAU,8BAAc,IAAI;CAEhC,IAAI,gBAAgB,UAAU;CAC9B,IAAI,cAAc,CAAE;CACpB,IAAI,iCAAiB,IAAI;CACzB,IAAI,iBAAiB,IAAI,IAAI;CAC7B,IAAI,OAAO,SAAU,IAAI;AACrB,OAAK,MAAM,eAAe,IAAI,GAAG,CAC7B;AAEJ,iBAAe,IAAI,GAAG;AACtB,OAAK,GAAG,WAAW;CACtB;AACD,SAAQ,QAAQ,KAAK;CACrB,IAAI,OAAO,SAAU,QAAQ;AACzB,OAAK,UAAU,eAAe,IAAI,OAAO,CACrC;AAEJ,QAAM,UAAU,QAAQ,KAAK,OAAO,UAAU,SAAU,MAAM;AAC1D,OAAI,eAAe,IAAI,KAAK,CACxB,MAAK,KAAK;OAGV,KAAI;IACA,IAAI,OAAO,KAAK,aAAa,iBAAiB;IAC9C,IAAI,gBAAgB,SAAS,QAAQ,SAAS;IAC9C,IAAI,gBAAgB,WAAW,IAAI,KAAK,IAAI,KAAK;IACjD,IAAI,eAAe,cAAc,IAAI,KAAK,IAAI,KAAK;AACnD,eAAW,IAAI,MAAM,aAAa;AAClC,kBAAc,IAAI,MAAM,YAAY;AACpC,gBAAY,KAAK,KAAK;AACtB,QAAI,iBAAiB,KAAK,cACtB,mBAAkB,IAAI,MAAM,KAAK;AAErC,QAAI,gBAAgB,EAChB,MAAK,aAAa,YAAY,OAAO;AAEzC,SAAK,cACD,MAAK,aAAa,kBAAkB,OAAO;GAElD,SACM,GAAG;AACN,YAAQ,MAAM,mCAAmC,MAAM,EAAE;GAC5D;EAER,EAAC;CACL;AACD,MAAK,WAAW;AAChB,gBAAe,OAAO;AACtB;AACA,QAAO,WAAY;AACf,cAAY,QAAQ,SAAU,MAAM;GAChC,IAAI,eAAe,WAAW,IAAI,KAAK,GAAG;GAC1C,IAAI,cAAc,cAAc,IAAI,KAAK,GAAG;AAC5C,cAAW,IAAI,MAAM,aAAa;AAClC,iBAAc,IAAI,MAAM,YAAY;AACpC,QAAK,cAAc;AACf,SAAK,kBAAkB,IAAI,KAAK,CAC5B,MAAK,gBAAgB,iBAAiB;AAE1C,sBAAkB,OAAO,KAAK;GACjC;AACD,QAAK,YACD,MAAK,gBAAgB,WAAW;EAEvC,EAAC;AACF;AACA,OAAK,WAAW;AAEZ,gCAAa,IAAI;AACjB,gCAAa,IAAI;AACjB,uCAAoB,IAAI;AACxB,eAAY,CAAE;EACjB;CACJ;AACJ;;;;;;;;AAQD,IAAW,aAAa,SAAU,gBAAgB,YAAY,YAAY;AACtE,KAAI,oBAAoB,EAAK,cAAa;CAC1C,IAAI,UAAU,MAAM,KAAK,MAAM,QAAQ,eAAe,GAAG,iBAAiB,CAAC,cAAe,EAAC;CAC3F,IAAI,mBAAmB,cAAc,iBAAiB,eAAe;AACrE,MAAK,iBACD,QAAO,WAAY;AAAE,SAAO;CAAO;AAIvC,SAAQ,KAAK,MAAM,SAAS,MAAM,KAAK,iBAAiB,iBAAiB,sBAAsB,CAAC,CAAC;AACjG,QAAO,uBAAuB,SAAS,kBAAkB,YAAY,cAAc;AACtF;;;;AClID,IAAI,OAAO,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACnD,MAAM,EAAE,SAAU,GAAG,WAAW,GAAG;CACnC,MAAM,gBAAgB,aAAM,SAAS,QAAQ,SAAS;CACtD,MAAM,YAAY,cAAc,KAAK,YAAY;AACjD,KAAI,WAAW;EACb,MAAM,aAAa,UAAU,MAAM;EACnC,MAAM,cAAc,cAAc,IAAI,CAAC,UAAU;AAC/C,OAAI,UAAU,WAAW;AACvB,QAAI,aAAM,SAAS,MAAM,WAAW,GAAG,EAAG,QAAO,aAAM,SAAS,KAAK,KAAK;AAC1E,WAAO,aAAM,eAAe,WAAW,GAAG,WAAW,MAAM,WAAW;GACvE,MACC,QAAO;EAEV,EAAC;AACF,yBAAuB,4BAAI,WAAW;GAAE,GAAG;GAAW,KAAK;GAAc,UAAU,aAAM,eAAe,WAAW,GAAG,aAAM,aAAa,iBAAiB,GAAG,YAAY,GAAG;EAAM,EAAC;CACpL;AACD,wBAAuB,4BAAI,WAAW;EAAE,GAAG;EAAW,KAAK;EAAc;CAAU,EAAC;AACrF,EAAC;AACF,KAAK,cAAc;AACnB,IAAI,YAAY,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACxD,MAAM,EAAE,SAAU,GAAG,WAAW,GAAG;AACnC,KAAI,aAAM,eAAe,SAAS,EAAE;EAClC,MAAM,cAAc,cAAc,SAAS;AAC3C,SAAO,aAAM,aAAa,UAAU;GAClC,GAAG,WAAW,WAAW,SAAS,MAAM;GAExC,KAAK,eAAe,YAAY,cAAc,YAAY,GAAG;EAC9D,EAAC;CACH;AACD,QAAO,aAAM,SAAS,MAAM,SAAS,GAAG,IAAI,aAAM,SAAS,KAAK,KAAK,GAAG;AACzE,EAAC;AACF,UAAU,cAAc;AACxB,IAAI,YAAY,CAAC,EAAE,UAAU,KAAK;AAChC,wBAAuB,4BAAIC,6BAAU,EAAE,SAAU,EAAC;AACnD;AACD,SAAS,YAAY,OAAO;AAC1B,QAAO,aAAM,eAAe,MAAM,IAAI,MAAM,SAAS;AACtD;AACD,SAAS,WAAW,WAAW,YAAY;CACzC,MAAM,gBAAgB,EAAE,GAAG,WAAY;AACvC,MAAK,MAAM,YAAY,YAAY;EACjC,MAAM,gBAAgB,UAAU;EAChC,MAAM,iBAAiB,WAAW;EAClC,MAAM,YAAY,WAAW,KAAK,SAAS;AAC3C,MAAI,WACF;OAAI,iBAAiB,eACnB,eAAc,YAAY,CAAC,GAAG,SAAS;AACrC,mBAAe,GAAG,KAAK;AACvB,kBAAc,GAAG,KAAK;GACvB;YACQ,cACT,eAAc,YAAY;EAC3B,WACQ,aAAa,QACtB,eAAc,YAAY;GAAE,GAAG;GAAe,GAAG;EAAgB;WACxD,aAAa,YACtB,eAAc,YAAY,CAAC,eAAe,cAAe,EAAC,OAAO,QAAQ,CAAC,KAAK,IAAI;CAEtF;AACD,QAAO;EAAE,GAAG;EAAW,GAAG;CAAe;AAC1C;AACD,SAAS,cAAc,SAAS;CAC9B,IAAI,SAAS,OAAO,yBAAyB,QAAQ,OAAO,MAAM,EAAE;CACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,KAAI,QACF,QAAO,QAAQ;AAEjB,UAAS,OAAO,yBAAyB,SAAS,MAAM,EAAE;AAC1D,WAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,KAAI,QACF,QAAO,QAAQ,MAAM;AAEvB,QAAO,QAAQ,MAAM,OAAO,QAAQ;AACrC;;;;AC1DD,IAAI,cAAc;AAClB,IAAI,CAAC,qBAAqB,kBAAkB,GAAG,mBAAmB,YAAY;AAC9E,IAAI,CAAC,gBAAgB,iBAAiB,GAAG,oBAAoB,YAAY;AACzE,IAAI,SAAS,CAAC,UAAU;CACtB,MAAM,EACJ,eACA,UACA,MAAM,UACN,aACA,cACA,QAAQ,MACT,GAAG;CACJ,MAAM,aAAa,aAAM,OAAO,KAAK;CACrC,MAAM,aAAa,aAAM,OAAO,KAAK;CACrC,MAAM,CAAC,OAAO,OAAO,QAAQ,GAAG,qBAAqB;EACnD,MAAM;EACN,aAAa;EACb,UAAU;CACX,EAAC;AACF,wBAAuB,4BACrB,gBACA;EACE,OAAO;EACP;EACA;EACA,WAAW,OAAO;EAClB,SAAS,OAAO;EAChB,eAAe,OAAO;EACtB;EACA,cAAc;EACd,cAAc,aAAM,YAAY,MAAM,QAAQ,CAAC,cAAc,SAAS,EAAE,CAAC,OAAQ,EAAC;EAClF;EACA;CACD,EACF;AACF;AACD,OAAO,cAAc;AACrB,IAAI,eAAe;AACnB,IAAI,gBAAgB,aAAM,WACxB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,cAAe,GAAG,cAAc,GAAG;CAC3C,MAAM,UAAU,iBAAiB,cAAc,cAAc;CAC7D,MAAM,qBAAqB,gBAAgB,cAAc,QAAQ,WAAW;AAC5E,wBAAuB,4BACrB,UAAU,QACV;EACE,MAAM;EACN,iBAAiB;EACjB,iBAAiB,QAAQ;EACzB,iBAAiB,QAAQ;EACzB,cAAc,SAAS,QAAQ,KAAK;EACpC,GAAG;EACH,KAAK;EACL,SAAS,qBAAqB,MAAM,SAAS,QAAQ,aAAa;CACnE,EACF;AACF,EACF;AACD,cAAc,cAAc;AAC5B,IAAI,cAAc;AAClB,IAAI,CAAC,gBAAgB,iBAAiB,GAAG,oBAAoB,aAAa,EACxE,iBAAiB,EAClB,EAAC;AACF,IAAI,eAAe,CAAC,UAAU;CAC5B,MAAM,EAAE,eAAe,YAAY,UAAU,WAAW,GAAG;CAC3D,MAAM,UAAU,iBAAiB,aAAa,cAAc;AAC5D,wBAAuB,4BAAI,gBAAgB;EAAE,OAAO;EAAe;EAAY,UAAU,aAAM,SAAS,IAAI,UAAU,CAAC,0BAA0B,4BAAI,UAAU;GAAE,SAAS,cAAc,QAAQ;GAAM,0BAA0B,4BAAIC,UAAiB;IAAE,SAAS;IAAM;IAAW,UAAU;GAAO,EAAC;EAAE,EAAC,CAAC;CAAE,EAAC;AAC3S;AACD,aAAa,cAAc;AAC3B,IAAI,eAAe;AACnB,IAAI,gBAAgB,aAAM,WACxB,CAAC,OAAO,iBAAiB;CACvB,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,cAAc;CACzE,MAAM,EAAE,aAAa,cAAc,WAAY,GAAG,cAAc,GAAG;CACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,cAAc;AACnE,QAAO,QAAQ,wBAAwB,4BAAI,UAAU;EAAE,SAAS,cAAc,QAAQ;EAAM,0BAA0B,4BAAI,mBAAmB;GAAE,GAAG;GAAc,KAAK;EAAc,EAAC;CAAE,EAAC,GAAG;AAC3L,EACF;AACD,cAAc,cAAc;AAC5B,IAAI,oBAAoB,aAAM,WAC5B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,cAAe,GAAG,cAAc,GAAG;CAC3C,MAAM,UAAU,iBAAiB,cAAc,cAAc;AAC7D,wBAGkB,4BAAIC,qBAAc;EAAE,IAAI;EAAM,gBAAgB;EAAM,QAAQ,CAAC,QAAQ,UAAW;EAAE,0BAA0B,4BAC1H,UAAU,KACV;GACE,cAAc,SAAS,QAAQ,KAAK;GACpC,GAAG;GACH,KAAK;GACL,OAAO;IAAE,eAAe;IAAQ,GAAG,aAAa;GAAO;EACxD,EACF;CAAE,EAAC;AAEP,EACF;AACD,IAAI,eAAe;AACnB,IAAI,gBAAgB,aAAM,WACxB,CAAC,OAAO,iBAAiB;CACvB,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,cAAc;CACzE,MAAM,EAAE,aAAa,cAAc,WAAY,GAAG,cAAc,GAAG;CACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,cAAc;AACnE,wBAAuB,4BAAI,UAAU;EAAE,SAAS,cAAc,QAAQ;EAAM,UAAU,QAAQ,wBAAwB,4BAAI,oBAAoB;GAAE,GAAG;GAAc,KAAK;EAAc,EAAC,mBAAmB,4BAAI,uBAAuB;GAAE,GAAG;GAAc,KAAK;EAAc,EAAC;CAAE,EAAC;AAC9Q,EACF;AACD,cAAc,cAAc;AAC5B,IAAI,qBAAqB,aAAM,WAC7B,CAAC,OAAO,iBAAiB;CACvB,MAAM,UAAU,iBAAiB,cAAc,MAAM,cAAc;CACnE,MAAM,aAAa,aAAM,OAAO,KAAK;CACrC,MAAM,eAAe,gBAAgB,cAAc,QAAQ,YAAY,WAAW;AAClF,cAAM,UAAU,MAAM;EACpB,MAAM,UAAU,WAAW;AAC3B,MAAI,QAAS,QAAO,WAAW,QAAQ;CACxC,GAAE,CAAE,EAAC;AACN,wBAAuB,4BACrB,mBACA;EACE,GAAG;EACH,KAAK;EACL,WAAW,QAAQ;EACnB,6BAA6B;EAC7B,kBAAkB,qBAAqB,MAAM,kBAAkB,CAAC,UAAU;AACxE,SAAM,gBAAgB;AACtB,WAAQ,WAAW,SAAS,OAAO;EACpC,EAAC;EACF,sBAAsB,qBAAqB,MAAM,sBAAsB,CAAC,UAAU;GAChF,MAAM,gBAAgB,MAAM,OAAO;GACnC,MAAM,gBAAgB,cAAc,WAAW,KAAK,cAAc,YAAY;GAC9E,MAAM,eAAe,cAAc,WAAW,KAAK;AACnD,OAAI,aAAc,OAAM,gBAAgB;EACzC,EAAC;EACF,gBAAgB,qBACd,MAAM,gBACN,CAAC,UAAU,MAAM,gBAAgB,CAClC;CACF,EACF;AACF,EACF;AACD,IAAI,wBAAwB,aAAM,WAChC,CAAC,OAAO,iBAAiB;CACvB,MAAM,UAAU,iBAAiB,cAAc,MAAM,cAAc;CACnE,MAAM,0BAA0B,aAAM,OAAO,MAAM;CACnD,MAAM,2BAA2B,aAAM,OAAO,MAAM;AACpD,wBAAuB,4BACrB,mBACA;EACE,GAAG;EACH,KAAK;EACL,WAAW;EACX,6BAA6B;EAC7B,kBAAkB,CAAC,UAAU;AAC3B,SAAM,mBAAmB,MAAM;AAC/B,QAAK,MAAM,kBAAkB;AAC3B,SAAK,wBAAwB,QAAS,SAAQ,WAAW,SAAS,OAAO;AACzE,UAAM,gBAAgB;GACvB;AACD,2BAAwB,UAAU;AAClC,4BAAyB,UAAU;EACpC;EACD,mBAAmB,CAAC,UAAU;AAC5B,SAAM,oBAAoB,MAAM;AAChC,QAAK,MAAM,kBAAkB;AAC3B,4BAAwB,UAAU;AAClC,QAAI,MAAM,OAAO,cAAc,SAAS,cACtC,0BAAyB,UAAU;GAEtC;GACD,MAAM,SAAS,MAAM;GACrB,MAAM,kBAAkB,QAAQ,WAAW,SAAS,SAAS,OAAO;AACpE,OAAI,gBAAiB,OAAM,gBAAgB;AAC3C,OAAI,MAAM,OAAO,cAAc,SAAS,aAAa,yBAAyB,QAC5E,OAAM,gBAAgB;EAEzB;CACF,EACF;AACF,EACF;AACD,IAAI,oBAAoB,aAAM,WAC5B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,eAAe,WAAW,iBAAiB,iBAAkB,GAAG,cAAc,GAAG;CACzF,MAAM,UAAU,iBAAiB,cAAc,cAAc;CAC7D,MAAM,aAAa,aAAM,OAAO,KAAK;CACrC,MAAM,eAAe,gBAAgB,cAAc,WAAW;AAC9D,iBAAgB;AAChB,wBAAuB,6BAAKC,6BAAU,EAAE,UAAU,iBAChC,4BACd,YACA;EACE,SAAS;EACT,MAAM;EACN,SAAS;EACT,kBAAkB;EAClB,oBAAoB;EACpB,0BAA0B,4BACxB,kBACA;GACE,MAAM;GACN,IAAI,QAAQ;GACZ,oBAAoB,QAAQ;GAC5B,mBAAmB,QAAQ;GAC3B,cAAc,SAAS,QAAQ,KAAK;GACpC,GAAG;GACH,KAAK;GACL,WAAW,MAAM,QAAQ,aAAa,MAAM;EAC7C,EACF;CACF,EACF,kBACe,6BAAKA,6BAAU,EAAE,UAAU,iBACzB,4BAAI,cAAc,EAAE,SAAS,QAAQ,QAAS,EAAC,kBAC/C,4BAAI,oBAAoB;EAAE;EAAY,eAAe,QAAQ;CAAe,EAAC,AAC9F,EAAE,EAAC,AACL,EAAE,EAAC;AACL,EACF;AACD,IAAI,aAAa;AACjB,IAAI,cAAc,aAAM,WACtB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,cAAe,GAAG,YAAY,GAAG;CACzC,MAAM,UAAU,iBAAiB,YAAY,cAAc;AAC3D,wBAAuB,4BAAI,UAAU,IAAI;EAAE,IAAI,QAAQ;EAAS,GAAG;EAAY,KAAK;CAAc,EAAC;AACpG,EACF;AACD,YAAY,cAAc;AAC1B,IAAI,mBAAmB;AACvB,IAAI,oBAAoB,aAAM,WAC5B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,cAAe,GAAG,kBAAkB,GAAG;CAC/C,MAAM,UAAU,iBAAiB,kBAAkB,cAAc;AACjE,wBAAuB,4BAAI,UAAU,GAAG;EAAE,IAAI,QAAQ;EAAe,GAAG;EAAkB,KAAK;CAAc,EAAC;AAC/G,EACF;AACD,kBAAkB,cAAc;AAChC,IAAI,aAAa;AACjB,IAAI,cAAc,aAAM,WACtB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,cAAe,GAAG,YAAY,GAAG;CACzC,MAAM,UAAU,iBAAiB,YAAY,cAAc;AAC3D,wBAAuB,4BACrB,UAAU,QACV;EACE,MAAM;EACN,GAAG;EACH,KAAK;EACL,SAAS,qBAAqB,MAAM,SAAS,MAAM,QAAQ,aAAa,MAAM,CAAC;CAChF,EACF;AACF,EACF;AACD,YAAY,cAAc;AAC1B,SAAS,SAAS,MAAM;AACtB,QAAO,OAAO,SAAS;AACxB;AACD,IAAI,qBAAqB;AACzB,IAAI,CAAC,iBAAiB,kBAAkB,GAAG,eAAc,oBAAoB;CAC3E,aAAa;CACb,WAAW;CACX,UAAU;AACX,EAAC;AACF,IAAI,eAAe,CAAC,EAAE,SAAS,KAAK;CAClC,MAAM,sBAAsB,kBAAkB,mBAAmB;CACjE,MAAM,WAAW,IAAI,oBAAoB,YAAY,kBAAkB,oBAAoB,UAAU;;4BAE3E,oBAAoB,UAAU;;4EAEkB,oBAAoB;AAC9F,cAAM,UAAU,MAAM;AACpB,MAAI,SAAS;GACX,MAAM,WAAW,SAAS,eAAe,QAAQ;AACjD,QAAK,SAAU,SAAQ,MAAM,QAAQ;EACtC;CACF,GAAE,CAAC,SAAS,OAAQ,EAAC;AACtB,QAAO;AACR;AACD,IAAI,2BAA2B;AAC/B,IAAI,qBAAqB,CAAC,EAAE,YAAY,eAAe,KAAK;CAC1D,MAAM,4BAA4B,kBAAkB,yBAAyB;CAC7E,MAAM,WAAW,4EAA4E,0BAA0B,YAAY;AACnI,cAAM,UAAU,MAAM;EACpB,MAAM,gBAAgB,WAAW,SAAS,aAAa,mBAAmB;AAC1E,MAAI,iBAAiB,eAAe;GAClC,MAAM,iBAAiB,SAAS,eAAe,cAAc;AAC7D,QAAK,eAAgB,SAAQ,KAAK,QAAQ;EAC3C;CACF,GAAE;EAAC;EAAS;EAAY;CAAc,EAAC;AACxC,QAAO;AACR;AACD,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,QAAQ"}