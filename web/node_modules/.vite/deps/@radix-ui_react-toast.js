"use client";


import { __toESM } from "./chunk-CSDACtG1.js";
import { require_react } from "./react-jwkZF-4H.js";
import { require_react_dom } from "./react-dom-yX6PEKSL.js";
import { require_jsx_runtime } from "./jsx-runtime-BaUrS6dR.js";
import { createSlot } from "./dist-t3wzf1ro.js";
import "./dist-CzHXh4Qo.js";

//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/primitive/dist/index.mjs
function composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {
	return function handleEvent(event) {
		originalEventHandler?.(event);
		if (checkForDefaultPrevented === false || !event.defaultPrevented) return ourEventHandler?.(event);
	};
}

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-compose-refs/dist/index.mjs
var import_react = __toESM(require_react(), 1);
function setRef(ref, value) {
	if (typeof ref === "function") return ref(value);
	else if (ref !== null && ref !== void 0) ref.current = value;
}
function composeRefs(...refs) {
	return (node) => {
		let hasCleanup = false;
		const cleanups = refs.map((ref) => {
			const cleanup = setRef(ref, node);
			if (!hasCleanup && typeof cleanup == "function") hasCleanup = true;
			return cleanup;
		});
		if (hasCleanup) return () => {
			for (let i = 0; i < cleanups.length; i++) {
				const cleanup = cleanups[i];
				if (typeof cleanup == "function") cleanup();
				else setRef(refs[i], null);
			}
		};
	};
}
function useComposedRefs(...refs) {
	return import_react.useCallback(composeRefs(...refs), refs);
}

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-context/dist/index.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
function createContextScope(scopeName, createContextScopeDeps = []) {
	let defaultContexts = [];
	function createContext3(rootComponentName, defaultContext) {
		const BaseContext = import_react.createContext(defaultContext);
		const index = defaultContexts.length;
		defaultContexts = [...defaultContexts, defaultContext];
		const Provider$1 = (props) => {
			const { scope, children,...context } = props;
			const Context = scope?.[scopeName]?.[index] || BaseContext;
			const value = import_react.useMemo(() => context, Object.values(context));
			return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Context.Provider, {
				value,
				children
			});
		};
		Provider$1.displayName = rootComponentName + "Provider";
		function useContext2(consumerName, scope) {
			const Context = scope?.[scopeName]?.[index] || BaseContext;
			const context = import_react.useContext(Context);
			if (context) return context;
			if (defaultContext !== void 0) return defaultContext;
			throw new Error(`\`${consumerName}\` must be used within \`${rootComponentName}\``);
		}
		return [Provider$1, useContext2];
	}
	const createScope = () => {
		const scopeContexts = defaultContexts.map((defaultContext) => {
			return import_react.createContext(defaultContext);
		});
		return function useScope(scope) {
			const contexts = scope?.[scopeName] || scopeContexts;
			return import_react.useMemo(() => ({ [`__scope${scopeName}`]: {
				...scope,
				[scopeName]: contexts
			} }), [scope, contexts]);
		};
	};
	createScope.scopeName = scopeName;
	return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];
}
function composeContextScopes(...scopes) {
	const baseScope = scopes[0];
	if (scopes.length === 1) return baseScope;
	const createScope = () => {
		const scopeHooks = scopes.map((createScope2) => ({
			useScope: createScope2(),
			scopeName: createScope2.scopeName
		}));
		return function useComposedScopes(overrideScopes) {
			const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {
				const scopeProps = useScope(overrideScopes);
				const currentScope = scopeProps[`__scope${scopeName}`];
				return {
					...nextScopes2,
					...currentScope
				};
			}, {});
			return import_react.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);
		};
	};
	createScope.scopeName = baseScope.scopeName;
	return createScope;
}

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-collection/dist/index.mjs
function createCollection(name) {
	const PROVIDER_NAME$1 = name + "CollectionProvider";
	const [createCollectionContext, createCollectionScope$1] = createContextScope(PROVIDER_NAME$1);
	const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME$1, {
		collectionRef: { current: null },
		itemMap: /* @__PURE__ */ new Map()
	});
	const CollectionProvider = (props) => {
		const { scope, children } = props;
		const ref = import_react.default.useRef(null);
		const itemMap = import_react.default.useRef(/* @__PURE__ */ new Map()).current;
		return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(CollectionProviderImpl, {
			scope,
			itemMap,
			collectionRef: ref,
			children
		});
	};
	CollectionProvider.displayName = PROVIDER_NAME$1;
	const COLLECTION_SLOT_NAME = name + "CollectionSlot";
	const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);
	const CollectionSlot = import_react.default.forwardRef((props, forwardedRef) => {
		const { scope, children } = props;
		const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);
		const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);
		return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(CollectionSlotImpl, {
			ref: composedRefs,
			children
		});
	});
	CollectionSlot.displayName = COLLECTION_SLOT_NAME;
	const ITEM_SLOT_NAME = name + "CollectionItemSlot";
	const ITEM_DATA_ATTR = "data-radix-collection-item";
	const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);
	const CollectionItemSlot = import_react.default.forwardRef((props, forwardedRef) => {
		const { scope, children,...itemData } = props;
		const ref = import_react.default.useRef(null);
		const composedRefs = useComposedRefs(forwardedRef, ref);
		const context = useCollectionContext(ITEM_SLOT_NAME, scope);
		import_react.default.useEffect(() => {
			context.itemMap.set(ref, {
				ref,
				...itemData
			});
			return () => void context.itemMap.delete(ref);
		});
		return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(CollectionItemSlotImpl, {
			[ITEM_DATA_ATTR]: "",
			ref: composedRefs,
			children
		});
	});
	CollectionItemSlot.displayName = ITEM_SLOT_NAME;
	function useCollection$1(scope) {
		const context = useCollectionContext(name + "CollectionConsumer", scope);
		const getItems = import_react.default.useCallback(() => {
			const collectionNode = context.collectionRef.current;
			if (!collectionNode) return [];
			const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));
			const items = Array.from(context.itemMap.values());
			const orderedItems = items.sort((a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));
			return orderedItems;
		}, [context.collectionRef, context.itemMap]);
		return getItems;
	}
	return [
		{
			Provider: CollectionProvider,
			Slot: CollectionSlot,
			ItemSlot: CollectionItemSlot
		},
		useCollection$1,
		createCollectionScope$1
	];
}

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-primitive/dist/index.mjs
var import_react_dom$2 = __toESM(require_react_dom(), 1);
var NODES = [
	"a",
	"button",
	"div",
	"form",
	"h2",
	"h3",
	"img",
	"input",
	"label",
	"li",
	"nav",
	"ol",
	"p",
	"select",
	"span",
	"svg",
	"ul"
];
var Primitive = NODES.reduce((primitive, node) => {
	const Slot = createSlot(`Primitive.${node}`);
	const Node$1 = import_react.forwardRef((props, forwardedRef) => {
		const { asChild,...primitiveProps } = props;
		const Comp = asChild ? Slot : node;
		if (typeof window !== "undefined") window[Symbol.for("radix-ui")] = true;
		return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Comp, {
			...primitiveProps,
			ref: forwardedRef
		});
	});
	Node$1.displayName = `Primitive.${node}`;
	return {
		...primitive,
		[node]: Node$1
	};
}, {});
function dispatchDiscreteCustomEvent(target, event) {
	if (target) import_react_dom$2.flushSync(() => target.dispatchEvent(event));
}

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs
function useCallbackRef(callback) {
	const callbackRef = import_react.useRef(callback);
	import_react.useEffect(() => {
		callbackRef.current = callback;
	});
	return import_react.useMemo(() => (...args) => callbackRef.current?.(...args), []);
}

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-dismissable-layer/node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs
function useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {
	const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);
	import_react.useEffect(() => {
		const handleKeyDown = (event) => {
			if (event.key === "Escape") onEscapeKeyDown(event);
		};
		ownerDocument.addEventListener("keydown", handleKeyDown, { capture: true });
		return () => ownerDocument.removeEventListener("keydown", handleKeyDown, { capture: true });
	}, [onEscapeKeyDown, ownerDocument]);
}

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs
var DISMISSABLE_LAYER_NAME = "DismissableLayer";
var CONTEXT_UPDATE = "dismissableLayer.update";
var POINTER_DOWN_OUTSIDE = "dismissableLayer.pointerDownOutside";
var FOCUS_OUTSIDE = "dismissableLayer.focusOutside";
var originalBodyPointerEvents;
var DismissableLayerContext = import_react.createContext({
	layers: /* @__PURE__ */ new Set(),
	layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),
	branches: /* @__PURE__ */ new Set()
});
var DismissableLayer = import_react.forwardRef((props, forwardedRef) => {
	const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss,...layerProps } = props;
	const context = import_react.useContext(DismissableLayerContext);
	const [node, setNode] = import_react.useState(null);
	const ownerDocument = node?.ownerDocument ?? globalThis?.document;
	const [, force] = import_react.useState({});
	const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));
	const layers = Array.from(context.layers);
	const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);
	const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);
	const index = node ? layers.indexOf(node) : -1;
	const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;
	const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;
	const pointerDownOutside = usePointerDownOutside((event) => {
		const target = event.target;
		const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));
		if (!isPointerEventsEnabled || isPointerDownOnBranch) return;
		onPointerDownOutside?.(event);
		onInteractOutside?.(event);
		if (!event.defaultPrevented) onDismiss?.();
	}, ownerDocument);
	const focusOutside = useFocusOutside((event) => {
		const target = event.target;
		const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));
		if (isFocusInBranch) return;
		onFocusOutside?.(event);
		onInteractOutside?.(event);
		if (!event.defaultPrevented) onDismiss?.();
	}, ownerDocument);
	useEscapeKeydown((event) => {
		const isHighestLayer = index === context.layers.size - 1;
		if (!isHighestLayer) return;
		onEscapeKeyDown?.(event);
		if (!event.defaultPrevented && onDismiss) {
			event.preventDefault();
			onDismiss();
		}
	}, ownerDocument);
	import_react.useEffect(() => {
		if (!node) return;
		if (disableOutsidePointerEvents) {
			if (context.layersWithOutsidePointerEventsDisabled.size === 0) {
				originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;
				ownerDocument.body.style.pointerEvents = "none";
			}
			context.layersWithOutsidePointerEventsDisabled.add(node);
		}
		context.layers.add(node);
		dispatchUpdate();
		return () => {
			if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;
		};
	}, [
		node,
		ownerDocument,
		disableOutsidePointerEvents,
		context
	]);
	import_react.useEffect(() => {
		return () => {
			if (!node) return;
			context.layers.delete(node);
			context.layersWithOutsidePointerEventsDisabled.delete(node);
			dispatchUpdate();
		};
	}, [node, context]);
	import_react.useEffect(() => {
		const handleUpdate = () => force({});
		document.addEventListener(CONTEXT_UPDATE, handleUpdate);
		return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);
	}, []);
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.div, {
		...layerProps,
		ref: composedRefs,
		style: {
			pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? "auto" : "none" : void 0,
			...props.style
		},
		onFocusCapture: composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),
		onBlurCapture: composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),
		onPointerDownCapture: composeEventHandlers(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)
	});
});
DismissableLayer.displayName = DISMISSABLE_LAYER_NAME;
var BRANCH_NAME = "DismissableLayerBranch";
var DismissableLayerBranch = import_react.forwardRef((props, forwardedRef) => {
	const context = import_react.useContext(DismissableLayerContext);
	const ref = import_react.useRef(null);
	const composedRefs = useComposedRefs(forwardedRef, ref);
	import_react.useEffect(() => {
		const node = ref.current;
		if (node) {
			context.branches.add(node);
			return () => {
				context.branches.delete(node);
			};
		}
	}, [context.branches]);
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.div, {
		...props,
		ref: composedRefs
	});
});
DismissableLayerBranch.displayName = BRANCH_NAME;
function usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {
	const handlePointerDownOutside = useCallbackRef(onPointerDownOutside);
	const isPointerInsideReactTreeRef = import_react.useRef(false);
	const handleClickRef = import_react.useRef(() => {});
	import_react.useEffect(() => {
		const handlePointerDown = (event) => {
			if (event.target && !isPointerInsideReactTreeRef.current) {
				let handleAndDispatchPointerDownOutsideEvent2 = function() {
					handleAndDispatchCustomEvent$1(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, { discrete: true });
				};
				var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;
				const eventDetail = { originalEvent: event };
				if (event.pointerType === "touch") {
					ownerDocument.removeEventListener("click", handleClickRef.current);
					handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;
					ownerDocument.addEventListener("click", handleClickRef.current, { once: true });
				} else handleAndDispatchPointerDownOutsideEvent2();
			} else ownerDocument.removeEventListener("click", handleClickRef.current);
			isPointerInsideReactTreeRef.current = false;
		};
		const timerId = window.setTimeout(() => {
			ownerDocument.addEventListener("pointerdown", handlePointerDown);
		}, 0);
		return () => {
			window.clearTimeout(timerId);
			ownerDocument.removeEventListener("pointerdown", handlePointerDown);
			ownerDocument.removeEventListener("click", handleClickRef.current);
		};
	}, [ownerDocument, handlePointerDownOutside]);
	return { onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true };
}
function useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {
	const handleFocusOutside = useCallbackRef(onFocusOutside);
	const isFocusInsideReactTreeRef = import_react.useRef(false);
	import_react.useEffect(() => {
		const handleFocus = (event) => {
			if (event.target && !isFocusInsideReactTreeRef.current) {
				const eventDetail = { originalEvent: event };
				handleAndDispatchCustomEvent$1(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, { discrete: false });
			}
		};
		ownerDocument.addEventListener("focusin", handleFocus);
		return () => ownerDocument.removeEventListener("focusin", handleFocus);
	}, [ownerDocument, handleFocusOutside]);
	return {
		onFocusCapture: () => isFocusInsideReactTreeRef.current = true,
		onBlurCapture: () => isFocusInsideReactTreeRef.current = false
	};
}
function dispatchUpdate() {
	const event = new CustomEvent(CONTEXT_UPDATE);
	document.dispatchEvent(event);
}
function handleAndDispatchCustomEvent$1(name, handler, detail, { discrete }) {
	const target = detail.originalEvent.target;
	const event = new CustomEvent(name, {
		bubbles: false,
		cancelable: true,
		detail
	});
	if (handler) target.addEventListener(name, handler, { once: true });
	if (discrete) dispatchDiscreteCustomEvent(target, event);
	else target.dispatchEvent(event);
}
var Root = DismissableLayer;
var Branch = DismissableLayerBranch;

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs
var useLayoutEffect2 = globalThis?.document ? import_react.useLayoutEffect : () => {};

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-portal/dist/index.mjs
var import_react_dom$1 = __toESM(require_react_dom(), 1);
var PORTAL_NAME = "Portal";
var Portal = import_react.forwardRef((props, forwardedRef) => {
	const { container: containerProp,...portalProps } = props;
	const [mounted, setMounted] = import_react.useState(false);
	useLayoutEffect2(() => setMounted(true), []);
	const container = containerProp || mounted && globalThis?.document?.body;
	return container ? import_react_dom$1.createPortal(/* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.div, {
		...portalProps,
		ref: forwardedRef
	}), container) : null;
});
Portal.displayName = PORTAL_NAME;

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-presence/dist/index.mjs
function useStateMachine(initialState, machine) {
	return import_react.useReducer((state, event) => {
		const nextState = machine[state][event];
		return nextState ?? state;
	}, initialState);
}
var Presence = (props) => {
	const { present, children } = props;
	const presence = usePresence(present);
	const child = typeof children === "function" ? children({ present: presence.isPresent }) : import_react.Children.only(children);
	const ref = useComposedRefs(presence.ref, getElementRef(child));
	const forceMount = typeof children === "function";
	return forceMount || presence.isPresent ? import_react.cloneElement(child, { ref }) : null;
};
Presence.displayName = "Presence";
function usePresence(present) {
	const [node, setNode] = import_react.useState();
	const stylesRef = import_react.useRef(null);
	const prevPresentRef = import_react.useRef(present);
	const prevAnimationNameRef = import_react.useRef("none");
	const initialState = present ? "mounted" : "unmounted";
	const [state, send] = useStateMachine(initialState, {
		mounted: {
			UNMOUNT: "unmounted",
			ANIMATION_OUT: "unmountSuspended"
		},
		unmountSuspended: {
			MOUNT: "mounted",
			ANIMATION_END: "unmounted"
		},
		unmounted: { MOUNT: "mounted" }
	});
	import_react.useEffect(() => {
		const currentAnimationName = getAnimationName(stylesRef.current);
		prevAnimationNameRef.current = state === "mounted" ? currentAnimationName : "none";
	}, [state]);
	useLayoutEffect2(() => {
		const styles = stylesRef.current;
		const wasPresent = prevPresentRef.current;
		const hasPresentChanged = wasPresent !== present;
		if (hasPresentChanged) {
			const prevAnimationName = prevAnimationNameRef.current;
			const currentAnimationName = getAnimationName(styles);
			if (present) send("MOUNT");
			else if (currentAnimationName === "none" || styles?.display === "none") send("UNMOUNT");
			else {
				const isAnimating = prevAnimationName !== currentAnimationName;
				if (wasPresent && isAnimating) send("ANIMATION_OUT");
				else send("UNMOUNT");
			}
			prevPresentRef.current = present;
		}
	}, [present, send]);
	useLayoutEffect2(() => {
		if (node) {
			let timeoutId;
			const ownerWindow = node.ownerDocument.defaultView ?? window;
			const handleAnimationEnd = (event) => {
				const currentAnimationName = getAnimationName(stylesRef.current);
				const isCurrentAnimation = currentAnimationName.includes(event.animationName);
				if (event.target === node && isCurrentAnimation) {
					send("ANIMATION_END");
					if (!prevPresentRef.current) {
						const currentFillMode = node.style.animationFillMode;
						node.style.animationFillMode = "forwards";
						timeoutId = ownerWindow.setTimeout(() => {
							if (node.style.animationFillMode === "forwards") node.style.animationFillMode = currentFillMode;
						});
					}
				}
			};
			const handleAnimationStart = (event) => {
				if (event.target === node) prevAnimationNameRef.current = getAnimationName(stylesRef.current);
			};
			node.addEventListener("animationstart", handleAnimationStart);
			node.addEventListener("animationcancel", handleAnimationEnd);
			node.addEventListener("animationend", handleAnimationEnd);
			return () => {
				ownerWindow.clearTimeout(timeoutId);
				node.removeEventListener("animationstart", handleAnimationStart);
				node.removeEventListener("animationcancel", handleAnimationEnd);
				node.removeEventListener("animationend", handleAnimationEnd);
			};
		} else send("ANIMATION_END");
	}, [node, send]);
	return {
		isPresent: ["mounted", "unmountSuspended"].includes(state),
		ref: import_react.useCallback((node2) => {
			stylesRef.current = node2 ? getComputedStyle(node2) : null;
			setNode(node2);
		}, [])
	};
}
function getAnimationName(styles) {
	return styles?.animationName || "none";
}
function getElementRef(element) {
	let getter = Object.getOwnPropertyDescriptor(element.props, "ref")?.get;
	let mayWarn = getter && "isReactWarning" in getter && getter.isReactWarning;
	if (mayWarn) return element.ref;
	getter = Object.getOwnPropertyDescriptor(element, "ref")?.get;
	mayWarn = getter && "isReactWarning" in getter && getter.isReactWarning;
	if (mayWarn) return element.props.ref;
	return element.props.ref || element.ref;
}

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs
var useInsertionEffect = import_react[" useInsertionEffect ".trim().toString()] || useLayoutEffect2;
function useControllableState({ prop, defaultProp, onChange = () => {}, caller }) {
	const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({
		defaultProp,
		onChange
	});
	const isControlled = prop !== void 0;
	const value = isControlled ? prop : uncontrolledProp;
	{
		const isControlledRef = import_react.useRef(prop !== void 0);
		import_react.useEffect(() => {
			const wasControlled = isControlledRef.current;
			if (wasControlled !== isControlled) {
				const from = wasControlled ? "controlled" : "uncontrolled";
				const to = isControlled ? "controlled" : "uncontrolled";
				console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);
			}
			isControlledRef.current = isControlled;
		}, [isControlled, caller]);
	}
	const setValue = import_react.useCallback((nextValue) => {
		if (isControlled) {
			const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;
			if (value2 !== prop) onChangeRef.current?.(value2);
		} else setUncontrolledProp(nextValue);
	}, [
		isControlled,
		prop,
		setUncontrolledProp,
		onChangeRef
	]);
	return [value, setValue];
}
function useUncontrolledState({ defaultProp, onChange }) {
	const [value, setValue] = import_react.useState(defaultProp);
	const prevValueRef = import_react.useRef(value);
	const onChangeRef = import_react.useRef(onChange);
	useInsertionEffect(() => {
		onChangeRef.current = onChange;
	}, [onChange]);
	import_react.useEffect(() => {
		if (prevValueRef.current !== value) {
			onChangeRef.current?.(value);
			prevValueRef.current = value;
		}
	}, [value, prevValueRef]);
	return [
		value,
		setValue,
		onChangeRef
	];
}
function isFunction(value) {
	return typeof value === "function";
}
var SYNC_STATE = Symbol("RADIX:SYNC_STATE");

//#endregion
//#region node_modules/@radix-ui/react-toast/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs
var VISUALLY_HIDDEN_STYLES = Object.freeze({
	position: "absolute",
	border: 0,
	width: 1,
	height: 1,
	padding: 0,
	margin: -1,
	overflow: "hidden",
	clip: "rect(0, 0, 0, 0)",
	whiteSpace: "nowrap",
	wordWrap: "normal"
});
var NAME = "VisuallyHidden";
var VisuallyHidden = import_react.forwardRef((props, forwardedRef) => {
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.span, {
		...props,
		ref: forwardedRef,
		style: {
			...VISUALLY_HIDDEN_STYLES,
			...props.style
		}
	});
});
VisuallyHidden.displayName = NAME;

//#endregion
//#region node_modules/@radix-ui/react-toast/dist/index.mjs
var import_react_dom = __toESM(require_react_dom(), 1);
var PROVIDER_NAME = "ToastProvider";
var [Collection, useCollection, createCollectionScope] = createCollection("Toast");
var [createToastContext, createToastScope] = createContextScope("Toast", [createCollectionScope]);
var [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);
var ToastProvider = (props) => {
	const { __scopeToast, label = "Notification", duration = 5e3, swipeDirection = "right", swipeThreshold = 50, children } = props;
	const [viewport, setViewport] = import_react.useState(null);
	const [toastCount, setToastCount] = import_react.useState(0);
	const isFocusedToastEscapeKeyDownRef = import_react.useRef(false);
	const isClosePausedRef = import_react.useRef(false);
	if (!label.trim()) console.error(`Invalid prop \`label\` supplied to \`${PROVIDER_NAME}\`. Expected non-empty \`string\`.`);
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Collection.Provider, {
		scope: __scopeToast,
		children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(ToastProviderProvider, {
			scope: __scopeToast,
			label,
			duration,
			swipeDirection,
			swipeThreshold,
			toastCount,
			viewport,
			onViewportChange: setViewport,
			onToastAdd: import_react.useCallback(() => setToastCount((prevCount) => prevCount + 1), []),
			onToastRemove: import_react.useCallback(() => setToastCount((prevCount) => prevCount - 1), []),
			isFocusedToastEscapeKeyDownRef,
			isClosePausedRef,
			children
		})
	});
};
ToastProvider.displayName = PROVIDER_NAME;
var VIEWPORT_NAME = "ToastViewport";
var VIEWPORT_DEFAULT_HOTKEY = ["F8"];
var VIEWPORT_PAUSE = "toast.viewportPause";
var VIEWPORT_RESUME = "toast.viewportResume";
var ToastViewport = import_react.forwardRef((props, forwardedRef) => {
	const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = "Notifications ({hotkey})",...viewportProps } = props;
	const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);
	const getItems = useCollection(__scopeToast);
	const wrapperRef = import_react.useRef(null);
	const headFocusProxyRef = import_react.useRef(null);
	const tailFocusProxyRef = import_react.useRef(null);
	const ref = import_react.useRef(null);
	const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);
	const hotkeyLabel = hotkey.join("+").replace(/Key/g, "").replace(/Digit/g, "");
	const hasToasts = context.toastCount > 0;
	import_react.useEffect(() => {
		const handleKeyDown = (event) => {
			const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key) => event[key] || event.code === key);
			if (isHotkeyPressed) ref.current?.focus();
		};
		document.addEventListener("keydown", handleKeyDown);
		return () => document.removeEventListener("keydown", handleKeyDown);
	}, [hotkey]);
	import_react.useEffect(() => {
		const wrapper = wrapperRef.current;
		const viewport = ref.current;
		if (hasToasts && wrapper && viewport) {
			const handlePause = () => {
				if (!context.isClosePausedRef.current) {
					const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);
					viewport.dispatchEvent(pauseEvent);
					context.isClosePausedRef.current = true;
				}
			};
			const handleResume = () => {
				if (context.isClosePausedRef.current) {
					const resumeEvent = new CustomEvent(VIEWPORT_RESUME);
					viewport.dispatchEvent(resumeEvent);
					context.isClosePausedRef.current = false;
				}
			};
			const handleFocusOutResume = (event) => {
				const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);
				if (isFocusMovingOutside) handleResume();
			};
			const handlePointerLeaveResume = () => {
				const isFocusInside = wrapper.contains(document.activeElement);
				if (!isFocusInside) handleResume();
			};
			wrapper.addEventListener("focusin", handlePause);
			wrapper.addEventListener("focusout", handleFocusOutResume);
			wrapper.addEventListener("pointermove", handlePause);
			wrapper.addEventListener("pointerleave", handlePointerLeaveResume);
			window.addEventListener("blur", handlePause);
			window.addEventListener("focus", handleResume);
			return () => {
				wrapper.removeEventListener("focusin", handlePause);
				wrapper.removeEventListener("focusout", handleFocusOutResume);
				wrapper.removeEventListener("pointermove", handlePause);
				wrapper.removeEventListener("pointerleave", handlePointerLeaveResume);
				window.removeEventListener("blur", handlePause);
				window.removeEventListener("focus", handleResume);
			};
		}
	}, [hasToasts, context.isClosePausedRef]);
	const getSortedTabbableCandidates = import_react.useCallback(({ tabbingDirection }) => {
		const toastItems = getItems();
		const tabbableCandidates = toastItems.map((toastItem) => {
			const toastNode = toastItem.ref.current;
			const toastTabbableCandidates = [toastNode, ...getTabbableCandidates(toastNode)];
			return tabbingDirection === "forwards" ? toastTabbableCandidates : toastTabbableCandidates.reverse();
		});
		return (tabbingDirection === "forwards" ? tabbableCandidates.reverse() : tabbableCandidates).flat();
	}, [getItems]);
	import_react.useEffect(() => {
		const viewport = ref.current;
		if (viewport) {
			const handleKeyDown = (event) => {
				const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;
				const isTabKey = event.key === "Tab" && !isMetaKey;
				if (isTabKey) {
					const focusedElement = document.activeElement;
					const isTabbingBackwards = event.shiftKey;
					const targetIsViewport = event.target === viewport;
					if (targetIsViewport && isTabbingBackwards) {
						headFocusProxyRef.current?.focus();
						return;
					}
					const tabbingDirection = isTabbingBackwards ? "backwards" : "forwards";
					const sortedCandidates = getSortedTabbableCandidates({ tabbingDirection });
					const index = sortedCandidates.findIndex((candidate) => candidate === focusedElement);
					if (focusFirst(sortedCandidates.slice(index + 1))) event.preventDefault();
					else isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();
				}
			};
			viewport.addEventListener("keydown", handleKeyDown);
			return () => viewport.removeEventListener("keydown", handleKeyDown);
		}
	}, [getItems, getSortedTabbableCandidates]);
	return /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(Branch, {
		ref: wrapperRef,
		role: "region",
		"aria-label": label.replace("{hotkey}", hotkeyLabel),
		tabIndex: -1,
		style: { pointerEvents: hasToasts ? void 0 : "none" },
		children: [
			hasToasts && /* @__PURE__ */ (0, import_jsx_runtime.jsx)(FocusProxy, {
				ref: headFocusProxyRef,
				onFocusFromOutsideViewport: () => {
					const tabbableCandidates = getSortedTabbableCandidates({ tabbingDirection: "forwards" });
					focusFirst(tabbableCandidates);
				}
			}),
			/* @__PURE__ */ (0, import_jsx_runtime.jsx)(Collection.Slot, {
				scope: __scopeToast,
				children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.ol, {
					tabIndex: -1,
					...viewportProps,
					ref: composedRefs
				})
			}),
			hasToasts && /* @__PURE__ */ (0, import_jsx_runtime.jsx)(FocusProxy, {
				ref: tailFocusProxyRef,
				onFocusFromOutsideViewport: () => {
					const tabbableCandidates = getSortedTabbableCandidates({ tabbingDirection: "backwards" });
					focusFirst(tabbableCandidates);
				}
			})
		]
	});
});
ToastViewport.displayName = VIEWPORT_NAME;
var FOCUS_PROXY_NAME = "ToastFocusProxy";
var FocusProxy = import_react.forwardRef((props, forwardedRef) => {
	const { __scopeToast, onFocusFromOutsideViewport,...proxyProps } = props;
	const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(VisuallyHidden, {
		"aria-hidden": true,
		tabIndex: 0,
		...proxyProps,
		ref: forwardedRef,
		style: { position: "fixed" },
		onFocus: (event) => {
			const prevFocusedElement = event.relatedTarget;
			const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);
			if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();
		}
	});
});
FocusProxy.displayName = FOCUS_PROXY_NAME;
var TOAST_NAME = "Toast";
var TOAST_SWIPE_START = "toast.swipeStart";
var TOAST_SWIPE_MOVE = "toast.swipeMove";
var TOAST_SWIPE_CANCEL = "toast.swipeCancel";
var TOAST_SWIPE_END = "toast.swipeEnd";
var Toast = import_react.forwardRef((props, forwardedRef) => {
	const { forceMount, open: openProp, defaultOpen, onOpenChange,...toastProps } = props;
	const [open, setOpen] = useControllableState({
		prop: openProp,
		defaultProp: defaultOpen ?? true,
		onChange: onOpenChange,
		caller: TOAST_NAME
	});
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Presence, {
		present: forceMount || open,
		children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(ToastImpl, {
			open,
			...toastProps,
			ref: forwardedRef,
			onClose: () => setOpen(false),
			onPause: useCallbackRef(props.onPause),
			onResume: useCallbackRef(props.onResume),
			onSwipeStart: composeEventHandlers(props.onSwipeStart, (event) => {
				event.currentTarget.setAttribute("data-swipe", "start");
			}),
			onSwipeMove: composeEventHandlers(props.onSwipeMove, (event) => {
				const { x, y } = event.detail.delta;
				event.currentTarget.setAttribute("data-swipe", "move");
				event.currentTarget.style.setProperty("--radix-toast-swipe-move-x", `${x}px`);
				event.currentTarget.style.setProperty("--radix-toast-swipe-move-y", `${y}px`);
			}),
			onSwipeCancel: composeEventHandlers(props.onSwipeCancel, (event) => {
				event.currentTarget.setAttribute("data-swipe", "cancel");
				event.currentTarget.style.removeProperty("--radix-toast-swipe-move-x");
				event.currentTarget.style.removeProperty("--radix-toast-swipe-move-y");
				event.currentTarget.style.removeProperty("--radix-toast-swipe-end-x");
				event.currentTarget.style.removeProperty("--radix-toast-swipe-end-y");
			}),
			onSwipeEnd: composeEventHandlers(props.onSwipeEnd, (event) => {
				const { x, y } = event.detail.delta;
				event.currentTarget.setAttribute("data-swipe", "end");
				event.currentTarget.style.removeProperty("--radix-toast-swipe-move-x");
				event.currentTarget.style.removeProperty("--radix-toast-swipe-move-y");
				event.currentTarget.style.setProperty("--radix-toast-swipe-end-x", `${x}px`);
				event.currentTarget.style.setProperty("--radix-toast-swipe-end-y", `${y}px`);
				setOpen(false);
			})
		})
	});
});
Toast.displayName = TOAST_NAME;
var [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, { onClose() {} });
var ToastImpl = import_react.forwardRef((props, forwardedRef) => {
	const { __scopeToast, type = "foreground", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd,...toastProps } = props;
	const context = useToastProviderContext(TOAST_NAME, __scopeToast);
	const [node, setNode] = import_react.useState(null);
	const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));
	const pointerStartRef = import_react.useRef(null);
	const swipeDeltaRef = import_react.useRef(null);
	const duration = durationProp || context.duration;
	const closeTimerStartTimeRef = import_react.useRef(0);
	const closeTimerRemainingTimeRef = import_react.useRef(duration);
	const closeTimerRef = import_react.useRef(0);
	const { onToastAdd, onToastRemove } = context;
	const handleClose = useCallbackRef(() => {
		const isFocusInToast = node?.contains(document.activeElement);
		if (isFocusInToast) context.viewport?.focus();
		onClose();
	});
	const startTimer = import_react.useCallback((duration2) => {
		if (!duration2 || duration2 === Infinity) return;
		window.clearTimeout(closeTimerRef.current);
		closeTimerStartTimeRef.current = (/* @__PURE__ */ new Date()).getTime();
		closeTimerRef.current = window.setTimeout(handleClose, duration2);
	}, [handleClose]);
	import_react.useEffect(() => {
		const viewport = context.viewport;
		if (viewport) {
			const handleResume = () => {
				startTimer(closeTimerRemainingTimeRef.current);
				onResume?.();
			};
			const handlePause = () => {
				const elapsedTime = (/* @__PURE__ */ new Date()).getTime() - closeTimerStartTimeRef.current;
				closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;
				window.clearTimeout(closeTimerRef.current);
				onPause?.();
			};
			viewport.addEventListener(VIEWPORT_PAUSE, handlePause);
			viewport.addEventListener(VIEWPORT_RESUME, handleResume);
			return () => {
				viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);
				viewport.removeEventListener(VIEWPORT_RESUME, handleResume);
			};
		}
	}, [
		context.viewport,
		duration,
		onPause,
		onResume,
		startTimer
	]);
	import_react.useEffect(() => {
		if (open && !context.isClosePausedRef.current) startTimer(duration);
	}, [
		open,
		duration,
		context.isClosePausedRef,
		startTimer
	]);
	import_react.useEffect(() => {
		onToastAdd();
		return () => onToastRemove();
	}, [onToastAdd, onToastRemove]);
	const announceTextContent = import_react.useMemo(() => {
		return node ? getAnnounceTextContent(node) : null;
	}, [node]);
	if (!context.viewport) return null;
	return /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [announceTextContent && /* @__PURE__ */ (0, import_jsx_runtime.jsx)(ToastAnnounce, {
		__scopeToast,
		role: "status",
		"aria-live": type === "foreground" ? "assertive" : "polite",
		"aria-atomic": true,
		children: announceTextContent
	}), /* @__PURE__ */ (0, import_jsx_runtime.jsx)(ToastInteractiveProvider, {
		scope: __scopeToast,
		onClose: handleClose,
		children: import_react_dom.createPortal(/* @__PURE__ */ (0, import_jsx_runtime.jsx)(Collection.ItemSlot, {
			scope: __scopeToast,
			children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Root, {
				asChild: true,
				onEscapeKeyDown: composeEventHandlers(onEscapeKeyDown, () => {
					if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();
					context.isFocusedToastEscapeKeyDownRef.current = false;
				}),
				children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.li, {
					role: "status",
					"aria-live": "off",
					"aria-atomic": true,
					tabIndex: 0,
					"data-state": open ? "open" : "closed",
					"data-swipe-direction": context.swipeDirection,
					...toastProps,
					ref: composedRefs,
					style: {
						userSelect: "none",
						touchAction: "none",
						...props.style
					},
					onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {
						if (event.key !== "Escape") return;
						onEscapeKeyDown?.(event.nativeEvent);
						if (!event.nativeEvent.defaultPrevented) {
							context.isFocusedToastEscapeKeyDownRef.current = true;
							handleClose();
						}
					}),
					onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {
						if (event.button !== 0) return;
						pointerStartRef.current = {
							x: event.clientX,
							y: event.clientY
						};
					}),
					onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {
						if (!pointerStartRef.current) return;
						const x = event.clientX - pointerStartRef.current.x;
						const y = event.clientY - pointerStartRef.current.y;
						const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);
						const isHorizontalSwipe = ["left", "right"].includes(context.swipeDirection);
						const clamp = ["left", "up"].includes(context.swipeDirection) ? Math.min : Math.max;
						const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;
						const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;
						const moveStartBuffer = event.pointerType === "touch" ? 10 : 2;
						const delta = {
							x: clampedX,
							y: clampedY
						};
						const eventDetail = {
							originalEvent: event,
							delta
						};
						if (hasSwipeMoveStarted) {
							swipeDeltaRef.current = delta;
							handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, { discrete: false });
						} else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {
							swipeDeltaRef.current = delta;
							handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, { discrete: false });
							event.target.setPointerCapture(event.pointerId);
						} else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) pointerStartRef.current = null;
					}),
					onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {
						const delta = swipeDeltaRef.current;
						const target = event.target;
						if (target.hasPointerCapture(event.pointerId)) target.releasePointerCapture(event.pointerId);
						swipeDeltaRef.current = null;
						pointerStartRef.current = null;
						if (delta) {
							const toast = event.currentTarget;
							const eventDetail = {
								originalEvent: event,
								delta
							};
							if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, { discrete: true });
							else handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, { discrete: true });
							toast.addEventListener("click", (event2) => event2.preventDefault(), { once: true });
						}
					})
				})
			})
		}), context.viewport)
	})] });
});
var ToastAnnounce = (props) => {
	const { __scopeToast, children,...announceProps } = props;
	const context = useToastProviderContext(TOAST_NAME, __scopeToast);
	const [renderAnnounceText, setRenderAnnounceText] = import_react.useState(false);
	const [isAnnounced, setIsAnnounced] = import_react.useState(false);
	useNextFrame(() => setRenderAnnounceText(true));
	import_react.useEffect(() => {
		const timer = window.setTimeout(() => setIsAnnounced(true), 1e3);
		return () => window.clearTimeout(timer);
	}, []);
	return isAnnounced ? null : /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Portal, {
		asChild: true,
		children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(VisuallyHidden, {
			...announceProps,
			children: renderAnnounceText && /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
				context.label,
				" ",
				children
			] })
		})
	});
};
var TITLE_NAME = "ToastTitle";
var ToastTitle = import_react.forwardRef((props, forwardedRef) => {
	const { __scopeToast,...titleProps } = props;
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.div, {
		...titleProps,
		ref: forwardedRef
	});
});
ToastTitle.displayName = TITLE_NAME;
var DESCRIPTION_NAME = "ToastDescription";
var ToastDescription = import_react.forwardRef((props, forwardedRef) => {
	const { __scopeToast,...descriptionProps } = props;
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.div, {
		...descriptionProps,
		ref: forwardedRef
	});
});
ToastDescription.displayName = DESCRIPTION_NAME;
var ACTION_NAME = "ToastAction";
var ToastAction = import_react.forwardRef((props, forwardedRef) => {
	const { altText,...actionProps } = props;
	if (!altText.trim()) {
		console.error(`Invalid prop \`altText\` supplied to \`${ACTION_NAME}\`. Expected non-empty \`string\`.`);
		return null;
	}
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(ToastAnnounceExclude, {
		altText,
		asChild: true,
		children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(ToastClose, {
			...actionProps,
			ref: forwardedRef
		})
	});
});
ToastAction.displayName = ACTION_NAME;
var CLOSE_NAME = "ToastClose";
var ToastClose = import_react.forwardRef((props, forwardedRef) => {
	const { __scopeToast,...closeProps } = props;
	const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(ToastAnnounceExclude, {
		asChild: true,
		children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.button, {
			type: "button",
			...closeProps,
			ref: forwardedRef,
			onClick: composeEventHandlers(props.onClick, interactiveContext.onClose)
		})
	});
});
ToastClose.displayName = CLOSE_NAME;
var ToastAnnounceExclude = import_react.forwardRef((props, forwardedRef) => {
	const { __scopeToast, altText,...announceExcludeProps } = props;
	return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Primitive.div, {
		"data-radix-toast-announce-exclude": "",
		"data-radix-toast-announce-alt": altText || void 0,
		...announceExcludeProps,
		ref: forwardedRef
	});
});
function getAnnounceTextContent(container) {
	const textContent = [];
	const childNodes = Array.from(container.childNodes);
	childNodes.forEach((node) => {
		if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);
		if (isHTMLElement(node)) {
			const isHidden = node.ariaHidden || node.hidden || node.style.display === "none";
			const isExcluded = node.dataset.radixToastAnnounceExclude === "";
			if (!isHidden) if (isExcluded) {
				const altText = node.dataset.radixToastAnnounceAlt;
				if (altText) textContent.push(altText);
			} else textContent.push(...getAnnounceTextContent(node));
		}
	});
	return textContent;
}
function handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {
	const currentTarget = detail.originalEvent.currentTarget;
	const event = new CustomEvent(name, {
		bubbles: true,
		cancelable: true,
		detail
	});
	if (handler) currentTarget.addEventListener(name, handler, { once: true });
	if (discrete) dispatchDiscreteCustomEvent(currentTarget, event);
	else currentTarget.dispatchEvent(event);
}
var isDeltaInDirection = (delta, direction, threshold = 0) => {
	const deltaX = Math.abs(delta.x);
	const deltaY = Math.abs(delta.y);
	const isDeltaX = deltaX > deltaY;
	if (direction === "left" || direction === "right") return isDeltaX && deltaX > threshold;
	else return !isDeltaX && deltaY > threshold;
};
function useNextFrame(callback = () => {}) {
	const fn = useCallbackRef(callback);
	useLayoutEffect2(() => {
		let raf1 = 0;
		let raf2 = 0;
		raf1 = window.requestAnimationFrame(() => raf2 = window.requestAnimationFrame(fn));
		return () => {
			window.cancelAnimationFrame(raf1);
			window.cancelAnimationFrame(raf2);
		};
	}, [fn]);
}
function isHTMLElement(node) {
	return node.nodeType === node.ELEMENT_NODE;
}
function getTabbableCandidates(container) {
	const nodes = [];
	const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, { acceptNode: (node) => {
		const isHiddenInput = node.tagName === "INPUT" && node.type === "hidden";
		if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;
		return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;
	} });
	while (walker.nextNode()) nodes.push(walker.currentNode);
	return nodes;
}
function focusFirst(candidates) {
	const previouslyFocusedElement = document.activeElement;
	return candidates.some((candidate) => {
		if (candidate === previouslyFocusedElement) return true;
		candidate.focus();
		return document.activeElement !== previouslyFocusedElement;
	});
}
var Provider = ToastProvider;
var Viewport = ToastViewport;
var Root2 = Toast;
var Title = ToastTitle;
var Description = ToastDescription;
var Action = ToastAction;
var Close = ToastClose;

//#endregion
export { Action, Close, Description, Provider, Root2 as Root, Title, Toast, ToastAction, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport, Viewport, createToastScope };
//# sourceMappingURL=@radix-ui_react-toast.js.map