{"version": 3, "file": "@openrouter_ai-sdk-provider.js", "names": ["UnsupportedFunctionalityError2", "UnsupportedFunctionalityError3"], "sources": ["../../@openrouter/ai-sdk-provider/dist/index.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/openrouter-facade.ts\nimport { loadApiKey, withoutTrailingSlash } from \"@ai-sdk/provider-utils\";\n\n// src/schemas/reasoning-details.ts\nimport { z } from \"zod\";\nvar ReasoningDetailSummarySchema = z.object({\n  type: z.literal(\"reasoning.summary\" /* Summary */),\n  summary: z.string()\n});\nvar ReasoningDetailEncryptedSchema = z.object({\n  type: z.literal(\"reasoning.encrypted\" /* Encrypted */),\n  data: z.string()\n});\nvar ReasoningDetailTextSchema = z.object({\n  type: z.literal(\"reasoning.text\" /* Text */),\n  text: z.string().nullish(),\n  signature: z.string().nullish()\n});\nvar ReasoningDetailUnionSchema = z.union([\n  ReasoningDetailSummarySchema,\n  ReasoningDetailEncryptedSchema,\n  ReasoningDetailTextSchema\n]);\nvar ReasoningDetailsWithUnknownSchema = z.union([\n  ReasoningDetailUnionSchema,\n  z.unknown().transform(() => null)\n]);\nvar ReasoningDetailArraySchema = z.array(ReasoningDetailsWithUnknownSchema).transform((d) => d.filter((d2) => !!d2));\n\n// src/openrouter-chat-language-model.ts\nimport {\n  InvalidResponseDataError,\n  UnsupportedFunctionalityError\n} from \"@ai-sdk/provider\";\nimport {\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  isParsableJson,\n  postJsonToApi\n} from \"@ai-sdk/provider-utils\";\nimport { z as z3 } from \"zod\";\n\n// src/convert-to-openrouter-chat-messages.ts\nimport { convertUint8ArrayToBase64 } from \"@ai-sdk/provider-utils\";\nfunction getCacheControl(providerMetadata) {\n  var _a, _b, _c;\n  const anthropic = providerMetadata == null ? void 0 : providerMetadata.anthropic;\n  const openrouter2 = providerMetadata == null ? void 0 : providerMetadata.openrouter;\n  return (_c = (_b = (_a = openrouter2 == null ? void 0 : openrouter2.cacheControl) != null ? _a : openrouter2 == null ? void 0 : openrouter2.cache_control) != null ? _b : anthropic == null ? void 0 : anthropic.cacheControl) != null ? _c : anthropic == null ? void 0 : anthropic.cache_control;\n}\nfunction convertToOpenRouterChatMessages(prompt) {\n  var _a, _b, _c;\n  const messages = [];\n  for (const { role, content, providerMetadata } of prompt) {\n    switch (role) {\n      case \"system\": {\n        messages.push({\n          role: \"system\",\n          content,\n          cache_control: getCacheControl(providerMetadata)\n        });\n        break;\n      }\n      case \"user\": {\n        if (content.length === 1 && ((_a = content[0]) == null ? void 0 : _a.type) === \"text\") {\n          messages.push({\n            role: \"user\",\n            content: content[0].text,\n            cache_control: (_b = getCacheControl(providerMetadata)) != null ? _b : getCacheControl(content[0].providerMetadata)\n          });\n          break;\n        }\n        const messageCacheControl = getCacheControl(providerMetadata);\n        const contentParts = content.map(\n          (part) => {\n            var _a2, _b2, _c2, _d;\n            const cacheControl = (_a2 = getCacheControl(part.providerMetadata)) != null ? _a2 : messageCacheControl;\n            switch (part.type) {\n              case \"text\":\n                return {\n                  type: \"text\",\n                  text: part.text,\n                  // For text parts, only use part-specific cache control\n                  cache_control: cacheControl\n                };\n              case \"image\":\n                return {\n                  type: \"image_url\",\n                  image_url: {\n                    url: part.image instanceof URL ? part.image.toString() : `data:${(_b2 = part.mimeType) != null ? _b2 : \"image/jpeg\"};base64,${convertUint8ArrayToBase64(\n                      part.image\n                    )}`\n                  },\n                  // For image parts, use part-specific or message-level cache control\n                  cache_control: cacheControl\n                };\n              case \"file\":\n                return {\n                  type: \"file\",\n                  file: {\n                    filename: String(\n                      (_d = (_c2 = part.providerMetadata) == null ? void 0 : _c2.openrouter) == null ? void 0 : _d.filename\n                    ),\n                    file_data: part.data instanceof Uint8Array ? `data:${part.mimeType};base64,${convertUint8ArrayToBase64(part.data)}` : `data:${part.mimeType};base64,${part.data}`\n                  },\n                  cache_control: cacheControl\n                };\n              default: {\n                const _exhaustiveCheck = part;\n                throw new Error(\n                  `Unsupported content part type: ${_exhaustiveCheck}`\n                );\n              }\n            }\n          }\n        );\n        messages.push({\n          role: \"user\",\n          content: contentParts\n        });\n        break;\n      }\n      case \"assistant\": {\n        let text = \"\";\n        let reasoning = \"\";\n        const reasoningDetails = [];\n        const toolCalls = [];\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              text += part.text;\n              break;\n            }\n            case \"tool-call\": {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: \"function\",\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args)\n                }\n              });\n              break;\n            }\n            case \"reasoning\": {\n              reasoning += part.text;\n              reasoningDetails.push({\n                type: \"reasoning.text\" /* Text */,\n                text: part.text,\n                signature: part.signature\n              });\n              break;\n            }\n            case \"redacted-reasoning\": {\n              reasoningDetails.push({\n                type: \"reasoning.encrypted\" /* Encrypted */,\n                data: part.data\n              });\n              break;\n            }\n            case \"file\":\n              break;\n            default: {\n              const _exhaustiveCheck = part;\n              throw new Error(`Unsupported part: ${_exhaustiveCheck}`);\n            }\n          }\n        }\n        messages.push({\n          role: \"assistant\",\n          content: text,\n          tool_calls: toolCalls.length > 0 ? toolCalls : void 0,\n          reasoning: reasoning || void 0,\n          reasoning_details: reasoningDetails.length > 0 ? reasoningDetails : void 0,\n          cache_control: getCacheControl(providerMetadata)\n        });\n        break;\n      }\n      case \"tool\": {\n        for (const toolResponse of content) {\n          messages.push({\n            role: \"tool\",\n            tool_call_id: toolResponse.toolCallId,\n            content: JSON.stringify(toolResponse.result),\n            cache_control: (_c = getCacheControl(providerMetadata)) != null ? _c : getCacheControl(toolResponse.providerMetadata)\n          });\n        }\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return messages;\n}\n\n// src/map-openrouter-chat-logprobs.ts\nfunction mapOpenRouterChatLogProbsOutput(logprobs) {\n  var _a, _b;\n  return (_b = (_a = logprobs == null ? void 0 : logprobs.content) == null ? void 0 : _a.map(({ token, logprob, top_logprobs }) => ({\n    token,\n    logprob,\n    topLogprobs: top_logprobs ? top_logprobs.map(({ token: token2, logprob: logprob2 }) => ({\n      token: token2,\n      logprob: logprob2\n    })) : []\n  }))) != null ? _b : void 0;\n}\n\n// src/map-openrouter-finish-reason.ts\nfunction mapOpenRouterFinishReason(finishReason) {\n  switch (finishReason) {\n    case \"stop\":\n      return \"stop\";\n    case \"length\":\n      return \"length\";\n    case \"content_filter\":\n      return \"content-filter\";\n    case \"function_call\":\n    case \"tool_calls\":\n      return \"tool-calls\";\n    default:\n      return \"unknown\";\n  }\n}\n\n// src/openrouter-error.ts\nimport { createJsonErrorResponseHandler } from \"@ai-sdk/provider-utils\";\nimport { z as z2 } from \"zod\";\nvar OpenRouterErrorResponseSchema = z2.object({\n  error: z2.object({\n    code: z2.union([z2.string(), z2.number()]).nullable(),\n    message: z2.string(),\n    type: z2.string().nullable(),\n    param: z2.any().nullable()\n  })\n});\nvar openrouterFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: OpenRouterErrorResponseSchema,\n  errorToMessage: (data) => data.error.message\n});\n\n// src/openrouter-chat-language-model.ts\nfunction isFunctionTool(tool) {\n  return \"parameters\" in tool;\n}\nvar OpenRouterChatLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = \"tool\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    frequencyPenalty,\n    presencePenalty,\n    seed,\n    stopSequences,\n    responseFormat,\n    topK,\n    providerMetadata\n  }) {\n    var _a;\n    const type = mode.type;\n    const extraCallingBody = (_a = providerMetadata == null ? void 0 : providerMetadata.openrouter) != null ? _a : {};\n    const baseArgs = __spreadValues(__spreadValues(__spreadValues({\n      // model id:\n      model: this.modelId,\n      models: this.settings.models,\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs: this.settings.logprobs === true || typeof this.settings.logprobs === \"number\" ? true : void 0,\n      top_logprobs: typeof this.settings.logprobs === \"number\" ? this.settings.logprobs : typeof this.settings.logprobs === \"boolean\" ? this.settings.logprobs ? 0 : void 0 : void 0,\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n      stop: stopSequences,\n      response_format: responseFormat,\n      top_k: topK,\n      // messages:\n      messages: convertToOpenRouterChatMessages(prompt),\n      // OpenRouter specific settings:\n      include_reasoning: this.settings.includeReasoning,\n      reasoning: this.settings.reasoning,\n      usage: this.settings.usage\n    }, this.config.extraBody), this.settings.extraBody), extraCallingBody);\n    switch (type) {\n      case \"regular\": {\n        return __spreadValues(__spreadValues({}, baseArgs), prepareToolsAndToolChoice(mode));\n      }\n      case \"object-json\": {\n        return __spreadProps(__spreadValues({}, baseArgs), {\n          response_format: { type: \"json_object\" }\n        });\n      }\n      case \"object-tool\": {\n        return __spreadProps(__spreadValues({}, baseArgs), {\n          tool_choice: { type: \"function\", function: { name: mode.tool.name } },\n          tools: [\n            {\n              type: \"function\",\n              function: {\n                name: mode.tool.name,\n                description: mode.tool.description,\n                parameters: mode.tool.parameters\n              }\n            }\n          ]\n        });\n      }\n      // Handle all non-text types with a single default case\n      default: {\n        const _exhaustiveCheck = type;\n        throw new UnsupportedFunctionalityError({\n          functionality: `${_exhaustiveCheck} mode`\n        });\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _b, _c, _d, _e, _f, _g, _h, _i, _j;\n    const args = this.getArgs(options);\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        OpenRouterNonStreamChatCompletionResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const _a = args, { messages: rawPrompt } = _a, rawSettings = __objRest(_a, [\"messages\"]);\n    const choice = response.choices[0];\n    if (!choice) {\n      throw new Error(\"No choice in response\");\n    }\n    const usageInfo = response.usage ? {\n      promptTokens: (_b = response.usage.prompt_tokens) != null ? _b : 0,\n      completionTokens: (_c = response.usage.completion_tokens) != null ? _c : 0\n    } : {\n      promptTokens: 0,\n      completionTokens: 0\n    };\n    const providerMetadata = {};\n    if (response.usage && ((_d = this.settings.usage) == null ? void 0 : _d.include)) {\n      providerMetadata.openrouter = {\n        usage: {\n          promptTokens: response.usage.prompt_tokens,\n          promptTokensDetails: response.usage.prompt_tokens_details ? {\n            cachedTokens: (_e = response.usage.prompt_tokens_details.cached_tokens) != null ? _e : 0\n          } : void 0,\n          completionTokens: response.usage.completion_tokens,\n          completionTokensDetails: response.usage.completion_tokens_details ? {\n            reasoningTokens: (_f = response.usage.completion_tokens_details.reasoning_tokens) != null ? _f : 0\n          } : void 0,\n          cost: response.usage.cost,\n          totalTokens: (_g = response.usage.total_tokens) != null ? _g : 0\n        }\n      };\n    }\n    const hasProviderMetadata = Object.keys(providerMetadata).length > 0;\n    const reasoningDetails = (_h = choice.message.reasoning_details) != null ? _h : [];\n    const reasoning = reasoningDetails.length > 0 ? reasoningDetails.map((detail) => {\n      var _a2;\n      switch (detail.type) {\n        case \"reasoning.text\" /* Text */: {\n          if (detail.text) {\n            return {\n              type: \"text\",\n              text: detail.text,\n              signature: (_a2 = detail.signature) != null ? _a2 : void 0\n            };\n          }\n          break;\n        }\n        case \"reasoning.summary\" /* Summary */: {\n          if (detail.summary) {\n            return {\n              type: \"text\",\n              text: detail.summary\n            };\n          }\n          break;\n        }\n        case \"reasoning.encrypted\" /* Encrypted */: {\n          if (detail.data) {\n            return {\n              type: \"redacted\",\n              data: detail.data\n            };\n          }\n          break;\n        }\n        default: {\n          detail;\n        }\n      }\n      return null;\n    }).filter((p) => p !== null) : choice.message.reasoning ? [\n      {\n        type: \"text\",\n        text: choice.message.reasoning\n      }\n    ] : [];\n    return __spreadValues({\n      response: {\n        id: response.id,\n        modelId: response.model\n      },\n      text: (_i = choice.message.content) != null ? _i : void 0,\n      reasoning,\n      toolCalls: (_j = choice.message.tool_calls) == null ? void 0 : _j.map((toolCall) => {\n        var _a2;\n        return {\n          toolCallType: \"function\",\n          toolCallId: (_a2 = toolCall.id) != null ? _a2 : generateId(),\n          toolName: toolCall.function.name,\n          args: toolCall.function.arguments\n        };\n      }),\n      finishReason: mapOpenRouterFinishReason(choice.finish_reason),\n      usage: usageInfo,\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings: [],\n      logprobs: mapOpenRouterChatLogProbsOutput(choice.logprobs)\n    }, hasProviderMetadata ? { providerMetadata } : {});\n  }\n  async doStream(options) {\n    var _a, _c;\n    const args = this.getArgs(options);\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: __spreadProps(__spreadValues({}, args), {\n        stream: true,\n        // only include stream_options when in strict compatibility mode:\n        stream_options: this.config.compatibility === \"strict\" ? __spreadValues({\n          include_usage: true\n        }, ((_a = this.settings.usage) == null ? void 0 : _a.include) ? { include_usage: true } : {}) : void 0\n      }),\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        OpenRouterStreamChatCompletionChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const _b = args, { messages: rawPrompt } = _b, rawSettings = __objRest(_b, [\"messages\"]);\n    const toolCalls = [];\n    let finishReason = \"other\";\n    let usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN\n    };\n    let logprobs;\n    const openrouterUsage = {};\n    const shouldIncludeUsageAccounting = !!((_c = this.settings.usage) == null ? void 0 : _c.include);\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a2, _b2, _c2, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (value.id) {\n              controller.enqueue({\n                type: \"response-metadata\",\n                id: value.id\n              });\n            }\n            if (value.model) {\n              controller.enqueue({\n                type: \"response-metadata\",\n                modelId: value.model\n              });\n            }\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens\n              };\n              openrouterUsage.promptTokens = value.usage.prompt_tokens;\n              if (value.usage.prompt_tokens_details) {\n                openrouterUsage.promptTokensDetails = {\n                  cachedTokens: (_a2 = value.usage.prompt_tokens_details.cached_tokens) != null ? _a2 : 0\n                };\n              }\n              openrouterUsage.completionTokens = value.usage.completion_tokens;\n              if (value.usage.completion_tokens_details) {\n                openrouterUsage.completionTokensDetails = {\n                  reasoningTokens: (_b2 = value.usage.completion_tokens_details.reasoning_tokens) != null ? _b2 : 0\n                };\n              }\n              openrouterUsage.cost = value.usage.cost;\n              openrouterUsage.totalTokens = value.usage.total_tokens;\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenRouterFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.delta) == null) {\n              return;\n            }\n            const delta = choice.delta;\n            if (delta.content != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: delta.content\n              });\n            }\n            if (delta.reasoning != null) {\n              controller.enqueue({\n                type: \"reasoning\",\n                textDelta: delta.reasoning\n              });\n            }\n            if (delta.reasoning_details && delta.reasoning_details.length > 0) {\n              for (const detail of delta.reasoning_details) {\n                switch (detail.type) {\n                  case \"reasoning.text\" /* Text */: {\n                    if (detail.text) {\n                      controller.enqueue({\n                        type: \"reasoning\",\n                        textDelta: detail.text\n                      });\n                    }\n                    if (detail.signature) {\n                      controller.enqueue({\n                        type: \"reasoning-signature\",\n                        signature: detail.signature\n                      });\n                    }\n                    break;\n                  }\n                  case \"reasoning.encrypted\" /* Encrypted */: {\n                    if (detail.data) {\n                      controller.enqueue({\n                        type: \"redacted-reasoning\",\n                        data: detail.data\n                      });\n                    }\n                    break;\n                  }\n                  case \"reasoning.summary\" /* Summary */: {\n                    if (detail.summary) {\n                      controller.enqueue({\n                        type: \"reasoning\",\n                        textDelta: detail.summary\n                      });\n                    }\n                    break;\n                  }\n                  default: {\n                    detail;\n                    break;\n                  }\n                }\n              }\n            }\n            const mappedLogprobs = mapOpenRouterChatLogProbsOutput(\n              choice == null ? void 0 : choice.logprobs\n            );\n            if (mappedLogprobs == null ? void 0 : mappedLogprobs.length) {\n              if (logprobs === void 0) {\n                logprobs = [];\n              }\n              logprobs.push(...mappedLogprobs);\n            }\n            if (delta.tool_calls != null) {\n              for (const toolCallDelta of delta.tool_calls) {\n                const index = toolCallDelta.index;\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== \"function\") {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`\n                    });\n                  }\n                  if (toolCallDelta.id == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`\n                    });\n                  }\n                  if (((_c2 = toolCallDelta.function) == null ? void 0 : _c2.name) == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`\n                    });\n                  }\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: \"function\",\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: (_d = toolCallDelta.function.arguments) != null ? _d : \"\"\n                    },\n                    sent: false\n                  };\n                  const toolCall2 = toolCalls[index];\n                  if (toolCall2 == null) {\n                    throw new Error(\"Tool call is missing\");\n                  }\n                  if (((_e = toolCall2.function) == null ? void 0 : _e.name) != null && ((_f = toolCall2.function) == null ? void 0 : _f.arguments) != null && isParsableJson(toolCall2.function.arguments)) {\n                    controller.enqueue({\n                      type: \"tool-call-delta\",\n                      toolCallType: \"function\",\n                      toolCallId: toolCall2.id,\n                      toolName: toolCall2.function.name,\n                      argsTextDelta: toolCall2.function.arguments\n                    });\n                    controller.enqueue({\n                      type: \"tool-call\",\n                      toolCallType: \"function\",\n                      toolCallId: (_g = toolCall2.id) != null ? _g : generateId(),\n                      toolName: toolCall2.function.name,\n                      args: toolCall2.function.arguments\n                    });\n                    toolCall2.sent = true;\n                  }\n                  continue;\n                }\n                const toolCall = toolCalls[index];\n                if (toolCall == null) {\n                  throw new Error(\"Tool call is missing\");\n                }\n                if (((_h = toolCallDelta.function) == null ? void 0 : _h.arguments) != null) {\n                  toolCall.function.arguments += (_j = (_i = toolCallDelta.function) == null ? void 0 : _i.arguments) != null ? _j : \"\";\n                }\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: (_k = toolCallDelta.function.arguments) != null ? _k : \"\"\n                });\n                if (((_l = toolCall.function) == null ? void 0 : _l.name) != null && ((_m = toolCall.function) == null ? void 0 : _m.arguments) != null && isParsableJson(toolCall.function.arguments)) {\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: (_n = toolCall.id) != null ? _n : generateId(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments\n                  });\n                  toolCall.sent = true;\n                }\n              }\n            }\n          },\n          flush(controller) {\n            var _a2;\n            if (finishReason === \"tool-calls\") {\n              for (const toolCall of toolCalls) {\n                if (!toolCall.sent) {\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: (_a2 = toolCall.id) != null ? _a2 : generateId(),\n                    toolName: toolCall.function.name,\n                    // Coerce invalid arguments to an empty JSON object\n                    args: isParsableJson(toolCall.function.arguments) ? toolCall.function.arguments : \"{}\"\n                  });\n                  toolCall.sent = true;\n                }\n              }\n            }\n            const providerMetadata = {};\n            if (shouldIncludeUsageAccounting && (openrouterUsage.totalTokens !== void 0 || openrouterUsage.cost !== void 0 || openrouterUsage.promptTokensDetails !== void 0 || openrouterUsage.completionTokensDetails !== void 0)) {\n              providerMetadata.openrouter = {\n                usage: openrouterUsage\n              };\n            }\n            const hasProviderMetadata = Object.keys(providerMetadata).length > 0 && shouldIncludeUsageAccounting;\n            controller.enqueue(__spreadValues({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage\n            }, hasProviderMetadata ? { providerMetadata } : {}));\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings: []\n    };\n  }\n};\nvar OpenRouterChatCompletionBaseResponseSchema = z3.object({\n  id: z3.string().optional(),\n  model: z3.string().optional(),\n  usage: z3.object({\n    prompt_tokens: z3.number(),\n    prompt_tokens_details: z3.object({\n      cached_tokens: z3.number()\n    }).nullish(),\n    completion_tokens: z3.number(),\n    completion_tokens_details: z3.object({\n      reasoning_tokens: z3.number()\n    }).nullish(),\n    total_tokens: z3.number(),\n    cost: z3.number().optional()\n  }).nullish()\n});\nvar OpenRouterNonStreamChatCompletionResponseSchema = OpenRouterChatCompletionBaseResponseSchema.extend({\n  choices: z3.array(\n    z3.object({\n      message: z3.object({\n        role: z3.literal(\"assistant\"),\n        content: z3.string().nullable().optional(),\n        reasoning: z3.string().nullable().optional(),\n        reasoning_details: ReasoningDetailArraySchema.nullish(),\n        tool_calls: z3.array(\n          z3.object({\n            id: z3.string().optional().nullable(),\n            type: z3.literal(\"function\"),\n            function: z3.object({\n              name: z3.string(),\n              arguments: z3.string()\n            })\n          })\n        ).optional()\n      }),\n      index: z3.number(),\n      logprobs: z3.object({\n        content: z3.array(\n          z3.object({\n            token: z3.string(),\n            logprob: z3.number(),\n            top_logprobs: z3.array(\n              z3.object({\n                token: z3.string(),\n                logprob: z3.number()\n              })\n            )\n          })\n        ).nullable()\n      }).nullable().optional(),\n      finish_reason: z3.string().optional().nullable()\n    })\n  )\n});\nvar OpenRouterStreamChatCompletionChunkSchema = z3.union([\n  OpenRouterChatCompletionBaseResponseSchema.extend({\n    choices: z3.array(\n      z3.object({\n        delta: z3.object({\n          role: z3.enum([\"assistant\"]).optional(),\n          content: z3.string().nullish(),\n          reasoning: z3.string().nullish().optional(),\n          reasoning_details: ReasoningDetailArraySchema.nullish(),\n          tool_calls: z3.array(\n            z3.object({\n              index: z3.number(),\n              id: z3.string().nullish(),\n              type: z3.literal(\"function\").optional(),\n              function: z3.object({\n                name: z3.string().nullish(),\n                arguments: z3.string().nullish()\n              })\n            })\n          ).nullish()\n        }).nullish(),\n        logprobs: z3.object({\n          content: z3.array(\n            z3.object({\n              token: z3.string(),\n              logprob: z3.number(),\n              top_logprobs: z3.array(\n                z3.object({\n                  token: z3.string(),\n                  logprob: z3.number()\n                })\n              )\n            })\n          ).nullable()\n        }).nullish(),\n        finish_reason: z3.string().nullable().optional(),\n        index: z3.number()\n      })\n    )\n  }),\n  OpenRouterErrorResponseSchema\n]);\nfunction prepareToolsAndToolChoice(mode) {\n  var _a;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  if (tools == null) {\n    return { tools: void 0, tool_choice: void 0 };\n  }\n  const mappedTools = tools.map((tool) => {\n    if (isFunctionTool(tool)) {\n      return {\n        type: \"function\",\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters\n        }\n      };\n    }\n    return {\n      type: \"function\",\n      function: {\n        name: tool.name\n      }\n    };\n  });\n  const toolChoice = mode.toolChoice;\n  if (toolChoice == null) {\n    return { tools: mappedTools, tool_choice: void 0 };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n    case \"required\":\n      return { tools: mappedTools, tool_choice: type };\n    case \"tool\":\n      return {\n        tools: mappedTools,\n        tool_choice: {\n          type: \"function\",\n          function: {\n            name: toolChoice.toolName\n          }\n        }\n      };\n    default: {\n      const _exhaustiveCheck = type;\n      throw new Error(`Unsupported tool choice type: ${_exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/openrouter-completion-language-model.ts\nimport { UnsupportedFunctionalityError as UnsupportedFunctionalityError3 } from \"@ai-sdk/provider\";\nimport {\n  combineHeaders as combineHeaders2,\n  createEventSourceResponseHandler as createEventSourceResponseHandler2,\n  createJsonResponseHandler as createJsonResponseHandler2,\n  postJsonToApi as postJsonToApi2\n} from \"@ai-sdk/provider-utils\";\nimport { z as z4 } from \"zod\";\n\n// src/convert-to-openrouter-completion-prompt.ts\nimport {\n  InvalidPromptError,\n  UnsupportedFunctionalityError as UnsupportedFunctionalityError2\n} from \"@ai-sdk/provider\";\nfunction convertToOpenRouterCompletionPrompt({\n  prompt,\n  inputFormat,\n  user = \"user\",\n  assistant = \"assistant\"\n}) {\n  if (inputFormat === \"prompt\" && prompt.length === 1 && prompt[0] && prompt[0].role === \"user\" && prompt[0].content.length === 1 && prompt[0].content[0] && prompt[0].content[0].type === \"text\") {\n    return { prompt: prompt[0].content[0].text };\n  }\n  let text = \"\";\n  if (prompt[0] && prompt[0].role === \"system\") {\n    text += `${prompt[0].content}\n\n`;\n    prompt = prompt.slice(1);\n  }\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        throw new InvalidPromptError({\n          message: \"Unexpected system message in prompt: ${content}\",\n          prompt\n        });\n      }\n      case \"user\": {\n        const userMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"image\": {\n              throw new UnsupportedFunctionalityError2({\n                functionality: \"images\"\n              });\n            }\n            case \"file\": {\n              throw new UnsupportedFunctionalityError2({\n                functionality: \"file attachments\"\n              });\n            }\n            default: {\n              const _exhaustiveCheck = part;\n              throw new Error(\n                `Unsupported content type: ${_exhaustiveCheck}`\n              );\n            }\n          }\n        }).join(\"\");\n        text += `${user}:\n${userMessage}\n\n`;\n        break;\n      }\n      case \"assistant\": {\n        const assistantMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"tool-call\": {\n              throw new UnsupportedFunctionalityError2({\n                functionality: \"tool-call messages\"\n              });\n            }\n            case \"reasoning\": {\n              throw new UnsupportedFunctionalityError2({\n                functionality: \"reasoning messages\"\n              });\n            }\n            case \"redacted-reasoning\": {\n              throw new UnsupportedFunctionalityError2({\n                functionality: \"redacted reasoning messages\"\n              });\n            }\n            case \"file\": {\n              throw new UnsupportedFunctionalityError2({\n                functionality: \"file attachments\"\n              });\n            }\n            default: {\n              const _exhaustiveCheck = part;\n              throw new Error(\n                `Unsupported content type: ${_exhaustiveCheck}`\n              );\n            }\n          }\n        }).join(\"\");\n        text += `${assistant}:\n${assistantMessage}\n\n`;\n        break;\n      }\n      case \"tool\": {\n        throw new UnsupportedFunctionalityError2({\n          functionality: \"tool messages\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  text += `${assistant}:\n`;\n  return {\n    prompt: text\n  };\n}\n\n// src/map-openrouter-completion-logprobs.ts\nfunction mapOpenRouterCompletionLogProbs(logprobs) {\n  return logprobs == null ? void 0 : logprobs.tokens.map((token, index) => {\n    var _a, _b;\n    return {\n      token,\n      logprob: (_a = logprobs.token_logprobs[index]) != null ? _a : 0,\n      topLogprobs: logprobs.top_logprobs ? Object.entries((_b = logprobs.top_logprobs[index]) != null ? _b : {}).map(\n        ([token2, logprob]) => ({\n          token: token2,\n          logprob\n        })\n      ) : []\n    };\n  });\n}\n\n// src/openrouter-completion-language-model.ts\nvar OpenRouterCompletionLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = void 0;\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    mode,\n    inputFormat,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    frequencyPenalty,\n    presencePenalty,\n    seed,\n    responseFormat,\n    topK,\n    stopSequences,\n    providerMetadata\n  }) {\n    var _a, _b;\n    const type = mode.type;\n    const extraCallingBody = (_a = providerMetadata == null ? void 0 : providerMetadata.openrouter) != null ? _a : {};\n    const { prompt: completionPrompt } = convertToOpenRouterCompletionPrompt({\n      prompt,\n      inputFormat\n    });\n    const baseArgs = __spreadValues(__spreadValues(__spreadValues({\n      // model id:\n      model: this.modelId,\n      models: this.settings.models,\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs: typeof this.settings.logprobs === \"number\" ? this.settings.logprobs : typeof this.settings.logprobs === \"boolean\" ? this.settings.logprobs ? 0 : void 0 : void 0,\n      suffix: this.settings.suffix,\n      user: this.settings.user,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n      stop: stopSequences,\n      response_format: responseFormat,\n      top_k: topK,\n      // prompt:\n      prompt: completionPrompt,\n      // OpenRouter specific settings:\n      include_reasoning: this.settings.includeReasoning,\n      reasoning: this.settings.reasoning\n    }, this.config.extraBody), this.settings.extraBody), extraCallingBody);\n    switch (type) {\n      case \"regular\": {\n        if ((_b = mode.tools) == null ? void 0 : _b.length) {\n          throw new UnsupportedFunctionalityError3({\n            functionality: \"tools\"\n          });\n        }\n        if (mode.toolChoice) {\n          throw new UnsupportedFunctionalityError3({\n            functionality: \"toolChoice\"\n          });\n        }\n        return baseArgs;\n      }\n      case \"object-json\": {\n        throw new UnsupportedFunctionalityError3({\n          functionality: \"object-json mode\"\n        });\n      }\n      case \"object-tool\": {\n        throw new UnsupportedFunctionalityError3({\n          functionality: \"object-tool mode\"\n        });\n      }\n      // Handle all non-text types with a single default case\n      default: {\n        const _exhaustiveCheck = type;\n        throw new UnsupportedFunctionalityError3({\n          functionality: `${_exhaustiveCheck} mode`\n        });\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _b, _c, _d, _e, _f;\n    const args = this.getArgs(options);\n    const { responseHeaders, value: response } = await postJsonToApi2({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders2(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler2(\n        OpenRouterCompletionChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const _a = args, { prompt: rawPrompt } = _a, rawSettings = __objRest(_a, [\"prompt\"]);\n    if (\"error\" in response) {\n      throw new Error(`${response.error.message}`);\n    }\n    const choice = response.choices[0];\n    if (!choice) {\n      throw new Error(\"No choice in OpenRouter completion response\");\n    }\n    return {\n      response: {\n        id: response.id,\n        modelId: response.model\n      },\n      text: (_b = choice.text) != null ? _b : \"\",\n      reasoning: choice.reasoning || void 0,\n      usage: {\n        promptTokens: (_d = (_c = response.usage) == null ? void 0 : _c.prompt_tokens) != null ? _d : 0,\n        completionTokens: (_f = (_e = response.usage) == null ? void 0 : _e.completion_tokens) != null ? _f : 0\n      },\n      finishReason: mapOpenRouterFinishReason(choice.finish_reason),\n      logprobs: mapOpenRouterCompletionLogProbs(choice.logprobs),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings: []\n    };\n  }\n  async doStream(options) {\n    const args = this.getArgs(options);\n    const { responseHeaders, value: response } = await postJsonToApi2({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders2(this.config.headers(), options.headers),\n      body: __spreadProps(__spreadValues({}, this.getArgs(options)), {\n        stream: true,\n        // only include stream_options when in strict compatibility mode:\n        stream_options: this.config.compatibility === \"strict\" ? { include_usage: true } : void 0\n      }),\n      failedResponseHandler: openrouterFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler2(\n        OpenRouterCompletionChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const _a = args, { prompt: rawPrompt } = _a, rawSettings = __objRest(_a, [\"prompt\"]);\n    let finishReason = \"other\";\n    let usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN\n    };\n    let logprobs;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens\n              };\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenRouterFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.text) != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: choice.text\n              });\n            }\n            const mappedLogprobs = mapOpenRouterCompletionLogProbs(\n              choice == null ? void 0 : choice.logprobs\n            );\n            if (mappedLogprobs == null ? void 0 : mappedLogprobs.length) {\n              if (logprobs === void 0) {\n                logprobs = [];\n              }\n              logprobs.push(...mappedLogprobs);\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings: []\n    };\n  }\n};\nvar OpenRouterCompletionChunkSchema = z4.union([\n  z4.object({\n    id: z4.string().optional(),\n    model: z4.string().optional(),\n    choices: z4.array(\n      z4.object({\n        text: z4.string(),\n        reasoning: z4.string().nullish().optional(),\n        reasoning_details: ReasoningDetailArraySchema.nullish(),\n        finish_reason: z4.string().nullish(),\n        index: z4.number(),\n        logprobs: z4.object({\n          tokens: z4.array(z4.string()),\n          token_logprobs: z4.array(z4.number()),\n          top_logprobs: z4.array(z4.record(z4.string(), z4.number())).nullable()\n        }).nullable().optional()\n      })\n    ),\n    usage: z4.object({\n      prompt_tokens: z4.number(),\n      completion_tokens: z4.number()\n    }).optional().nullable()\n  }),\n  OpenRouterErrorResponseSchema\n]);\n\n// src/openrouter-facade.ts\nvar OpenRouter = class {\n  /**\n   * Creates a new OpenRouter provider instance.\n   */\n  constructor(options = {}) {\n    var _a, _b;\n    this.baseURL = (_b = withoutTrailingSlash((_a = options.baseURL) != null ? _a : options.baseUrl)) != null ? _b : \"https://openrouter.ai/api/v1\";\n    this.apiKey = options.apiKey;\n    this.headers = options.headers;\n  }\n  get baseConfig() {\n    return {\n      baseURL: this.baseURL,\n      headers: () => __spreadValues({\n        Authorization: `Bearer ${loadApiKey({\n          apiKey: this.apiKey,\n          environmentVariableName: \"OPENROUTER_API_KEY\",\n          description: \"OpenRouter\"\n        })}`\n      }, this.headers)\n    };\n  }\n  chat(modelId, settings = {}) {\n    return new OpenRouterChatLanguageModel(modelId, settings, __spreadProps(__spreadValues({\n      provider: \"openrouter.chat\"\n    }, this.baseConfig), {\n      compatibility: \"strict\",\n      url: ({ path }) => `${this.baseURL}${path}`\n    }));\n  }\n  completion(modelId, settings = {}) {\n    return new OpenRouterCompletionLanguageModel(modelId, settings, __spreadProps(__spreadValues({\n      provider: \"openrouter.completion\"\n    }, this.baseConfig), {\n      compatibility: \"strict\",\n      url: ({ path }) => `${this.baseURL}${path}`\n    }));\n  }\n};\n\n// src/openrouter-provider.ts\nimport { loadApiKey as loadApiKey2, withoutTrailingSlash as withoutTrailingSlash2 } from \"@ai-sdk/provider-utils\";\nfunction createOpenRouter(options = {}) {\n  var _a, _b, _c;\n  const baseURL = (_b = withoutTrailingSlash2((_a = options.baseURL) != null ? _a : options.baseUrl)) != null ? _b : \"https://openrouter.ai/api/v1\";\n  const compatibility = (_c = options.compatibility) != null ? _c : \"compatible\";\n  const getHeaders = () => __spreadValues({\n    Authorization: `Bearer ${loadApiKey2({\n      apiKey: options.apiKey,\n      environmentVariableName: \"OPENROUTER_API_KEY\",\n      description: \"OpenRouter\"\n    })}`\n  }, options.headers);\n  const createChatModel = (modelId, settings = {}) => new OpenRouterChatLanguageModel(modelId, settings, {\n    provider: \"openrouter.chat\",\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    compatibility,\n    fetch: options.fetch,\n    extraBody: options.extraBody\n  });\n  const createCompletionModel = (modelId, settings = {}) => new OpenRouterCompletionLanguageModel(modelId, settings, {\n    provider: \"openrouter.completion\",\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    compatibility,\n    fetch: options.fetch,\n    extraBody: options.extraBody\n  });\n  const createLanguageModel = (modelId, settings) => {\n    if (new.target) {\n      throw new Error(\n        \"The OpenRouter model function cannot be called with the new keyword.\"\n      );\n    }\n    if (modelId === \"openai/gpt-3.5-turbo-instruct\") {\n      return createCompletionModel(\n        modelId,\n        settings\n      );\n    }\n    return createChatModel(modelId, settings);\n  };\n  const provider = (modelId, settings) => createLanguageModel(modelId, settings);\n  provider.languageModel = createLanguageModel;\n  provider.chat = createChatModel;\n  provider.completion = createCompletionModel;\n  return provider;\n}\nvar openrouter = createOpenRouter({\n  compatibility: \"strict\"\n  // strict for OpenRouter API\n});\nexport {\n  OpenRouter,\n  createOpenRouter,\n  openrouter\n};\n//# sourceMappingURL=index.mjs.map"], "mappings": ";;;AAAA,IAAI,YAAY,OAAO;AACvB,IAAI,aAAa,OAAO;AACxB,IAAI,oBAAoB,OAAO;AAC/B,IAAI,sBAAsB,OAAO;AACjC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK;CAAE,YAAY;CAAM,cAAc;CAAM,UAAU;CAAM;AAAO,EAAC,GAAG,IAAI,OAAO;AAC1J,IAAI,iBAAiB,CAAC,GAAG,MAAM;AAC7B,MAAK,IAAI,QAAQ,MAAM,IAAI,CAAE,GAC3B,KAAI,aAAa,KAAK,GAAG,KAAK,CAC5B,iBAAgB,GAAG,MAAM,EAAE,MAAM;AACrC,KAAI,qBACF;OAAK,IAAI,QAAQ,oBAAoB,EAAE,CACrC,KAAI,aAAa,KAAK,GAAG,KAAK,CAC5B,iBAAgB,GAAG,MAAM,EAAE,MAAM;CACpC;AACH,QAAO;AACR;AACD,IAAI,gBAAgB,CAAC,GAAG,MAAM,WAAW,GAAG,kBAAkB,EAAE,CAAC;AACjE,IAAI,YAAY,CAAC,QAAQ,YAAY;CACnC,IAAI,SAAS,CAAE;AACf,MAAK,IAAI,QAAQ,OACf,KAAI,aAAa,KAAK,QAAQ,KAAK,IAAI,QAAQ,QAAQ,KAAK,GAAG,EAC7D,QAAO,QAAQ,OAAO;AAC1B,KAAI,UAAU,QAAQ,qBACpB;OAAK,IAAI,QAAQ,oBAAoB,OAAO,CAC1C,KAAI,QAAQ,QAAQ,KAAK,GAAG,KAAK,aAAa,KAAK,QAAQ,KAAK,CAC9D,QAAO,QAAQ,OAAO;CACzB;AACH,QAAO;AACR;AAOD,IAAI,+BAA+B,WAAS;CAC1C,MAAM,YAAU,oBAAkC;CAClD,SAAS,YAAU;AACpB,EAAC;AACF,IAAI,iCAAiC,WAAS;CAC5C,MAAM,YAAU,sBAAsC;CACtD,MAAM,YAAU;AACjB,EAAC;AACF,IAAI,4BAA4B,WAAS;CACvC,MAAM,YAAU,iBAA4B;CAC5C,MAAM,YAAU,CAAC,SAAS;CAC1B,WAAW,YAAU,CAAC,SAAS;AAChC,EAAC;AACF,IAAI,6BAA6B,UAAQ;CACvC;CACA;CACA;AACD,EAAC;AACF,IAAI,oCAAoC,UAAQ,CAC9C,4BACA,aAAW,CAAC,UAAU,MAAM,KAAK,AAClC,EAAC;AACF,IAAI,6BAA6B,UAAQ,kCAAkC,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,GAAG,CAAC;AAmBpH,SAAS,gBAAgB,kBAAkB;CACzC,IAAI,IAAI,IAAI;CACZ,MAAM,YAAY,oBAAoB,YAAY,IAAI,iBAAiB;CACvE,MAAM,cAAc,oBAAoB,YAAY,IAAI,iBAAiB;AACzE,SAAQ,MAAM,MAAM,KAAK,eAAe,YAAY,IAAI,YAAY,iBAAiB,OAAO,KAAK,eAAe,YAAY,IAAI,YAAY,kBAAkB,OAAO,KAAK,aAAa,YAAY,IAAI,UAAU,iBAAiB,OAAO,KAAK,aAAa,YAAY,IAAI,UAAU;AACtR;AACD,SAAS,gCAAgC,QAAQ;CAC/C,IAAI,IAAI,IAAI;CACZ,MAAM,WAAW,CAAE;AACnB,MAAK,MAAM,EAAE,MAAM,SAAS,kBAAkB,IAAI,OAChD,SAAQ,MAAR;EACE,KAAK,UAAU;AACb,YAAS,KAAK;IACZ,MAAM;IACN;IACA,eAAe,gBAAgB,iBAAiB;GACjD,EAAC;AACF;EACD;EACD,KAAK,QAAQ;AACX,OAAI,QAAQ,WAAW,OAAO,KAAK,QAAQ,OAAO,YAAY,IAAI,GAAG,UAAU,QAAQ;AACrF,aAAS,KAAK;KACZ,MAAM;KACN,SAAS,QAAQ,GAAG;KACpB,gBAAgB,KAAK,gBAAgB,iBAAiB,KAAK,OAAO,KAAK,gBAAgB,QAAQ,GAAG,iBAAiB;IACpH,EAAC;AACF;GACD;GACD,MAAM,sBAAsB,gBAAgB,iBAAiB;GAC7D,MAAM,eAAe,QAAQ,IAC3B,CAAC,SAAS;IACR,IAAI,KAAK,KAAK,KAAK;IACnB,MAAM,gBAAgB,MAAM,gBAAgB,KAAK,iBAAiB,KAAK,OAAO,MAAM;AACpF,YAAQ,KAAK,MAAb;KACE,KAAK,OACH,QAAO;MACL,MAAM;MACN,MAAM,KAAK;MAEX,eAAe;KAChB;KACH,KAAK,QACH,QAAO;MACL,MAAM;MACN,WAAW,EACT,KAAK,KAAK,iBAAiB,MAAM,KAAK,MAAM,UAAU,IAAI,QAAQ,MAAM,KAAK,aAAa,OAAO,MAAM,aAAa,UAAU,0BAC5H,KAAK,MACN,GACF;MAED,eAAe;KAChB;KACH,KAAK,OACH,QAAO;MACL,MAAM;MACN,MAAM;OACJ,UAAU,QACP,MAAM,MAAM,KAAK,qBAAqB,YAAY,IAAI,IAAI,eAAe,YAAY,IAAI,GAAG,SAC9F;OACD,WAAW,KAAK,gBAAgB,cAAc,OAAO,KAAK,SAAS,UAAU,0BAA0B,KAAK,KAAK,MAAM,OAAO,KAAK,SAAS,UAAU,KAAK;MAC5J;MACD,eAAe;KAChB;KACH,SAAS;MACP,MAAM,mBAAmB;AACzB,YAAM,IAAI,OACP,iCAAiC;KAErC;IACF;GACF,EACF;AACD,YAAS,KAAK;IACZ,MAAM;IACN,SAAS;GACV,EAAC;AACF;EACD;EACD,KAAK,aAAa;GAChB,IAAI,OAAO;GACX,IAAI,YAAY;GAChB,MAAM,mBAAmB,CAAE;GAC3B,MAAM,YAAY,CAAE;AACpB,QAAK,MAAM,QAAQ,QACjB,SAAQ,KAAK,MAAb;IACE,KAAK,QAAQ;AACX,aAAQ,KAAK;AACb;IACD;IACD,KAAK,aAAa;AAChB,eAAU,KAAK;MACb,IAAI,KAAK;MACT,MAAM;MACN,UAAU;OACR,MAAM,KAAK;OACX,WAAW,KAAK,UAAU,KAAK,KAAK;MACrC;KACF,EAAC;AACF;IACD;IACD,KAAK,aAAa;AAChB,kBAAa,KAAK;AAClB,sBAAiB,KAAK;MACpB,MAAM;MACN,MAAM,KAAK;MACX,WAAW,KAAK;KACjB,EAAC;AACF;IACD;IACD,KAAK,sBAAsB;AACzB,sBAAiB,KAAK;MACpB,MAAM;MACN,MAAM,KAAK;KACZ,EAAC;AACF;IACD;IACD,KAAK,OACH;IACF,SAAS;KACP,MAAM,mBAAmB;AACzB,WAAM,IAAI,OAAO,oBAAoB;IACtC;GACF;AAEH,YAAS,KAAK;IACZ,MAAM;IACN,SAAS;IACT,YAAY,UAAU,SAAS,IAAI,iBAAiB;IACpD,WAAW,kBAAkB;IAC7B,mBAAmB,iBAAiB,SAAS,IAAI,wBAAwB;IACzE,eAAe,gBAAgB,iBAAiB;GACjD,EAAC;AACF;EACD;EACD,KAAK,QAAQ;AACX,QAAK,MAAM,gBAAgB,QACzB,UAAS,KAAK;IACZ,MAAM;IACN,cAAc,aAAa;IAC3B,SAAS,KAAK,UAAU,aAAa,OAAO;IAC5C,gBAAgB,KAAK,gBAAgB,iBAAiB,KAAK,OAAO,KAAK,gBAAgB,aAAa,iBAAiB;GACtH,EAAC;AAEJ;EACD;EACD,SAAS;GACP,MAAM,mBAAmB;AACzB,SAAM,IAAI,OAAO,oBAAoB;EACtC;CACF;AAEH,QAAO;AACR;AAGD,SAAS,gCAAgC,UAAU;CACjD,IAAI,IAAI;AACR,SAAQ,MAAM,KAAK,YAAY,YAAY,IAAI,SAAS,YAAY,YAAY,IAAI,GAAG,IAAI,CAAC,EAAE,OAAO,SAAS,cAAc,MAAM;EAChI;EACA;EACA,aAAa,eAAe,aAAa,IAAI,CAAC,EAAE,OAAO,QAAQ,SAAS,UAAU,MAAM;GACtF,OAAO;GACP,SAAS;EACV,GAAE,GAAG,CAAE;CACT,GAAE,KAAK,OAAO,UAAU;AAC1B;AAGD,SAAS,0BAA0B,cAAc;AAC/C,SAAQ,cAAR;EACE,KAAK,OACH,QAAO;EACT,KAAK,SACH,QAAO;EACT,KAAK,iBACH,QAAO;EACT,KAAK;EACL,KAAK,aACH,QAAO;EACT,QACE,QAAO;CACV;AACF;AAKD,IAAI,gCAAgC,WAAU,EAC5C,OAAO,WAAU;CACf,MAAM,UAAS,CAAC,YAAW,EAAE,YAAW,AAAC,EAAC,CAAC,UAAU;CACrD,SAAS,YAAW;CACpB,MAAM,YAAW,CAAC,UAAU;CAC5B,OAAO,SAAQ,CAAC,UAAU;AAC3B,EAAC,CACH,EAAC;AACF,IAAI,kCAAkC,+BAA+B;CACnE,aAAa;CACb,gBAAgB,CAAC,SAAS,KAAK,MAAM;AACtC,EAAC;AAGF,SAAS,eAAe,MAAM;AAC5B,QAAO,gBAAgB;AACxB;AACD,IAAI,8BAA8B,MAAM;CACtC,YAAY,SAAS,UAAU,QAAQ;AACrC,OAAK,uBAAuB;AAC5B,OAAK,8BAA8B;AACnC,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,SAAS;CACf;CACD,IAAI,WAAW;AACb,SAAO,KAAK,OAAO;CACpB;CACD,QAAQ,EACN,MACA,QACA,WACA,aACA,MACA,kBACA,iBACA,MACA,eACA,gBACA,MACA,kBACD,EAAE;EACD,IAAI;EACJ,MAAM,OAAO,KAAK;EAClB,MAAM,oBAAoB,KAAK,oBAAoB,YAAY,IAAI,iBAAiB,eAAe,OAAO,KAAK,CAAE;EACjH,MAAM,WAAW,eAAe,eAAe,eAAe;GAE5D,OAAO,KAAK;GACZ,QAAQ,KAAK,SAAS;GAEtB,YAAY,KAAK,SAAS;GAC1B,UAAU,KAAK,SAAS,aAAa,eAAe,KAAK,SAAS,aAAa,WAAW,YAAY;GACtG,qBAAqB,KAAK,SAAS,aAAa,WAAW,KAAK,SAAS,kBAAkB,KAAK,SAAS,aAAa,YAAY,KAAK,SAAS,WAAW,SAAS,SAAS;GAC7K,MAAM,KAAK,SAAS;GACpB,qBAAqB,KAAK,SAAS;GAEnC,YAAY;GACZ;GACA,OAAO;GACP,mBAAmB;GACnB,kBAAkB;GAClB;GACA,MAAM;GACN,iBAAiB;GACjB,OAAO;GAEP,UAAU,gCAAgC,OAAO;GAEjD,mBAAmB,KAAK,SAAS;GACjC,WAAW,KAAK,SAAS;GACzB,OAAO,KAAK,SAAS;EACtB,GAAE,KAAK,OAAO,UAAU,EAAE,KAAK,SAAS,UAAU,EAAE,iBAAiB;AACtE,UAAQ,MAAR;GACE,KAAK,UACH,QAAO,eAAe,eAAe,CAAE,GAAE,SAAS,EAAE,0BAA0B,KAAK,CAAC;GAEtF,KAAK,cACH,QAAO,cAAc,eAAe,CAAE,GAAE,SAAS,EAAE,EACjD,iBAAiB,EAAE,MAAM,cAAe,EACzC,EAAC;GAEJ,KAAK,cACH,QAAO,cAAc,eAAe,CAAE,GAAE,SAAS,EAAE;IACjD,aAAa;KAAE,MAAM;KAAY,UAAU,EAAE,MAAM,KAAK,KAAK,KAAM;IAAE;IACrE,OAAO,CACL;KACE,MAAM;KACN,UAAU;MACR,MAAM,KAAK,KAAK;MAChB,aAAa,KAAK,KAAK;MACvB,YAAY,KAAK,KAAK;KACvB;IACF,CACF;GACF,EAAC;GAGJ,SAAS;IACP,MAAM,mBAAmB;AACzB,UAAM,IAAI,8BAA8B,EACtC,kBAAkB,iBAAiB,OACpC;GACF;EACF;CACF;CACD,MAAM,WAAW,SAAS;EACxB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;EACpC,MAAM,OAAO,KAAK,QAAQ,QAAQ;EAClC,MAAM,EAAE,iBAAiB,OAAO,UAAU,GAAG,MAAM,cAAc;GAC/D,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAe,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAC/D,MAAM;GACN,uBAAuB;GACvB,2BAA2B,0BACzB,gDACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,KAAK,MAAM,EAAE,UAAU,WAAW,GAAG,IAAI,cAAc,UAAU,IAAI,CAAC,UAAW,EAAC;EACxF,MAAM,SAAS,SAAS,QAAQ;AAChC,OAAK,OACH,OAAM,IAAI,MAAM;EAElB,MAAM,YAAY,SAAS,QAAQ;GACjC,eAAe,KAAK,SAAS,MAAM,kBAAkB,OAAO,KAAK;GACjE,mBAAmB,KAAK,SAAS,MAAM,sBAAsB,OAAO,KAAK;EAC1E,IAAG;GACF,cAAc;GACd,kBAAkB;EACnB;EACD,MAAM,mBAAmB,CAAE;AAC3B,MAAI,SAAS,WAAW,KAAK,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG,SACtE,kBAAiB,aAAa,EAC5B,OAAO;GACL,cAAc,SAAS,MAAM;GAC7B,qBAAqB,SAAS,MAAM,wBAAwB,EAC1D,eAAe,KAAK,SAAS,MAAM,sBAAsB,kBAAkB,OAAO,KAAK,EACxF,SAAQ;GACT,kBAAkB,SAAS,MAAM;GACjC,yBAAyB,SAAS,MAAM,4BAA4B,EAClE,kBAAkB,KAAK,SAAS,MAAM,0BAA0B,qBAAqB,OAAO,KAAK,EAClG,SAAQ;GACT,MAAM,SAAS,MAAM;GACrB,cAAc,KAAK,SAAS,MAAM,iBAAiB,OAAO,KAAK;EAChE,EACF;EAEH,MAAM,sBAAsB,OAAO,KAAK,iBAAiB,CAAC,SAAS;EACnE,MAAM,oBAAoB,KAAK,OAAO,QAAQ,sBAAsB,OAAO,KAAK,CAAE;EAClF,MAAM,YAAY,iBAAiB,SAAS,IAAI,iBAAiB,IAAI,CAAC,WAAW;GAC/E,IAAI;AACJ,WAAQ,OAAO,MAAf;IACE,KAAK,kBAA6B;AAChC,SAAI,OAAO,KACT,QAAO;MACL,MAAM;MACN,MAAM,OAAO;MACb,YAAY,MAAM,OAAO,cAAc,OAAO,WAAW;KAC1D;AAEH;IACD;IACD,KAAK,qBAAmC;AACtC,SAAI,OAAO,QACT,QAAO;MACL,MAAM;MACN,MAAM,OAAO;KACd;AAEH;IACD;IACD,KAAK,uBAAuC;AAC1C,SAAI,OAAO,KACT,QAAO;MACL,MAAM;MACN,MAAM,OAAO;KACd;AAEH;IACD;IACD,SAAS,CAER;GACF;AACD,UAAO;EACR,EAAC,CAAC,OAAO,CAAC,MAAM,MAAM,KAAK,GAAG,OAAO,QAAQ,YAAY,CACxD;GACE,MAAM;GACN,MAAM,OAAO,QAAQ;EACtB,CACF,IAAG,CAAE;AACN,SAAO,eAAe;GACpB,UAAU;IACR,IAAI,SAAS;IACb,SAAS,SAAS;GACnB;GACD,OAAO,KAAK,OAAO,QAAQ,YAAY,OAAO,UAAU;GACxD;GACA,YAAY,KAAK,OAAO,QAAQ,eAAe,YAAY,IAAI,GAAG,IAAI,CAAC,aAAa;IAClF,IAAI;AACJ,WAAO;KACL,cAAc;KACd,aAAa,MAAM,SAAS,OAAO,OAAO,MAAM,YAAY;KAC5D,UAAU,SAAS,SAAS;KAC5B,MAAM,SAAS,SAAS;IACzB;GACF,EAAC;GACF,cAAc,0BAA0B,OAAO,cAAc;GAC7D,OAAO;GACP,SAAS;IAAE;IAAW;GAAa;GACnC,aAAa,EAAE,SAAS,gBAAiB;GACzC,UAAU,CAAE;GACZ,UAAU,gCAAgC,OAAO,SAAS;EAC3D,GAAE,sBAAsB,EAAE,iBAAkB,IAAG,CAAE,EAAC;CACpD;CACD,MAAM,SAAS,SAAS;EACtB,IAAI,IAAI;EACR,MAAM,OAAO,KAAK,QAAQ,QAAQ;EAClC,MAAM,EAAE,iBAAiB,OAAO,UAAU,GAAG,MAAM,cAAc;GAC/D,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAe,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAC/D,MAAM,cAAc,eAAe,CAAE,GAAE,KAAK,EAAE;IAC5C,QAAQ;IAER,gBAAgB,KAAK,OAAO,kBAAkB,WAAW,eAAe,EACtE,eAAe,KAChB,KAAI,KAAK,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG,WAAW,EAAE,eAAe,KAAM,IAAG,CAAE,EAAC,QAAQ;GACtG,EAAC;GACF,uBAAuB;GACvB,2BAA2B,iCACzB,0CACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,KAAK,MAAM,EAAE,UAAU,WAAW,GAAG,IAAI,cAAc,UAAU,IAAI,CAAC,UAAW,EAAC;EACxF,MAAM,YAAY,CAAE;EACpB,IAAI,eAAe;EACnB,IAAI,QAAQ;GACV,cAAc;GACd,kBAAkB;EACnB;EACD,IAAI;EACJ,MAAM,kBAAkB,CAAE;EAC1B,MAAM,mCAAmC,KAAK,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG;AACzF,SAAO;GACL,QAAQ,SAAS,YACf,IAAI,gBAAgB;IAClB,UAAU,OAAO,YAAY;KAC3B,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3D,UAAK,MAAM,SAAS;AAClB,qBAAe;AACf,iBAAW,QAAQ;OAAE,MAAM;OAAS,OAAO,MAAM;MAAO,EAAC;AACzD;KACD;KACD,MAAM,QAAQ,MAAM;AACpB,SAAI,WAAW,OAAO;AACpB,qBAAe;AACf,iBAAW,QAAQ;OAAE,MAAM;OAAS,OAAO,MAAM;MAAO,EAAC;AACzD;KACD;AACD,SAAI,MAAM,GACR,YAAW,QAAQ;MACjB,MAAM;MACN,IAAI,MAAM;KACX,EAAC;AAEJ,SAAI,MAAM,MACR,YAAW,QAAQ;MACjB,MAAM;MACN,SAAS,MAAM;KAChB,EAAC;AAEJ,SAAI,MAAM,SAAS,MAAM;AACvB,cAAQ;OACN,cAAc,MAAM,MAAM;OAC1B,kBAAkB,MAAM,MAAM;MAC/B;AACD,sBAAgB,eAAe,MAAM,MAAM;AAC3C,UAAI,MAAM,MAAM,sBACd,iBAAgB,sBAAsB,EACpC,eAAe,MAAM,MAAM,MAAM,sBAAsB,kBAAkB,OAAO,MAAM,EACvF;AAEH,sBAAgB,mBAAmB,MAAM,MAAM;AAC/C,UAAI,MAAM,MAAM,0BACd,iBAAgB,0BAA0B,EACxC,kBAAkB,MAAM,MAAM,MAAM,0BAA0B,qBAAqB,OAAO,MAAM,EACjG;AAEH,sBAAgB,OAAO,MAAM,MAAM;AACnC,sBAAgB,cAAc,MAAM,MAAM;KAC3C;KACD,MAAM,SAAS,MAAM,QAAQ;AAC7B,UAAK,UAAU,YAAY,IAAI,OAAO,kBAAkB,KACtD,gBAAe,0BAA0B,OAAO,cAAc;AAEhE,UAAK,UAAU,YAAY,IAAI,OAAO,UAAU,KAC9C;KAEF,MAAM,QAAQ,OAAO;AACrB,SAAI,MAAM,WAAW,KACnB,YAAW,QAAQ;MACjB,MAAM;MACN,WAAW,MAAM;KAClB,EAAC;AAEJ,SAAI,MAAM,aAAa,KACrB,YAAW,QAAQ;MACjB,MAAM;MACN,WAAW,MAAM;KAClB,EAAC;AAEJ,SAAI,MAAM,qBAAqB,MAAM,kBAAkB,SAAS,EAC9D,MAAK,MAAM,UAAU,MAAM,kBACzB,SAAQ,OAAO,MAAf;MACE,KAAK,kBAA6B;AAChC,WAAI,OAAO,KACT,YAAW,QAAQ;QACjB,MAAM;QACN,WAAW,OAAO;OACnB,EAAC;AAEJ,WAAI,OAAO,UACT,YAAW,QAAQ;QACjB,MAAM;QACN,WAAW,OAAO;OACnB,EAAC;AAEJ;MACD;MACD,KAAK,uBAAuC;AAC1C,WAAI,OAAO,KACT,YAAW,QAAQ;QACjB,MAAM;QACN,MAAM,OAAO;OACd,EAAC;AAEJ;MACD;MACD,KAAK,qBAAmC;AACtC,WAAI,OAAO,QACT,YAAW,QAAQ;QACjB,MAAM;QACN,WAAW,OAAO;OACnB,EAAC;AAEJ;MACD;MACD,QAEE;KAEH;KAGL,MAAM,iBAAiB,gCACrB,UAAU,YAAY,IAAI,OAAO,SAClC;AACD,SAAI,kBAAkB,YAAY,IAAI,eAAe,QAAQ;AAC3D,UAAI,kBAAkB,EACpB,YAAW,CAAE;AAEf,eAAS,KAAK,GAAG,eAAe;KACjC;AACD,SAAI,MAAM,cAAc,KACtB,MAAK,MAAM,iBAAiB,MAAM,YAAY;MAC5C,MAAM,QAAQ,cAAc;AAC5B,UAAI,UAAU,UAAU,MAAM;AAC5B,WAAI,cAAc,SAAS,WACzB,OAAM,IAAI,yBAAyB;QACjC,MAAM;QACN,UAAU;OACX;AAEH,WAAI,cAAc,MAAM,KACtB,OAAM,IAAI,yBAAyB;QACjC,MAAM;QACN,UAAU;OACX;AAEH,aAAM,MAAM,cAAc,aAAa,YAAY,IAAI,IAAI,SAAS,KAClE,OAAM,IAAI,yBAAyB;QACjC,MAAM;QACN,UAAU;OACX;AAEH,iBAAU,SAAS;QACjB,IAAI,cAAc;QAClB,MAAM;QACN,UAAU;SACR,MAAM,cAAc,SAAS;SAC7B,YAAY,KAAK,cAAc,SAAS,cAAc,OAAO,KAAK;QACnE;QACD,MAAM;OACP;OACD,MAAM,YAAY,UAAU;AAC5B,WAAI,aAAa,KACf,OAAM,IAAI,MAAM;AAElB,aAAM,KAAK,UAAU,aAAa,YAAY,IAAI,GAAG,SAAS,UAAU,KAAK,UAAU,aAAa,YAAY,IAAI,GAAG,cAAc,QAAQ,eAAe,UAAU,SAAS,UAAU,EAAE;AACzL,mBAAW,QAAQ;SACjB,MAAM;SACN,cAAc;SACd,YAAY,UAAU;SACtB,UAAU,UAAU,SAAS;SAC7B,eAAe,UAAU,SAAS;QACnC,EAAC;AACF,mBAAW,QAAQ;SACjB,MAAM;SACN,cAAc;SACd,aAAa,KAAK,UAAU,OAAO,OAAO,KAAK,YAAY;SAC3D,UAAU,UAAU,SAAS;SAC7B,MAAM,UAAU,SAAS;QAC1B,EAAC;AACF,kBAAU,OAAO;OAClB;AACD;MACD;MACD,MAAM,WAAW,UAAU;AAC3B,UAAI,YAAY,KACd,OAAM,IAAI,MAAM;AAElB,YAAM,KAAK,cAAc,aAAa,YAAY,IAAI,GAAG,cAAc,KACrE,UAAS,SAAS,cAAc,MAAM,KAAK,cAAc,aAAa,YAAY,IAAI,GAAG,cAAc,OAAO,KAAK;AAErH,iBAAW,QAAQ;OACjB,MAAM;OACN,cAAc;OACd,YAAY,SAAS;OACrB,UAAU,SAAS,SAAS;OAC5B,gBAAgB,KAAK,cAAc,SAAS,cAAc,OAAO,KAAK;MACvE,EAAC;AACF,YAAM,KAAK,SAAS,aAAa,YAAY,IAAI,GAAG,SAAS,UAAU,KAAK,SAAS,aAAa,YAAY,IAAI,GAAG,cAAc,QAAQ,eAAe,SAAS,SAAS,UAAU,EAAE;AACtL,kBAAW,QAAQ;QACjB,MAAM;QACN,cAAc;QACd,aAAa,KAAK,SAAS,OAAO,OAAO,KAAK,YAAY;QAC1D,UAAU,SAAS,SAAS;QAC5B,MAAM,SAAS,SAAS;OACzB,EAAC;AACF,gBAAS,OAAO;MACjB;KACF;IAEJ;IACD,MAAM,YAAY;KAChB,IAAI;AACJ,SAAI,iBAAiB,cACnB;WAAK,MAAM,YAAY,UACrB,MAAK,SAAS,MAAM;AAClB,kBAAW,QAAQ;QACjB,MAAM;QACN,cAAc;QACd,aAAa,MAAM,SAAS,OAAO,OAAO,MAAM,YAAY;QAC5D,UAAU,SAAS,SAAS;QAE5B,MAAM,eAAe,SAAS,SAAS,UAAU,GAAG,SAAS,SAAS,YAAY;OACnF,EAAC;AACF,gBAAS,OAAO;MACjB;KACF;KAEH,MAAM,mBAAmB,CAAE;AAC3B,SAAI,iCAAiC,gBAAgB,qBAAqB,KAAK,gBAAgB,cAAc,KAAK,gBAAgB,6BAA6B,KAAK,gBAAgB,iCAAiC,GACnN,kBAAiB,aAAa,EAC5B,OAAO,gBACR;KAEH,MAAM,sBAAsB,OAAO,KAAK,iBAAiB,CAAC,SAAS,KAAK;AACxE,gBAAW,QAAQ,eAAe;MAChC,MAAM;MACN;MACA;MACA;KACD,GAAE,sBAAsB,EAAE,iBAAkB,IAAG,CAAE,EAAC,CAAC;IACrD;GACF,GACF;GACD,SAAS;IAAE;IAAW;GAAa;GACnC,aAAa,EAAE,SAAS,gBAAiB;GACzC,UAAU,CAAE;EACb;CACF;AACF;AACD,IAAI,6CAA6C,WAAU;CACzD,IAAI,YAAW,CAAC,UAAU;CAC1B,OAAO,YAAW,CAAC,UAAU;CAC7B,OAAO,WAAU;EACf,eAAe,YAAW;EAC1B,uBAAuB,WAAU,EAC/B,eAAe,YAAW,CAC3B,EAAC,CAAC,SAAS;EACZ,mBAAmB,YAAW;EAC9B,2BAA2B,WAAU,EACnC,kBAAkB,YAAW,CAC9B,EAAC,CAAC,SAAS;EACZ,cAAc,YAAW;EACzB,MAAM,YAAW,CAAC,UAAU;CAC7B,EAAC,CAAC,SAAS;AACb,EAAC;AACF,IAAI,kDAAkD,2CAA2C,OAAO,EACtG,SAAS,UACP,WAAU;CACR,SAAS,WAAU;EACjB,MAAM,YAAW,YAAY;EAC7B,SAAS,YAAW,CAAC,UAAU,CAAC,UAAU;EAC1C,WAAW,YAAW,CAAC,UAAU,CAAC,UAAU;EAC5C,mBAAmB,2BAA2B,SAAS;EACvD,YAAY,UACV,WAAU;GACR,IAAI,YAAW,CAAC,UAAU,CAAC,UAAU;GACrC,MAAM,YAAW,WAAW;GAC5B,UAAU,WAAU;IAClB,MAAM,YAAW;IACjB,WAAW,YAAW;GACvB,EAAC;EACH,EAAC,CACH,CAAC,UAAU;CACb,EAAC;CACF,OAAO,YAAW;CAClB,UAAU,WAAU,EAClB,SAAS,UACP,WAAU;EACR,OAAO,YAAW;EAClB,SAAS,YAAW;EACpB,cAAc,UACZ,WAAU;GACR,OAAO,YAAW;GAClB,SAAS,YAAW;EACrB,EAAC,CACH;CACF,EAAC,CACH,CAAC,UAAU,CACb,EAAC,CAAC,UAAU,CAAC,UAAU;CACxB,eAAe,YAAW,CAAC,UAAU,CAAC,UAAU;AACjD,EAAC,CACH,CACF,EAAC;AACF,IAAI,4CAA4C,UAAS,CACvD,2CAA2C,OAAO,EAChD,SAAS,UACP,WAAU;CACR,OAAO,WAAU;EACf,MAAM,SAAQ,CAAC,WAAY,EAAC,CAAC,UAAU;EACvC,SAAS,YAAW,CAAC,SAAS;EAC9B,WAAW,YAAW,CAAC,SAAS,CAAC,UAAU;EAC3C,mBAAmB,2BAA2B,SAAS;EACvD,YAAY,UACV,WAAU;GACR,OAAO,YAAW;GAClB,IAAI,YAAW,CAAC,SAAS;GACzB,MAAM,YAAW,WAAW,CAAC,UAAU;GACvC,UAAU,WAAU;IAClB,MAAM,YAAW,CAAC,SAAS;IAC3B,WAAW,YAAW,CAAC,SAAS;GACjC,EAAC;EACH,EAAC,CACH,CAAC,SAAS;CACZ,EAAC,CAAC,SAAS;CACZ,UAAU,WAAU,EAClB,SAAS,UACP,WAAU;EACR,OAAO,YAAW;EAClB,SAAS,YAAW;EACpB,cAAc,UACZ,WAAU;GACR,OAAO,YAAW;GAClB,SAAS,YAAW;EACrB,EAAC,CACH;CACF,EAAC,CACH,CAAC,UAAU,CACb,EAAC,CAAC,SAAS;CACZ,eAAe,YAAW,CAAC,UAAU,CAAC,UAAU;CAChD,OAAO,YAAW;AACnB,EAAC,CACH,CACF,EAAC,EACF,6BACD,EAAC;AACF,SAAS,0BAA0B,MAAM;CACvC,IAAI;CACJ,MAAM,UAAU,KAAK,KAAK,UAAU,YAAY,IAAI,GAAG,UAAU,KAAK,aAAa;AACnF,KAAI,SAAS,KACX,QAAO;EAAE,YAAY;EAAG,kBAAkB;CAAG;CAE/C,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS;AACtC,MAAI,eAAe,KAAK,CACtB,QAAO;GACL,MAAM;GACN,UAAU;IACR,MAAM,KAAK;IACX,aAAa,KAAK;IAClB,YAAY,KAAK;GAClB;EACF;AAEH,SAAO;GACL,MAAM;GACN,UAAU,EACR,MAAM,KAAK,KACZ;EACF;CACF,EAAC;CACF,MAAM,aAAa,KAAK;AACxB,KAAI,cAAc,KAChB,QAAO;EAAE,OAAO;EAAa,kBAAkB;CAAG;CAEpD,MAAM,OAAO,WAAW;AACxB,SAAQ,MAAR;EACE,KAAK;EACL,KAAK;EACL,KAAK,WACH,QAAO;GAAE,OAAO;GAAa,aAAa;EAAM;EAClD,KAAK,OACH,QAAO;GACL,OAAO;GACP,aAAa;IACX,MAAM;IACN,UAAU,EACR,MAAM,WAAW,SAClB;GACF;EACF;EACH,SAAS;GACP,MAAM,mBAAmB;AACzB,SAAM,IAAI,OAAO,gCAAgC;EAClD;CACF;AACF;AAiBD,SAAS,oCAAoC,EAC3C,QACA,aACA,OAAO,QACP,YAAY,aACb,EAAE;AACD,KAAI,gBAAgB,YAAY,OAAO,WAAW,KAAK,OAAO,MAAM,OAAO,GAAG,SAAS,UAAU,OAAO,GAAG,QAAQ,WAAW,KAAK,OAAO,GAAG,QAAQ,MAAM,OAAO,GAAG,QAAQ,GAAG,SAAS,OACvL,QAAO,EAAE,QAAQ,OAAO,GAAG,QAAQ,GAAG,KAAM;CAE9C,IAAI,OAAO;AACX,KAAI,OAAO,MAAM,OAAO,GAAG,SAAS,UAAU;AAC5C,aAAW,OAAO,GAAG,QAAQ;;;AAG7B,WAAS,OAAO,MAAM,EAAE;CACzB;AACD,MAAK,MAAM,EAAE,MAAM,SAAS,IAAI,OAC9B,SAAQ,MAAR;EACE,KAAK,SACH,OAAM,IAAI,mBAAmB;GAC3B,SAAS;GACT;EACD;EAEH,KAAK,QAAQ;GACX,MAAM,cAAc,QAAQ,IAAI,CAAC,SAAS;AACxC,YAAQ,KAAK,MAAb;KACE,KAAK,OACH,QAAO,KAAK;KAEd,KAAK,QACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,SAChB;KAEH,KAAK,OACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,mBAChB;KAEH,SAAS;MACP,MAAM,mBAAmB;AACzB,YAAM,IAAI,OACP,4BAA4B;KAEhC;IACF;GACF,EAAC,CAAC,KAAK,GAAG;AACX,cAAW,KAAK;EACtB,YAAY;;;AAGN;EACD;EACD,KAAK,aAAa;GAChB,MAAM,mBAAmB,QAAQ,IAAI,CAAC,SAAS;AAC7C,YAAQ,KAAK,MAAb;KACE,KAAK,OACH,QAAO,KAAK;KAEd,KAAK,YACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,qBAChB;KAEH,KAAK,YACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,qBAChB;KAEH,KAAK,qBACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,8BAChB;KAEH,KAAK,OACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,mBAChB;KAEH,SAAS;MACP,MAAM,mBAAmB;AACzB,YAAM,IAAI,OACP,4BAA4B;KAEhC;IACF;GACF,EAAC,CAAC,KAAK,GAAG;AACX,cAAW,UAAU;EAC3B,iBAAiB;;;AAGX;EACD;EACD,KAAK,OACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,gBAChB;EAEH,SAAS;GACP,MAAM,mBAAmB;AACzB,SAAM,IAAI,OAAO,oBAAoB;EACtC;CACF;AAEH,YAAW,UAAU;;AAErB,QAAO,EACL,QAAQ,KACT;AACF;AAGD,SAAS,gCAAgC,UAAU;AACjD,QAAO,YAAY,YAAY,IAAI,SAAS,OAAO,IAAI,CAAC,OAAO,UAAU;EACvE,IAAI,IAAI;AACR,SAAO;GACL;GACA,UAAU,KAAK,SAAS,eAAe,WAAW,OAAO,KAAK;GAC9D,aAAa,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS,aAAa,WAAW,OAAO,KAAK,CAAE,EAAC,CAAC,IACzG,CAAC,CAAC,QAAQ,QAAQ,MAAM;IACtB,OAAO;IACP;GACD,GACF,GAAG,CAAE;EACP;CACF,EAAC;AACH;AAGD,IAAI,oCAAoC,MAAM;CAC5C,YAAY,SAAS,UAAU,QAAQ;AACrC,OAAK,uBAAuB;AAC5B,OAAK,mCAAmC;AACxC,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,SAAS;CACf;CACD,IAAI,WAAW;AACb,SAAO,KAAK,OAAO;CACpB;CACD,QAAQ,EACN,MACA,aACA,QACA,WACA,aACA,MACA,kBACA,iBACA,MACA,gBACA,MACA,eACA,kBACD,EAAE;EACD,IAAI,IAAI;EACR,MAAM,OAAO,KAAK;EAClB,MAAM,oBAAoB,KAAK,oBAAoB,YAAY,IAAI,iBAAiB,eAAe,OAAO,KAAK,CAAE;EACjH,MAAM,EAAE,QAAQ,kBAAkB,GAAG,oCAAoC;GACvE;GACA;EACD,EAAC;EACF,MAAM,WAAW,eAAe,eAAe,eAAe;GAE5D,OAAO,KAAK;GACZ,QAAQ,KAAK,SAAS;GAEtB,YAAY,KAAK,SAAS;GAC1B,iBAAiB,KAAK,SAAS,aAAa,WAAW,KAAK,SAAS,kBAAkB,KAAK,SAAS,aAAa,YAAY,KAAK,SAAS,WAAW,SAAS,SAAS;GACzK,QAAQ,KAAK,SAAS;GACtB,MAAM,KAAK,SAAS;GAEpB,YAAY;GACZ;GACA,OAAO;GACP,mBAAmB;GACnB,kBAAkB;GAClB;GACA,MAAM;GACN,iBAAiB;GACjB,OAAO;GAEP,QAAQ;GAER,mBAAmB,KAAK,SAAS;GACjC,WAAW,KAAK,SAAS;EAC1B,GAAE,KAAK,OAAO,UAAU,EAAE,KAAK,SAAS,UAAU,EAAE,iBAAiB;AACtE,UAAQ,MAAR;GACE,KAAK,WAAW;AACd,SAAK,KAAK,KAAK,UAAU,YAAY,IAAI,GAAG,OAC1C,OAAM,IAAIC,8BAA+B,EACvC,eAAe,QAChB;AAEH,QAAI,KAAK,WACP,OAAM,IAAIA,8BAA+B,EACvC,eAAe,aAChB;AAEH,WAAO;GACR;GACD,KAAK,cACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,mBAChB;GAEH,KAAK,cACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,mBAChB;GAGH,SAAS;IACP,MAAM,mBAAmB;AACzB,UAAM,IAAIA,8BAA+B,EACvC,kBAAkB,iBAAiB,OACpC;GACF;EACF;CACF;CACD,MAAM,WAAW,SAAS;EACxB,IAAI,IAAI,IAAI,IAAI,IAAI;EACpB,MAAM,OAAO,KAAK,QAAQ,QAAQ;EAClC,MAAM,EAAE,iBAAiB,OAAO,UAAU,GAAG,MAAM,cAAe;GAChE,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAChE,MAAM;GACN,uBAAuB;GACvB,2BAA2B,0BACzB,gCACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,KAAK,MAAM,EAAE,QAAQ,WAAW,GAAG,IAAI,cAAc,UAAU,IAAI,CAAC,QAAS,EAAC;AACpF,MAAI,WAAW,SACb,OAAM,IAAI,SAAS,SAAS,MAAM;EAEpC,MAAM,SAAS,SAAS,QAAQ;AAChC,OAAK,OACH,OAAM,IAAI,MAAM;AAElB,SAAO;GACL,UAAU;IACR,IAAI,SAAS;IACb,SAAS,SAAS;GACnB;GACD,OAAO,KAAK,OAAO,SAAS,OAAO,KAAK;GACxC,WAAW,OAAO,kBAAkB;GACpC,OAAO;IACL,eAAe,MAAM,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG,kBAAkB,OAAO,KAAK;IAC9F,mBAAmB,MAAM,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG,sBAAsB,OAAO,KAAK;GACvG;GACD,cAAc,0BAA0B,OAAO,cAAc;GAC7D,UAAU,gCAAgC,OAAO,SAAS;GAC1D,SAAS;IAAE;IAAW;GAAa;GACnC,aAAa,EAAE,SAAS,gBAAiB;GACzC,UAAU,CAAE;EACb;CACF;CACD,MAAM,SAAS,SAAS;EACtB,MAAM,OAAO,KAAK,QAAQ,QAAQ;EAClC,MAAM,EAAE,iBAAiB,OAAO,UAAU,GAAG,MAAM,cAAe;GAChE,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAChE,MAAM,cAAc,eAAe,CAAE,GAAE,KAAK,QAAQ,QAAQ,CAAC,EAAE;IAC7D,QAAQ;IAER,gBAAgB,KAAK,OAAO,kBAAkB,WAAW,EAAE,eAAe,KAAM,SAAQ;GACzF,EAAC;GACF,uBAAuB;GACvB,2BAA2B,iCACzB,gCACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,KAAK,MAAM,EAAE,QAAQ,WAAW,GAAG,IAAI,cAAc,UAAU,IAAI,CAAC,QAAS,EAAC;EACpF,IAAI,eAAe;EACnB,IAAI,QAAQ;GACV,cAAc;GACd,kBAAkB;EACnB;EACD,IAAI;AACJ,SAAO;GACL,QAAQ,SAAS,YACf,IAAI,gBAAgB;IAClB,UAAU,OAAO,YAAY;AAC3B,UAAK,MAAM,SAAS;AAClB,qBAAe;AACf,iBAAW,QAAQ;OAAE,MAAM;OAAS,OAAO,MAAM;MAAO,EAAC;AACzD;KACD;KACD,MAAM,QAAQ,MAAM;AACpB,SAAI,WAAW,OAAO;AACpB,qBAAe;AACf,iBAAW,QAAQ;OAAE,MAAM;OAAS,OAAO,MAAM;MAAO,EAAC;AACzD;KACD;AACD,SAAI,MAAM,SAAS,KACjB,SAAQ;MACN,cAAc,MAAM,MAAM;MAC1B,kBAAkB,MAAM,MAAM;KAC/B;KAEH,MAAM,SAAS,MAAM,QAAQ;AAC7B,UAAK,UAAU,YAAY,IAAI,OAAO,kBAAkB,KACtD,gBAAe,0BAA0B,OAAO,cAAc;AAEhE,UAAK,UAAU,YAAY,IAAI,OAAO,SAAS,KAC7C,YAAW,QAAQ;MACjB,MAAM;MACN,WAAW,OAAO;KACnB,EAAC;KAEJ,MAAM,iBAAiB,gCACrB,UAAU,YAAY,IAAI,OAAO,SAClC;AACD,SAAI,kBAAkB,YAAY,IAAI,eAAe,QAAQ;AAC3D,UAAI,kBAAkB,EACpB,YAAW,CAAE;AAEf,eAAS,KAAK,GAAG,eAAe;KACjC;IACF;IACD,MAAM,YAAY;AAChB,gBAAW,QAAQ;MACjB,MAAM;MACN;MACA;MACA;KACD,EAAC;IACH;GACF,GACF;GACD,SAAS;IAAE;IAAW;GAAa;GACnC,aAAa,EAAE,SAAS,gBAAiB;GACzC,UAAU,CAAE;EACb;CACF;AACF;AACD,IAAI,kCAAkC,UAAS,CAC7C,WAAU;CACR,IAAI,YAAW,CAAC,UAAU;CAC1B,OAAO,YAAW,CAAC,UAAU;CAC7B,SAAS,UACP,WAAU;EACR,MAAM,YAAW;EACjB,WAAW,YAAW,CAAC,SAAS,CAAC,UAAU;EAC3C,mBAAmB,2BAA2B,SAAS;EACvD,eAAe,YAAW,CAAC,SAAS;EACpC,OAAO,YAAW;EAClB,UAAU,WAAU;GAClB,QAAQ,UAAS,YAAW,CAAC;GAC7B,gBAAgB,UAAS,YAAW,CAAC;GACrC,cAAc,UAAS,WAAU,YAAW,EAAE,YAAW,CAAC,CAAC,CAAC,UAAU;EACvE,EAAC,CAAC,UAAU,CAAC,UAAU;CACzB,EAAC,CACH;CACD,OAAO,WAAU;EACf,eAAe,YAAW;EAC1B,mBAAmB,YAAW;CAC/B,EAAC,CAAC,UAAU,CAAC,UAAU;AACzB,EAAC,EACF,6BACD,EAAC;AAGF,IAAI,aAAa,MAAM;;;;CAIrB,YAAY,UAAU,CAAE,GAAE;EACxB,IAAI,IAAI;AACR,OAAK,WAAW,KAAK,sBAAsB,KAAK,QAAQ,YAAY,OAAO,KAAK,QAAQ,QAAQ,KAAK,OAAO,KAAK;AACjH,OAAK,SAAS,QAAQ;AACtB,OAAK,UAAU,QAAQ;CACxB;CACD,IAAI,aAAa;AACf,SAAO;GACL,SAAS,KAAK;GACd,SAAS,MAAM,eAAe,EAC5B,gBAAgB,SAAS,WAAW;IAClC,QAAQ,KAAK;IACb,yBAAyB;IACzB,aAAa;GACd,EAAC,GACH,GAAE,KAAK,QAAQ;EACjB;CACF;CACD,KAAK,SAAS,WAAW,CAAE,GAAE;AAC3B,SAAO,IAAI,4BAA4B,SAAS,UAAU,cAAc,eAAe,EACrF,UAAU,kBACX,GAAE,KAAK,WAAW,EAAE;GACnB,eAAe;GACf,KAAK,CAAC,EAAE,MAAM,QAAQ,KAAK,UAAU;EACtC,EAAC;CACH;CACD,WAAW,SAAS,WAAW,CAAE,GAAE;AACjC,SAAO,IAAI,kCAAkC,SAAS,UAAU,cAAc,eAAe,EAC3F,UAAU,wBACX,GAAE,KAAK,WAAW,EAAE;GACnB,eAAe;GACf,KAAK,CAAC,EAAE,MAAM,QAAQ,KAAK,UAAU;EACtC,EAAC;CACH;AACF;AAID,SAAS,iBAAiB,UAAU,CAAE,GAAE;CACtC,IAAI,IAAI,IAAI;CACZ,MAAM,WAAW,KAAK,sBAAuB,KAAK,QAAQ,YAAY,OAAO,KAAK,QAAQ,QAAQ,KAAK,OAAO,KAAK;CACnH,MAAM,iBAAiB,KAAK,QAAQ,kBAAkB,OAAO,KAAK;CAClE,MAAM,aAAa,MAAM,eAAe,EACtC,gBAAgB,SAAS,WAAY;EACnC,QAAQ,QAAQ;EAChB,yBAAyB;EACzB,aAAa;CACd,EAAC,GACH,GAAE,QAAQ,QAAQ;CACnB,MAAM,kBAAkB,CAAC,SAAS,WAAW,CAAE,MAAK,IAAI,4BAA4B,SAAS,UAAU;EACrG,UAAU;EACV,KAAK,CAAC,EAAE,MAAM,QAAQ,UAAU;EAChC,SAAS;EACT;EACA,OAAO,QAAQ;EACf,WAAW,QAAQ;CACpB;CACD,MAAM,wBAAwB,CAAC,SAAS,WAAW,CAAE,MAAK,IAAI,kCAAkC,SAAS,UAAU;EACjH,UAAU;EACV,KAAK,CAAC,EAAE,MAAM,QAAQ,UAAU;EAChC,SAAS;EACT;EACA,OAAO,QAAQ;EACf,WAAW,QAAQ;CACpB;CACD,MAAM,sBAAsB,CAAC,SAAS,aAAa;AACjD,MAAI,IAAI,OACN,OAAM,IAAI,MACR;AAGJ,MAAI,YAAY,gCACd,QAAO,sBACL,SACA,SACD;AAEH,SAAO,gBAAgB,SAAS,SAAS;CAC1C;CACD,MAAM,WAAW,CAAC,SAAS,aAAa,oBAAoB,SAAS,SAAS;AAC9E,UAAS,gBAAgB;AACzB,UAAS,OAAO;AAChB,UAAS,aAAa;AACtB,QAAO;AACR;AACD,IAAI,aAAa,iBAAiB,EAChC,eAAe,SAEhB,EAAC"}