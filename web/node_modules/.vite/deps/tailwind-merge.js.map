{"version": 3, "file": "tailwind-merge.js", "names": ["classGroup"], "sources": ["../../tailwind-merge/dist/bundle-mjs.mjs"], "sourcesContent": ["const CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    prefix\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  const prefixedClassGroupEntries = getPrefixedClassGroupEntries(Object.entries(config.classGroups), prefix);\n  prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n    processClassesRecursively(classGroup, classMap, classGroupId, theme);\n  });\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\nconst getPrefixedClassGroupEntries = (classGroupEntries, prefix) => {\n  if (!prefix) {\n    return classGroupEntries;\n  }\n  return classGroupEntries.map(([classGroupId, classGroup]) => {\n    const prefixedClassGroup = classGroup.map(classDefinition => {\n      if (typeof classDefinition === 'string') {\n        return prefix + classDefinition;\n      }\n      if (typeof classDefinition === 'object') {\n        return Object.fromEntries(Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]));\n      }\n      return classDefinition;\n    });\n    return [classGroupId, prefixedClassGroup];\n  });\n};\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst createParseClassName = config => {\n  const {\n    separator,\n    experimentalParseClassName\n  } = config;\n  const isSeparatorSingleCharacter = separator.length === 1;\n  const firstSeparatorCharacter = separator[0];\n  const separatorLength = separator.length;\n  // parseClassName inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n  const parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0) {\n        if (currentCharacter === firstSeparatorCharacter && (isSeparatorSingleCharacter || className.slice(index, index + separatorLength) === separator)) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + separatorLength;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const hasImportantModifier = baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER);\n    const baseClassName = hasImportantModifier ? baseClassNameWithImportantModifier.substring(1) : baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (experimentalParseClassName) {\n    return className => experimentalParseClassName({\n      className,\n      parseClassName\n    });\n  }\n  return parseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst sortModifiers = modifiers => {\n  if (modifiers.length <= 1) {\n    return modifiers;\n  }\n  const sortedModifiers = [];\n  let unsortedModifiers = [];\n  modifiers.forEach(modifier => {\n    const isArbitraryVariant = modifier[0] === '[';\n    if (isArbitraryVariant) {\n      sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n      unsortedModifiers = [];\n    } else {\n      unsortedModifiers.push(modifier);\n    }\n  });\n  sortedModifiers.push(...unsortedModifiers.sort());\n  return sortedModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    let hasPostfixModifier = Boolean(maybePostfixModifierPosition);\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst stringLengths = /*#__PURE__*/new Set(['px', 'full', 'screen']);\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isLength = value => isNumber(value) || stringLengths.has(value) || fractionRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, 'length', isLengthOnly);\nconst isNumber = value => Boolean(value) && !Number.isNaN(Number(value));\nconst isArbitraryNumber = value => getIsArbitraryValue(value, 'number', isNumber);\nconst isInteger = value => Boolean(value) && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst sizeLabels = /*#__PURE__*/new Set(['length', 'size', 'percentage']);\nconst isArbitrarySize = value => getIsArbitraryValue(value, sizeLabels, isNever);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, 'position', isNever);\nconst imageLabels = /*#__PURE__*/new Set(['image', 'url']);\nconst isArbitraryImage = value => getIsArbitraryValue(value, imageLabels, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, '', isShadow);\nconst isAny = () => true;\nconst getIsArbitraryValue = (value, label, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return typeof label === 'string' ? result[1] === label : label.has(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isInteger,\n  isLength,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  const colors = fromTheme('colors');\n  const spacing = fromTheme('spacing');\n  const blur = fromTheme('blur');\n  const brightness = fromTheme('brightness');\n  const borderColor = fromTheme('borderColor');\n  const borderRadius = fromTheme('borderRadius');\n  const borderSpacing = fromTheme('borderSpacing');\n  const borderWidth = fromTheme('borderWidth');\n  const contrast = fromTheme('contrast');\n  const grayscale = fromTheme('grayscale');\n  const hueRotate = fromTheme('hueRotate');\n  const invert = fromTheme('invert');\n  const gap = fromTheme('gap');\n  const gradientColorStops = fromTheme('gradientColorStops');\n  const gradientColorStopPositions = fromTheme('gradientColorStopPositions');\n  const inset = fromTheme('inset');\n  const margin = fromTheme('margin');\n  const opacity = fromTheme('opacity');\n  const padding = fromTheme('padding');\n  const saturate = fromTheme('saturate');\n  const scale = fromTheme('scale');\n  const sepia = fromTheme('sepia');\n  const skew = fromTheme('skew');\n  const space = fromTheme('space');\n  const translate = fromTheme('translate');\n  const getOverscroll = () => ['auto', 'contain', 'none'];\n  const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing];\n  const getSpacingWithArbitrary = () => [isArbitraryValue, spacing];\n  const getLengthWithEmptyAndArbitrary = () => ['', isLength, isArbitraryLength];\n  const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue];\n  const getPositions = () => ['bottom', 'center', 'left', 'left-bottom', 'left-top', 'right', 'right-bottom', 'right-top', 'top'];\n  const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'];\n  const getBlendModes = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const getAlign = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'];\n  const getZeroAndEmpty = () => ['', '0', isArbitraryValue];\n  const getBreaks = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const getNumberAndArbitrary = () => [isNumber, isArbitraryValue];\n  return {\n    cacheSize: 500,\n    separator: ':',\n    theme: {\n      colors: [isAny],\n      spacing: [isLength, isArbitraryLength],\n      blur: ['none', '', isTshirtSize, isArbitraryValue],\n      brightness: getNumberAndArbitrary(),\n      borderColor: [colors],\n      borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n      borderSpacing: getSpacingWithArbitrary(),\n      borderWidth: getLengthWithEmptyAndArbitrary(),\n      contrast: getNumberAndArbitrary(),\n      grayscale: getZeroAndEmpty(),\n      hueRotate: getNumberAndArbitrary(),\n      invert: getZeroAndEmpty(),\n      gap: getSpacingWithArbitrary(),\n      gradientColorStops: [colors],\n      gradientColorStopPositions: [isPercent, isArbitraryLength],\n      inset: getSpacingWithAutoAndArbitrary(),\n      margin: getSpacingWithAutoAndArbitrary(),\n      opacity: getNumberAndArbitrary(),\n      padding: getSpacingWithArbitrary(),\n      saturate: getNumberAndArbitrary(),\n      scale: getNumberAndArbitrary(),\n      sepia: getZeroAndEmpty(),\n      skew: getNumberAndArbitrary(),\n      space: getSpacingWithArbitrary(),\n      translate: getSpacingWithArbitrary()\n    },\n    classGroups: {\n      // Layout\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', 'video', isArbitraryValue]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isTshirtSize]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': getBreaks()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': getBreaks()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: [...getPositions(), isArbitraryValue]\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: getOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': getOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': getOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': getOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: [inset]\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': [inset]\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': [inset]\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: [inset]\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: [inset]\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: [inset]\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: [inset]\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: [inset]\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: [inset]\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: ['auto', isInteger, isArbitraryValue]\n      }],\n      // Flexbox and Grid\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: getSpacingWithAutoAndArbitrary()\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['wrap', 'wrap-reverse', 'nowrap']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: ['1', 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: getZeroAndEmpty()\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: getZeroAndEmpty()\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: ['first', 'last', 'none', isInteger, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': [isAny]\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: ['auto', {\n          span: ['full', isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': [isAny]\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: ['auto', {\n          span: [isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: [gap]\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': [gap]\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': [gap]\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: ['normal', ...getAlign()]\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': ['start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...getAlign(), 'baseline']\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline']\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': [...getAlign(), 'baseline']\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: [padding]\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: [padding]\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: [padding]\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: [padding]\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: [padding]\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: [padding]\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: [padding]\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: [padding]\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: [padding]\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: [margin]\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: [margin]\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: [margin]\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: [margin]\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: [margin]\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: [margin]\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: [margin]\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: [margin]\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: [margin]\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x': [{\n        'space-x': [space]\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y': [{\n        'space-y': [space]\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // Sizing\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: ['auto', 'min', 'max', 'fit', 'svw', 'lvw', 'dvw', isArbitraryValue, spacing]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [isArbitraryValue, spacing, 'min', 'max', 'fit']\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [isArbitraryValue, spacing, 'none', 'full', 'min', 'max', 'fit', 'prose', {\n          screen: [isTshirtSize]\n        }, isTshirtSize]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/size\n       */\n      size: [{\n        size: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit']\n      }],\n      // Typography\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', isTshirtSize, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black', isArbitraryNumber]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isAny]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest', isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': ['none', isNumber, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose', isLength, isArbitraryValue]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryValue]\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['none', 'disc', 'decimal', isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: [colors]\n      }],\n      /**\n       * Placeholder Opacity\n       * @see https://tailwindcss.com/docs/placeholder-opacity\n       */\n      'placeholder-opacity': [{\n        'placeholder-opacity': [opacity]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: [colors]\n      }],\n      /**\n       * Text Opacity\n       * @see https://tailwindcss.com/docs/text-opacity\n       */\n      'text-opacity': [{\n        'text-opacity': [opacity]\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...getLineStyles(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: ['auto', 'from-font', isLength, isArbitraryLength]\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': ['auto', isLength, isArbitraryValue]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: [colors]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: getSpacingWithArbitrary()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryValue]\n      }],\n      // Backgrounds\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Opacity\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/background-opacity\n       */\n      'bg-opacity': [{\n        'bg-opacity': [opacity]\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: [...getPositions(), isArbitraryPosition]\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: ['no-repeat', {\n          repeat: ['', 'x', 'y', 'round', 'space']\n        }]\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: ['auto', 'cover', 'contain', isArbitrarySize]\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n        }, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: [colors]\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: [gradientColorStops]\n      }],\n      // Borders\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: [borderRadius]\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': [borderRadius]\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': [borderRadius]\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': [borderRadius]\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': [borderRadius]\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': [borderRadius]\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': [borderRadius]\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': [borderRadius]\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': [borderRadius]\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': [borderRadius]\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: [borderWidth]\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': [borderWidth]\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': [borderWidth]\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': [borderWidth]\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': [borderWidth]\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': [borderWidth]\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': [borderWidth]\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': [borderWidth]\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': [borderWidth]\n      }],\n      /**\n       * Border Opacity\n       * @see https://tailwindcss.com/docs/border-opacity\n       */\n      'border-opacity': [{\n        'border-opacity': [opacity]\n      }],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...getLineStyles(), 'hidden']\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x': [{\n        'divide-x': [borderWidth]\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y': [{\n        'divide-y': [borderWidth]\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Divide Opacity\n       * @see https://tailwindcss.com/docs/divide-opacity\n       */\n      'divide-opacity': [{\n        'divide-opacity': [opacity]\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/divide-style\n       */\n      'divide-style': [{\n        divide: getLineStyles()\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: [borderColor]\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': [borderColor]\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': [borderColor]\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': [borderColor]\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': [borderColor]\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': [borderColor]\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': [borderColor]\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': [borderColor]\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': [borderColor]\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: [borderColor]\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: ['', ...getLineStyles()]\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isLength, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: [isLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: [colors]\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w': [{\n        ring: getLengthWithEmptyAndArbitrary()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/ring-color\n       */\n      'ring-color': [{\n        ring: [colors]\n      }],\n      /**\n       * Ring Opacity\n       * @see https://tailwindcss.com/docs/ring-opacity\n       */\n      'ring-opacity': [{\n        'ring-opacity': [opacity]\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://tailwindcss.com/docs/ring-offset-width\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isLength, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://tailwindcss.com/docs/ring-offset-color\n       */\n      'ring-offset-color': [{\n        'ring-offset': [colors]\n      }],\n      // Effects\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow-color\n       */\n      'shadow-color': [{\n        shadow: [isAny]\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [opacity]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...getBlendModes(), 'plus-lighter', 'plus-darker']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': getBlendModes()\n      }],\n      // Filters\n      /**\n       * Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: ['', 'none']\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: [blur]\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [brightness]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [contrast]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue]\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: [grayscale]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [hueRotate]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: [invert]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [saturate]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: [sepia]\n      }],\n      /**\n       * Backdrop Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': ['', 'none']\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': [blur]\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [brightness]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [contrast]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': [grayscale]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [hueRotate]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': [invert]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [opacity]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [saturate]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': [sepia]\n      }],\n      // Tables\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': [borderSpacing]\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': [borderSpacing]\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': [borderSpacing]\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // Transitions and Animation\n      /**\n       * Tranisition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['none', 'all', '', 'colors', 'opacity', 'shadow', 'transform', isArbitraryValue]\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: getNumberAndArbitrary()\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: getNumberAndArbitrary()\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue]\n      }],\n      // Transforms\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: ['', 'gpu', 'none']\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: [scale]\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': [scale]\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': [scale]\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: [isInteger, isArbitraryValue]\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': [translate]\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': [translate]\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': [skew]\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': [skew]\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: ['center', 'top', 'top-right', 'right', 'bottom-right', 'bottom', 'bottom-left', 'left', 'top-left', isArbitraryValue]\n      }],\n      // Interactivity\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: ['auto', colors]\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryValue]\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: [colors]\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['none', 'auto']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', 'y', 'x', '']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue]\n      }],\n      // SVG\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: [colors, 'none']\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: [colors, 'none']\n      }],\n      // Accessibility\n      /**\n       * Screen Readers\n       * @see https://tailwindcss.com/docs/screen-readers\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    }\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  separator,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'separator', separator);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  for (const configKey in override) {\n    overrideConfigProperties(baseConfig[configKey], override[configKey]);\n  }\n  for (const key in extend) {\n    mergeConfigProperties(baseConfig[key], extend[key]);\n  }\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      const mergeValue = mergeObject[key];\n      if (mergeValue !== undefined) {\n        baseObject[key] = (baseObject[key] || []).concat(mergeValue);\n      }\n    }\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\nexport { createTailwindMerge, extendTailwindMerge, fromTheme, getDefaultConfig, mergeConfigs, twJoin, twMerge, validators };\n//# sourceMappingURL=bundle-mjs.mjs.map\n"], "mappings": ";AAAA,MAAM,uBAAuB;AAC7B,MAAM,wBAAwB,YAAU;CACtC,MAAM,WAAW,eAAe,OAAO;CACvC,MAAM,EACJ,wBACA,gCACD,GAAG;CACJ,MAAM,kBAAkB,eAAa;EACnC,MAAM,aAAa,UAAU,MAAM,qBAAqB;AAExD,MAAI,WAAW,OAAO,MAAM,WAAW,WAAW,EAChD,YAAW,OAAO;AAEpB,SAAO,kBAAkB,YAAY,SAAS,IAAI,+BAA+B,UAAU;CAC5F;CACD,MAAM,8BAA8B,CAAC,cAAc,uBAAuB;EACxE,MAAM,YAAY,uBAAuB,iBAAiB,CAAE;AAC5D,MAAI,sBAAsB,+BAA+B,cACvD,QAAO,CAAC,GAAG,WAAW,GAAG,+BAA+B,aAAc;AAExE,SAAO;CACR;AACD,QAAO;EACL;EACA;CACD;AACF;AACD,MAAM,oBAAoB,CAAC,YAAY,oBAAoB;AACzD,KAAI,WAAW,WAAW,EACxB,QAAO,gBAAgB;CAEzB,MAAM,mBAAmB,WAAW;CACpC,MAAM,sBAAsB,gBAAgB,SAAS,IAAI,iBAAiB;CAC1E,MAAM,8BAA8B,sBAAsB,kBAAkB,WAAW,MAAM,EAAE,EAAE,oBAAoB;AACrH,KAAI,4BACF,QAAO;AAET,KAAI,gBAAgB,WAAW,WAAW,EACxC;CAEF,MAAM,YAAY,WAAW,KAAK,qBAAqB;AACvD,QAAO,gBAAgB,WAAW,KAAK,CAAC,EACtC,WACD,KAAK,UAAU,UAAU,CAAC,EAAE;AAC9B;AACD,MAAM,yBAAyB;AAC/B,MAAM,iCAAiC,eAAa;AAClD,KAAI,uBAAuB,KAAK,UAAU,EAAE;EAC1C,MAAM,6BAA6B,uBAAuB,KAAK,UAAU,CAAC;EAC1E,MAAM,WAAW,4BAA4B,UAAU,GAAG,2BAA2B,QAAQ,IAAI,CAAC;AAClG,MAAI,SAEF,QAAO,gBAAgB;CAE1B;AACF;;;;AAID,MAAM,iBAAiB,YAAU;CAC/B,MAAM,EACJ,OACA,QACD,GAAG;CACJ,MAAM,WAAW;EACf,0BAAU,IAAI;EACd,YAAY,CAAE;CACf;CACD,MAAM,4BAA4B,6BAA6B,OAAO,QAAQ,OAAO,YAAY,EAAE,OAAO;AAC1G,2BAA0B,QAAQ,CAAC,CAAC,cAAc,WAAW,KAAK;AAChE,4BAA0B,YAAY,UAAU,cAAc,MAAM;CACrE,EAAC;AACF,QAAO;AACR;AACD,MAAM,4BAA4B,CAAC,YAAY,iBAAiB,cAAc,UAAU;AACtF,YAAW,QAAQ,qBAAmB;AACpC,aAAW,oBAAoB,UAAU;GACvC,MAAM,wBAAwB,oBAAoB,KAAK,kBAAkB,QAAQ,iBAAiB,gBAAgB;AAClH,yBAAsB,eAAe;AACrC;EACD;AACD,aAAW,oBAAoB,YAAY;AACzC,OAAI,cAAc,gBAAgB,EAAE;AAClC,8BAA0B,gBAAgB,MAAM,EAAE,iBAAiB,cAAc,MAAM;AACvF;GACD;AACD,mBAAgB,WAAW,KAAK;IAC9B,WAAW;IACX;GACD,EAAC;AACF;EACD;AACD,SAAO,QAAQ,gBAAgB,CAAC,QAAQ,CAAC,CAAC,KAAKA,aAAW,KAAK;AAC7D,6BAA0BA,cAAY,QAAQ,iBAAiB,IAAI,EAAE,cAAc,MAAM;EAC1F,EAAC;CACH,EAAC;AACH;AACD,MAAM,UAAU,CAAC,iBAAiB,SAAS;CACzC,IAAI,yBAAyB;AAC7B,MAAK,MAAM,qBAAqB,CAAC,QAAQ,cAAY;AACnD,OAAK,uBAAuB,SAAS,IAAI,SAAS,CAChD,wBAAuB,SAAS,IAAI,UAAU;GAC5C,0BAAU,IAAI;GACd,YAAY,CAAE;EACf,EAAC;AAEJ,2BAAyB,uBAAuB,SAAS,IAAI,SAAS;CACvE,EAAC;AACF,QAAO;AACR;AACD,MAAM,gBAAgB,UAAQ,KAAK;AACnC,MAAM,+BAA+B,CAAC,mBAAmB,WAAW;AAClE,MAAK,OACH,QAAO;AAET,QAAO,kBAAkB,IAAI,CAAC,CAAC,cAAc,WAAW,KAAK;EAC3D,MAAM,qBAAqB,WAAW,IAAI,qBAAmB;AAC3D,cAAW,oBAAoB,SAC7B,QAAO,SAAS;AAElB,cAAW,oBAAoB,SAC7B,QAAO,OAAO,YAAY,OAAO,QAAQ,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,KAAK,CAAC,SAAS,KAAK,KAAM,EAAC,CAAC;AAEzG,UAAO;EACR,EAAC;AACF,SAAO,CAAC,cAAc,kBAAmB;CAC1C,EAAC;AACH;AAGD,MAAM,iBAAiB,kBAAgB;AACrC,KAAI,eAAe,EACjB,QAAO;EACL,KAAK;EACL,KAAK,MAAM,CAAE;CACd;CAEH,IAAI,YAAY;CAChB,IAAI,wBAAQ,IAAI;CAChB,IAAI,gCAAgB,IAAI;CACxB,MAAM,SAAS,CAAC,KAAK,UAAU;AAC7B,QAAM,IAAI,KAAK,MAAM;AACrB;AACA,MAAI,YAAY,cAAc;AAC5B,eAAY;AACZ,mBAAgB;AAChB,2BAAQ,IAAI;EACb;CACF;AACD,QAAO;EACL,IAAI,KAAK;GACP,IAAI,QAAQ,MAAM,IAAI,IAAI;AAC1B,OAAI,iBACF,QAAO;AAET,QAAK,QAAQ,cAAc,IAAI,IAAI,cAAiB;AAClD,WAAO,KAAK,MAAM;AAClB,WAAO;GACR;EACF;EACD,IAAI,KAAK,OAAO;AACd,OAAI,MAAM,IAAI,IAAI,CAChB,OAAM,IAAI,KAAK,MAAM;OAErB,QAAO,KAAK,MAAM;EAErB;CACF;AACF;AACD,MAAM,qBAAqB;AAC3B,MAAM,uBAAuB,YAAU;CACrC,MAAM,EACJ,WACA,4BACD,GAAG;CACJ,MAAM,6BAA6B,UAAU,WAAW;CACxD,MAAM,0BAA0B,UAAU;CAC1C,MAAM,kBAAkB,UAAU;CAElC,MAAM,iBAAiB,eAAa;EAClC,MAAM,YAAY,CAAE;EACpB,IAAI,eAAe;EACnB,IAAI,gBAAgB;EACpB,IAAI;AACJ,OAAK,IAAI,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;GACrD,IAAI,mBAAmB,UAAU;AACjC,OAAI,iBAAiB,GAAG;AACtB,QAAI,qBAAqB,4BAA4B,8BAA8B,UAAU,MAAM,OAAO,QAAQ,gBAAgB,KAAK,YAAY;AACjJ,eAAU,KAAK,UAAU,MAAM,eAAe,MAAM,CAAC;AACrD,qBAAgB,QAAQ;AACxB;IACD;AACD,QAAI,qBAAqB,KAAK;AAC5B,+BAA0B;AAC1B;IACD;GACF;AACD,OAAI,qBAAqB,IACvB;YACS,qBAAqB,IAC9B;EAEH;EACD,MAAM,qCAAqC,UAAU,WAAW,IAAI,YAAY,UAAU,UAAU,cAAc;EAClH,MAAM,uBAAuB,mCAAmC,WAAW,mBAAmB;EAC9F,MAAM,gBAAgB,uBAAuB,mCAAmC,UAAU,EAAE,GAAG;EAC/F,MAAM,+BAA+B,2BAA2B,0BAA0B,gBAAgB,0BAA0B;AACpI,SAAO;GACL;GACA;GACA;GACA;EACD;CACF;AACD,KAAI,2BACF,QAAO,eAAa,2BAA2B;EAC7C;EACA;CACD,EAAC;AAEJ,QAAO;AACR;;;;;;AAMD,MAAM,gBAAgB,eAAa;AACjC,KAAI,UAAU,UAAU,EACtB,QAAO;CAET,MAAM,kBAAkB,CAAE;CAC1B,IAAI,oBAAoB,CAAE;AAC1B,WAAU,QAAQ,cAAY;EAC5B,MAAM,qBAAqB,SAAS,OAAO;AAC3C,MAAI,oBAAoB;AACtB,mBAAgB,KAAK,GAAG,kBAAkB,MAAM,EAAE,SAAS;AAC3D,uBAAoB,CAAE;EACvB,MACC,mBAAkB,KAAK,SAAS;CAEnC,EAAC;AACF,iBAAgB,KAAK,GAAG,kBAAkB,MAAM,CAAC;AACjD,QAAO;AACR;AACD,MAAM,oBAAoB,aAAW;CACnC,OAAO,eAAe,OAAO,UAAU;CACvC,gBAAgB,qBAAqB,OAAO;CAC5C,GAAG,sBAAsB,OAAO;AACjC;AACD,MAAM,sBAAsB;AAC5B,MAAM,iBAAiB,CAAC,WAAW,gBAAgB;CACjD,MAAM,EACJ,gBACA,iBACA,6BACD,GAAG;;;;;;;;CAQJ,MAAM,wBAAwB,CAAE;CAChC,MAAM,aAAa,UAAU,MAAM,CAAC,MAAM,oBAAoB;CAC9D,IAAI,SAAS;AACb,MAAK,IAAI,QAAQ,WAAW,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG;EAC9D,MAAM,oBAAoB,WAAW;EACrC,MAAM,EACJ,WACA,sBACA,eACA,8BACD,GAAG,eAAe,kBAAkB;EACrC,IAAI,qBAAqB,QAAQ,6BAA6B;EAC9D,IAAI,eAAe,gBAAgB,qBAAqB,cAAc,UAAU,GAAG,6BAA6B,GAAG,cAAc;AACjI,OAAK,cAAc;AACjB,QAAK,oBAAoB;AAEvB,aAAS,qBAAqB,OAAO,SAAS,IAAI,MAAM,SAAS;AACjE;GACD;AACD,kBAAe,gBAAgB,cAAc;AAC7C,QAAK,cAAc;AAEjB,aAAS,qBAAqB,OAAO,SAAS,IAAI,MAAM,SAAS;AACjE;GACD;AACD,wBAAqB;EACtB;EACD,MAAM,kBAAkB,cAAc,UAAU,CAAC,KAAK,IAAI;EAC1D,MAAM,aAAa,uBAAuB,kBAAkB,qBAAqB;EACjF,MAAM,UAAU,aAAa;AAC7B,MAAI,sBAAsB,SAAS,QAAQ,CAEzC;AAEF,wBAAsB,KAAK,QAAQ;EACnC,MAAM,iBAAiB,4BAA4B,cAAc,mBAAmB;AACpF,OAAK,IAAI,IAAI,GAAG,IAAI,eAAe,QAAQ,EAAE,GAAG;GAC9C,MAAM,QAAQ,eAAe;AAC7B,yBAAsB,KAAK,aAAa,MAAM;EAC/C;AAED,WAAS,qBAAqB,OAAO,SAAS,IAAI,MAAM,SAAS;CAClE;AACD,QAAO;AACR;;;;;;;;;;AAWD,SAAS,SAAS;CAChB,IAAI,QAAQ;CACZ,IAAI;CACJ,IAAI;CACJ,IAAI,SAAS;AACb,QAAO,QAAQ,UAAU,OACvB,KAAI,WAAW,UAAU,UACvB;MAAI,gBAAgB,QAAQ,SAAS,EAAE;AACrC,cAAW,UAAU;AACrB,aAAU;EACX;;AAGL,QAAO;AACR;AACD,MAAM,UAAU,SAAO;AACrB,YAAW,QAAQ,SACjB,QAAO;CAET,IAAI;CACJ,IAAI,SAAS;AACb,MAAK,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,IAC9B,KAAI,IAAI,IACN;MAAI,gBAAgB,QAAQ,IAAI,GAAG,EAAE;AACnC,cAAW,UAAU;AACrB,aAAU;EACX;;AAGL,QAAO;AACR;AACD,SAAS,oBAAoB,mBAAmB,GAAG,kBAAkB;CACnE,IAAI;CACJ,IAAI;CACJ,IAAI;CACJ,IAAI,iBAAiB;CACrB,SAAS,kBAAkB,WAAW;EACpC,MAAM,SAAS,iBAAiB,OAAO,CAAC,gBAAgB,wBAAwB,oBAAoB,eAAe,EAAE,mBAAmB,CAAC;AACzI,gBAAc,kBAAkB,OAAO;AACvC,aAAW,YAAY,MAAM;AAC7B,aAAW,YAAY,MAAM;AAC7B,mBAAiB;AACjB,SAAO,cAAc,UAAU;CAChC;CACD,SAAS,cAAc,WAAW;EAChC,MAAM,eAAe,SAAS,UAAU;AACxC,MAAI,aACF,QAAO;EAET,MAAM,SAAS,eAAe,WAAW,YAAY;AACrD,WAAS,WAAW,OAAO;AAC3B,SAAO;CACR;AACD,QAAO,SAAS,oBAAoB;AAClC,SAAO,eAAe,OAAO,MAAM,MAAM,UAAU,CAAC;CACrD;AACF;AACD,MAAM,YAAY,SAAO;CACvB,MAAM,cAAc,WAAS,MAAM,QAAQ,CAAE;AAC7C,aAAY,gBAAgB;AAC5B,QAAO;AACR;AACD,MAAM,sBAAsB;AAC5B,MAAM,gBAAgB;AACtB,MAAM,gCAA6B,IAAI,IAAI;CAAC;CAAM;CAAQ;AAAS;AACnE,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,MAAM,qBAAqB;AAE3B,MAAM,cAAc;AACpB,MAAM,aAAa;AACnB,MAAM,WAAW,WAAS,SAAS,MAAM,IAAI,cAAc,IAAI,MAAM,IAAI,cAAc,KAAK,MAAM;AAClG,MAAM,oBAAoB,WAAS,oBAAoB,OAAO,UAAU,aAAa;AACrF,MAAM,WAAW,WAAS,QAAQ,MAAM,KAAK,OAAO,MAAM,OAAO,MAAM,CAAC;AACxE,MAAM,oBAAoB,WAAS,oBAAoB,OAAO,UAAU,SAAS;AACjF,MAAM,YAAY,WAAS,QAAQ,MAAM,IAAI,OAAO,UAAU,OAAO,MAAM,CAAC;AAC5E,MAAM,YAAY,WAAS,MAAM,SAAS,IAAI,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG,CAAC;AAC9E,MAAM,mBAAmB,WAAS,oBAAoB,KAAK,MAAM;AACjE,MAAM,eAAe,WAAS,gBAAgB,KAAK,MAAM;AACzD,MAAM,6BAA0B,IAAI,IAAI;CAAC;CAAU;CAAQ;AAAa;AACxE,MAAM,kBAAkB,WAAS,oBAAoB,OAAO,YAAY,QAAQ;AAChF,MAAM,sBAAsB,WAAS,oBAAoB,OAAO,YAAY,QAAQ;AACpF,MAAM,8BAA2B,IAAI,IAAI,CAAC,SAAS,KAAM;AACzD,MAAM,mBAAmB,WAAS,oBAAoB,OAAO,aAAa,QAAQ;AAClF,MAAM,oBAAoB,WAAS,oBAAoB,OAAO,IAAI,SAAS;AAC3E,MAAM,QAAQ,MAAM;AACpB,MAAM,sBAAsB,CAAC,OAAO,OAAO,cAAc;CACvD,MAAM,SAAS,oBAAoB,KAAK,MAAM;AAC9C,KAAI,QAAQ;AACV,MAAI,OAAO,GACT,eAAc,UAAU,WAAW,OAAO,OAAO,QAAQ,MAAM,IAAI,OAAO,GAAG;AAE/E,SAAO,UAAU,OAAO,GAAG;CAC5B;AACD,QAAO;AACR;AACD,MAAM,eAAe,WAIrB,gBAAgB,KAAK,MAAM,KAAK,mBAAmB,KAAK,MAAM;AAC9D,MAAM,UAAU,MAAM;AACtB,MAAM,WAAW,WAAS,YAAY,KAAK,MAAM;AACjD,MAAM,UAAU,WAAS,WAAW,KAAK,MAAM;AAC/C,MAAM,6BAA0B,OAAO,eAAe;CACpD,WAAW;CACX;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACD,GAAE,OAAO,aAAa,EACrB,OAAO,SACR,EAAC;AACF,MAAM,mBAAmB,MAAM;CAC7B,MAAM,SAAS,UAAU,SAAS;CAClC,MAAM,UAAU,UAAU,UAAU;CACpC,MAAM,OAAO,UAAU,OAAO;CAC9B,MAAM,aAAa,UAAU,aAAa;CAC1C,MAAM,cAAc,UAAU,cAAc;CAC5C,MAAM,eAAe,UAAU,eAAe;CAC9C,MAAM,gBAAgB,UAAU,gBAAgB;CAChD,MAAM,cAAc,UAAU,cAAc;CAC5C,MAAM,WAAW,UAAU,WAAW;CACtC,MAAM,YAAY,UAAU,YAAY;CACxC,MAAM,YAAY,UAAU,YAAY;CACxC,MAAM,SAAS,UAAU,SAAS;CAClC,MAAM,MAAM,UAAU,MAAM;CAC5B,MAAM,qBAAqB,UAAU,qBAAqB;CAC1D,MAAM,6BAA6B,UAAU,6BAA6B;CAC1E,MAAM,QAAQ,UAAU,QAAQ;CAChC,MAAM,SAAS,UAAU,SAAS;CAClC,MAAM,UAAU,UAAU,UAAU;CACpC,MAAM,UAAU,UAAU,UAAU;CACpC,MAAM,WAAW,UAAU,WAAW;CACtC,MAAM,QAAQ,UAAU,QAAQ;CAChC,MAAM,QAAQ,UAAU,QAAQ;CAChC,MAAM,OAAO,UAAU,OAAO;CAC9B,MAAM,QAAQ,UAAU,QAAQ;CAChC,MAAM,YAAY,UAAU,YAAY;CACxC,MAAM,gBAAgB,MAAM;EAAC;EAAQ;EAAW;CAAO;CACvD,MAAM,cAAc,MAAM;EAAC;EAAQ;EAAU;EAAQ;EAAW;CAAS;CACzE,MAAM,iCAAiC,MAAM;EAAC;EAAQ;EAAkB;CAAQ;CAChF,MAAM,0BAA0B,MAAM,CAAC,kBAAkB,OAAQ;CACjE,MAAM,iCAAiC,MAAM;EAAC;EAAI;EAAU;CAAkB;CAC9E,MAAM,gCAAgC,MAAM;EAAC;EAAQ;EAAU;CAAiB;CAChF,MAAM,eAAe,MAAM;EAAC;EAAU;EAAU;EAAQ;EAAe;EAAY;EAAS;EAAgB;EAAa;CAAM;CAC/H,MAAM,gBAAgB,MAAM;EAAC;EAAS;EAAU;EAAU;EAAU;CAAO;CAC3E,MAAM,gBAAgB,MAAM;EAAC;EAAU;EAAY;EAAU;EAAW;EAAU;EAAW;EAAe;EAAc;EAAc;EAAc;EAAc;EAAa;EAAO;EAAc;EAAS;CAAa;CAC5N,MAAM,WAAW,MAAM;EAAC;EAAS;EAAO;EAAU;EAAW;EAAU;EAAU;CAAU;CAC3F,MAAM,kBAAkB,MAAM;EAAC;EAAI;EAAK;CAAiB;CACzD,MAAM,YAAY,MAAM;EAAC;EAAQ;EAAS;EAAO;EAAc;EAAQ;EAAQ;EAAS;CAAS;CACjG,MAAM,wBAAwB,MAAM,CAAC,UAAU,gBAAiB;AAChE,QAAO;EACL,WAAW;EACX,WAAW;EACX,OAAO;GACL,QAAQ,CAAC,KAAM;GACf,SAAS,CAAC,UAAU,iBAAkB;GACtC,MAAM;IAAC;IAAQ;IAAI;IAAc;GAAiB;GAClD,YAAY,uBAAuB;GACnC,aAAa,CAAC,MAAO;GACrB,cAAc;IAAC;IAAQ;IAAI;IAAQ;IAAc;GAAiB;GAClE,eAAe,yBAAyB;GACxC,aAAa,gCAAgC;GAC7C,UAAU,uBAAuB;GACjC,WAAW,iBAAiB;GAC5B,WAAW,uBAAuB;GAClC,QAAQ,iBAAiB;GACzB,KAAK,yBAAyB;GAC9B,oBAAoB,CAAC,MAAO;GAC5B,4BAA4B,CAAC,WAAW,iBAAkB;GAC1D,OAAO,gCAAgC;GACvC,QAAQ,gCAAgC;GACxC,SAAS,uBAAuB;GAChC,SAAS,yBAAyB;GAClC,UAAU,uBAAuB;GACjC,OAAO,uBAAuB;GAC9B,OAAO,iBAAiB;GACxB,MAAM,uBAAuB;GAC7B,OAAO,yBAAyB;GAChC,WAAW,yBAAyB;EACrC;EACD,aAAa;GAMX,QAAQ,CAAC,EACP,QAAQ;IAAC;IAAQ;IAAU;IAAS;GAAiB,EACtD,CAAC;GAKF,WAAW,CAAC,WAAY;GAKxB,SAAS,CAAC,EACR,SAAS,CAAC,YAAa,EACxB,CAAC;GAKF,eAAe,CAAC,EACd,eAAe,WAAW,CAC3B,CAAC;GAKF,gBAAgB,CAAC,EACf,gBAAgB,WAAW,CAC5B,CAAC;GAKF,gBAAgB,CAAC,EACf,gBAAgB;IAAC;IAAQ;IAAS;IAAc;GAAe,EAChE,CAAC;GAKF,kBAAkB,CAAC,EACjB,kBAAkB,CAAC,SAAS,OAAQ,EACrC,CAAC;GAKF,KAAK,CAAC,EACJ,KAAK,CAAC,UAAU,SAAU,EAC3B,CAAC;GAKF,SAAS;IAAC;IAAS;IAAgB;IAAU;IAAQ;IAAe;IAAS;IAAgB;IAAiB;IAAc;IAAgB;IAAsB;IAAsB;IAAsB;IAAmB;IAAa;IAAa;IAAQ;IAAe;IAAY;IAAa;GAAS;GAKpT,OAAO,CAAC,EACN,OAAO;IAAC;IAAS;IAAQ;IAAQ;IAAS;GAAM,EACjD,CAAC;GAKF,OAAO,CAAC,EACN,OAAO;IAAC;IAAQ;IAAS;IAAQ;IAAQ;IAAS;GAAM,EACzD,CAAC;GAKF,WAAW,CAAC,WAAW,gBAAiB;GAKxC,cAAc,CAAC,EACb,QAAQ;IAAC;IAAW;IAAS;IAAQ;IAAQ;GAAa,EAC3D,CAAC;GAKF,mBAAmB,CAAC,EAClB,QAAQ,CAAC,GAAG,cAAc,EAAE,gBAAiB,EAC9C,CAAC;GAKF,UAAU,CAAC,EACT,UAAU,aAAa,CACxB,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,aAAa,CAC5B,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,aAAa,CAC5B,CAAC;GAKF,YAAY,CAAC,EACX,YAAY,eAAe,CAC5B,CAAC;GAKF,gBAAgB,CAAC,EACf,gBAAgB,eAAe,CAChC,CAAC;GAKF,gBAAgB,CAAC,EACf,gBAAgB,eAAe,CAChC,CAAC;GAKF,UAAU;IAAC;IAAU;IAAS;IAAY;IAAY;GAAS;GAK/D,OAAO,CAAC,EACN,OAAO,CAAC,KAAM,EACf,CAAC;GAKF,WAAW,CAAC,EACV,WAAW,CAAC,KAAM,EACnB,CAAC;GAKF,WAAW,CAAC,EACV,WAAW,CAAC,KAAM,EACnB,CAAC;GAKF,OAAO,CAAC,EACN,OAAO,CAAC,KAAM,EACf,CAAC;GAKF,KAAK,CAAC,EACJ,KAAK,CAAC,KAAM,EACb,CAAC;GAKF,KAAK,CAAC,EACJ,KAAK,CAAC,KAAM,EACb,CAAC;GAKF,OAAO,CAAC,EACN,OAAO,CAAC,KAAM,EACf,CAAC;GAKF,QAAQ,CAAC,EACP,QAAQ,CAAC,KAAM,EAChB,CAAC;GAKF,MAAM,CAAC,EACL,MAAM,CAAC,KAAM,EACd,CAAC;GAKF,YAAY;IAAC;IAAW;IAAa;GAAW;GAKhD,GAAG,CAAC,EACF,GAAG;IAAC;IAAQ;IAAW;GAAiB,EACzC,CAAC;GAMF,OAAO,CAAC,EACN,OAAO,gCAAgC,CACxC,CAAC;GAKF,kBAAkB,CAAC,EACjB,MAAM;IAAC;IAAO;IAAe;IAAO;GAAc,EACnD,CAAC;GAKF,aAAa,CAAC,EACZ,MAAM;IAAC;IAAQ;IAAgB;GAAS,EACzC,CAAC;GAKF,MAAM,CAAC,EACL,MAAM;IAAC;IAAK;IAAQ;IAAW;IAAQ;GAAiB,EACzD,CAAC;GAKF,MAAM,CAAC,EACL,MAAM,iBAAiB,CACxB,CAAC;GAKF,QAAQ,CAAC,EACP,QAAQ,iBAAiB,CAC1B,CAAC;GAKF,OAAO,CAAC,EACN,OAAO;IAAC;IAAS;IAAQ;IAAQ;IAAW;GAAiB,EAC9D,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,CAAC,KAAM,EACrB,CAAC;GAKF,iBAAiB,CAAC,EAChB,KAAK;IAAC;IAAQ,EACZ,MAAM;KAAC;KAAQ;KAAW;IAAiB,EAC5C;IAAE;GAAiB,EACrB,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,+BAA+B,CAC7C,CAAC;GAKF,WAAW,CAAC,EACV,WAAW,+BAA+B,CAC3C,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,CAAC,KAAM,EACrB,CAAC;GAKF,iBAAiB,CAAC,EAChB,KAAK;IAAC;IAAQ,EACZ,MAAM,CAAC,WAAW,gBAAiB,EACpC;IAAE;GAAiB,EACrB,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,+BAA+B,CAC7C,CAAC;GAKF,WAAW,CAAC,EACV,WAAW,+BAA+B,CAC3C,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa;IAAC;IAAO;IAAO;IAAS;IAAa;GAAY,EAC/D,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa;IAAC;IAAQ;IAAO;IAAO;IAAM;GAAiB,EAC5D,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa;IAAC;IAAQ;IAAO;IAAO;IAAM;GAAiB,EAC5D,CAAC;GAKF,KAAK,CAAC,EACJ,KAAK,CAAC,GAAI,EACX,CAAC;GAKF,SAAS,CAAC,EACR,SAAS,CAAC,GAAI,EACf,CAAC;GAKF,SAAS,CAAC,EACR,SAAS,CAAC,GAAI,EACf,CAAC;GAKF,mBAAmB,CAAC,EAClB,SAAS,CAAC,UAAU,GAAG,UAAU,AAAC,EACnC,CAAC;GAKF,iBAAiB,CAAC,EAChB,iBAAiB;IAAC;IAAS;IAAO;IAAU;GAAU,EACvD,CAAC;GAKF,gBAAgB,CAAC,EACf,gBAAgB;IAAC;IAAQ;IAAS;IAAO;IAAU;GAAU,EAC9D,CAAC;GAKF,iBAAiB,CAAC,EAChB,SAAS;IAAC;IAAU,GAAG,UAAU;IAAE;GAAW,EAC/C,CAAC;GAKF,eAAe,CAAC,EACd,OAAO;IAAC;IAAS;IAAO;IAAU;IAAY;GAAU,EACzD,CAAC;GAKF,cAAc,CAAC,EACb,MAAM;IAAC;IAAQ;IAAS;IAAO;IAAU;IAAW;GAAW,EAChE,CAAC;GAKF,iBAAiB,CAAC,EAChB,iBAAiB,CAAC,GAAG,UAAU,EAAE,UAAW,EAC7C,CAAC;GAKF,eAAe,CAAC,EACd,eAAe;IAAC;IAAS;IAAO;IAAU;IAAY;GAAU,EACjE,CAAC;GAKF,cAAc,CAAC,EACb,cAAc;IAAC;IAAQ;IAAS;IAAO;IAAU;GAAU,EAC5D,CAAC;GAMF,GAAG,CAAC,EACF,GAAG,CAAC,OAAQ,EACb,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,OAAQ,EACd,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,OAAQ,EACd,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,OAAQ,EACd,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,OAAQ,EACd,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,OAAQ,EACd,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,OAAQ,EACd,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,OAAQ,EACd,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,OAAQ,EACd,CAAC;GAKF,GAAG,CAAC,EACF,GAAG,CAAC,MAAO,EACZ,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,MAAO,EACb,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,MAAO,EACb,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,MAAO,EACb,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,MAAO,EACb,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,MAAO,EACb,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,MAAO,EACb,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,MAAO,EACb,CAAC;GAKF,IAAI,CAAC,EACH,IAAI,CAAC,MAAO,EACb,CAAC;GAKF,WAAW,CAAC,EACV,WAAW,CAAC,KAAM,EACnB,CAAC;GAKF,mBAAmB,CAAC,iBAAkB;GAKtC,WAAW,CAAC,EACV,WAAW,CAAC,KAAM,EACnB,CAAC;GAKF,mBAAmB,CAAC,iBAAkB;GAMtC,GAAG,CAAC,EACF,GAAG;IAAC;IAAQ;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAkB;GAAQ,EACjF,CAAC;GAKF,SAAS,CAAC,EACR,SAAS;IAAC;IAAkB;IAAS;IAAO;IAAO;GAAM,EAC1D,CAAC;GAKF,SAAS,CAAC,EACR,SAAS;IAAC;IAAkB;IAAS;IAAQ;IAAQ;IAAO;IAAO;IAAO;IAAS,EACjF,QAAQ,CAAC,YAAa,EACvB;IAAE;GAAa,EACjB,CAAC;GAKF,GAAG,CAAC,EACF,GAAG;IAAC;IAAkB;IAAS;IAAQ;IAAO;IAAO;IAAO;IAAO;IAAO;GAAM,EACjF,CAAC;GAKF,SAAS,CAAC,EACR,SAAS;IAAC;IAAkB;IAAS;IAAO;IAAO;IAAO;IAAO;IAAO;GAAM,EAC/E,CAAC;GAKF,SAAS,CAAC,EACR,SAAS;IAAC;IAAkB;IAAS;IAAO;IAAO;IAAO;IAAO;IAAO;GAAM,EAC/E,CAAC;GAKF,MAAM,CAAC,EACL,MAAM;IAAC;IAAkB;IAAS;IAAQ;IAAO;IAAO;GAAM,EAC/D,CAAC;GAMF,aAAa,CAAC,EACZ,MAAM;IAAC;IAAQ;IAAc;GAAkB,EAChD,CAAC;GAKF,kBAAkB,CAAC,eAAe,sBAAuB;GAKzD,cAAc,CAAC,UAAU,YAAa;GAKtC,eAAe,CAAC,EACd,MAAM;IAAC;IAAQ;IAAc;IAAS;IAAU;IAAU;IAAY;IAAQ;IAAa;IAAS;GAAkB,EACvH,CAAC;GAKF,eAAe,CAAC,EACd,MAAM,CAAC,KAAM,EACd,CAAC;GAKF,cAAc,CAAC,aAAc;GAK7B,eAAe,CAAC,SAAU;GAK1B,oBAAoB,CAAC,cAAe;GAKpC,cAAc,CAAC,eAAe,eAAgB;GAK9C,eAAe,CAAC,qBAAqB,cAAe;GAKpD,gBAAgB,CAAC,sBAAsB,mBAAoB;GAK3D,UAAU,CAAC,EACT,UAAU;IAAC;IAAW;IAAS;IAAU;IAAQ;IAAS;IAAU;GAAiB,EACtF,CAAC;GAKF,cAAc,CAAC,EACb,cAAc;IAAC;IAAQ;IAAU;GAAkB,EACpD,CAAC;GAKF,SAAS,CAAC,EACR,SAAS;IAAC;IAAQ;IAAS;IAAQ;IAAU;IAAW;IAAS;IAAU;GAAiB,EAC7F,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,QAAQ,gBAAiB,EACzC,CAAC;GAKF,mBAAmB,CAAC,EAClB,MAAM;IAAC;IAAQ;IAAQ;IAAW;GAAiB,EACpD,CAAC;GAKF,uBAAuB,CAAC,EACtB,MAAM,CAAC,UAAU,SAAU,EAC5B,CAAC;GAMF,qBAAqB,CAAC,EACpB,aAAa,CAAC,MAAO,EACtB,CAAC;GAKF,uBAAuB,CAAC,EACtB,uBAAuB,CAAC,OAAQ,EACjC,CAAC;GAKF,kBAAkB,CAAC,EACjB,MAAM;IAAC;IAAQ;IAAU;IAAS;IAAW;IAAS;GAAM,EAC7D,CAAC;GAKF,cAAc,CAAC,EACb,MAAM,CAAC,MAAO,EACf,CAAC;GAKF,gBAAgB,CAAC,EACf,gBAAgB,CAAC,OAAQ,EAC1B,CAAC;GAKF,mBAAmB;IAAC;IAAa;IAAY;IAAgB;GAAe;GAK5E,yBAAyB,CAAC,EACxB,YAAY,CAAC,GAAG,eAAe,EAAE,MAAO,EACzC,CAAC;GAKF,6BAA6B,CAAC,EAC5B,YAAY;IAAC;IAAQ;IAAa;IAAU;GAAkB,EAC/D,CAAC;GAKF,oBAAoB,CAAC,EACnB,oBAAoB;IAAC;IAAQ;IAAU;GAAiB,EACzD,CAAC;GAKF,yBAAyB,CAAC,EACxB,YAAY,CAAC,MAAO,EACrB,CAAC;GAKF,kBAAkB;IAAC;IAAa;IAAa;IAAc;GAAc;GAKzE,iBAAiB;IAAC;IAAY;IAAiB;GAAY;GAK3D,aAAa,CAAC,EACZ,MAAM;IAAC;IAAQ;IAAU;IAAW;GAAS,EAC9C,CAAC;GAKF,QAAQ,CAAC,EACP,QAAQ,yBAAyB,CAClC,CAAC;GAKF,kBAAkB,CAAC,EACjB,OAAO;IAAC;IAAY;IAAO;IAAU;IAAU;IAAY;IAAe;IAAO;IAAS;GAAiB,EAC5G,CAAC;GAKF,YAAY,CAAC,EACX,YAAY;IAAC;IAAU;IAAU;IAAO;IAAY;IAAY;GAAe,EAChF,CAAC;GAKF,OAAO,CAAC,EACN,OAAO;IAAC;IAAU;IAAS;IAAO;GAAO,EAC1C,CAAC;GAKF,SAAS,CAAC,EACR,SAAS;IAAC;IAAQ;IAAU;GAAO,EACpC,CAAC;GAKF,SAAS,CAAC,EACR,SAAS,CAAC,QAAQ,gBAAiB,EACpC,CAAC;GAMF,iBAAiB,CAAC,EAChB,IAAI;IAAC;IAAS;IAAS;GAAS,EACjC,CAAC;GAKF,WAAW,CAAC,EACV,WAAW;IAAC;IAAU;IAAW;IAAW;GAAO,EACpD,CAAC;GAMF,cAAc,CAAC,EACb,cAAc,CAAC,OAAQ,EACxB,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa;IAAC;IAAU;IAAW;GAAU,EAC9C,CAAC;GAKF,eAAe,CAAC,EACd,IAAI,CAAC,GAAG,cAAc,EAAE,mBAAoB,EAC7C,CAAC;GAKF,aAAa,CAAC,EACZ,IAAI,CAAC,aAAa,EAChB,QAAQ;IAAC;IAAI;IAAK;IAAK;IAAS;GAAQ,EACzC,CAAC,EACH,CAAC;GAKF,WAAW,CAAC,EACV,IAAI;IAAC;IAAQ;IAAS;IAAW;GAAgB,EAClD,CAAC;GAKF,YAAY,CAAC,EACX,IAAI;IAAC;IAAQ,EACX,eAAe;KAAC;KAAK;KAAM;KAAK;KAAM;KAAK;KAAM;KAAK;IAAK,EAC5D;IAAE;GAAiB,EACrB,CAAC;GAKF,YAAY,CAAC,EACX,IAAI,CAAC,MAAO,EACb,CAAC;GAKF,qBAAqB,CAAC,EACpB,MAAM,CAAC,0BAA2B,EACnC,CAAC;GAKF,oBAAoB,CAAC,EACnB,KAAK,CAAC,0BAA2B,EAClC,CAAC;GAKF,mBAAmB,CAAC,EAClB,IAAI,CAAC,0BAA2B,EACjC,CAAC;GAKF,iBAAiB,CAAC,EAChB,MAAM,CAAC,kBAAmB,EAC3B,CAAC;GAKF,gBAAgB,CAAC,EACf,KAAK,CAAC,kBAAmB,EAC1B,CAAC;GAKF,eAAe,CAAC,EACd,IAAI,CAAC,kBAAmB,EACzB,CAAC;GAMF,SAAS,CAAC,EACR,SAAS,CAAC,YAAa,EACxB,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,CAAC,YAAa,EAC5B,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,CAAC,YAAa,EAC5B,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,CAAC,YAAa,EAC5B,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,CAAC,YAAa,EAC5B,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,CAAC,YAAa,EAC5B,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,CAAC,YAAa,EAC5B,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,YAAa,EAC7B,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,YAAa,EAC7B,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,YAAa,EAC7B,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,YAAa,EAC7B,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,YAAa,EAC7B,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,YAAa,EAC7B,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,YAAa,EAC7B,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,YAAa,EAC7B,CAAC;GAKF,YAAY,CAAC,EACX,QAAQ,CAAC,WAAY,EACtB,CAAC;GAKF,cAAc,CAAC,EACb,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,cAAc,CAAC,EACb,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,cAAc,CAAC,EACb,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,cAAc,CAAC,EACb,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,cAAc,CAAC,EACb,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,cAAc,CAAC,EACb,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,cAAc,CAAC,EACb,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,cAAc,CAAC,EACb,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,kBAAkB,CAAC,EACjB,kBAAkB,CAAC,OAAQ,EAC5B,CAAC;GAKF,gBAAgB,CAAC,EACf,QAAQ,CAAC,GAAG,eAAe,EAAE,QAAS,EACvC,CAAC;GAKF,YAAY,CAAC,EACX,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,oBAAoB,CAAC,kBAAmB;GAKxC,YAAY,CAAC,EACX,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,oBAAoB,CAAC,kBAAmB;GAKxC,kBAAkB,CAAC,EACjB,kBAAkB,CAAC,OAAQ,EAC5B,CAAC;GAKF,gBAAgB,CAAC,EACf,QAAQ,eAAe,CACxB,CAAC;GAKF,gBAAgB,CAAC,EACf,QAAQ,CAAC,WAAY,EACtB,CAAC;GAKF,kBAAkB,CAAC,EACjB,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,kBAAkB,CAAC,EACjB,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,kBAAkB,CAAC,EACjB,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,kBAAkB,CAAC,EACjB,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,kBAAkB,CAAC,EACjB,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,kBAAkB,CAAC,EACjB,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,kBAAkB,CAAC,EACjB,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,kBAAkB,CAAC,EACjB,YAAY,CAAC,WAAY,EAC1B,CAAC;GAKF,gBAAgB,CAAC,EACf,QAAQ,CAAC,WAAY,EACtB,CAAC;GAKF,iBAAiB,CAAC,EAChB,SAAS,CAAC,IAAI,GAAG,eAAe,AAAC,EAClC,CAAC;GAKF,kBAAkB,CAAC,EACjB,kBAAkB,CAAC,UAAU,gBAAiB,EAC/C,CAAC;GAKF,aAAa,CAAC,EACZ,SAAS,CAAC,UAAU,iBAAkB,EACvC,CAAC;GAKF,iBAAiB,CAAC,EAChB,SAAS,CAAC,MAAO,EAClB,CAAC;GAKF,UAAU,CAAC,EACT,MAAM,gCAAgC,CACvC,CAAC;GAKF,gBAAgB,CAAC,YAAa;GAK9B,cAAc,CAAC,EACb,MAAM,CAAC,MAAO,EACf,CAAC;GAKF,gBAAgB,CAAC,EACf,gBAAgB,CAAC,OAAQ,EAC1B,CAAC;GAKF,iBAAiB,CAAC,EAChB,eAAe,CAAC,UAAU,iBAAkB,EAC7C,CAAC;GAKF,qBAAqB,CAAC,EACpB,eAAe,CAAC,MAAO,EACxB,CAAC;GAMF,QAAQ,CAAC,EACP,QAAQ;IAAC;IAAI;IAAS;IAAQ;IAAc;GAAkB,EAC/D,CAAC;GAKF,gBAAgB,CAAC,EACf,QAAQ,CAAC,KAAM,EAChB,CAAC;GAKF,SAAS,CAAC,EACR,SAAS,CAAC,OAAQ,EACnB,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa;IAAC,GAAG,eAAe;IAAE;IAAgB;GAAc,EACjE,CAAC;GAKF,YAAY,CAAC,EACX,YAAY,eAAe,CAC5B,CAAC;GAOF,QAAQ,CAAC,EACP,QAAQ,CAAC,IAAI,MAAO,EACrB,CAAC;GAKF,MAAM,CAAC,EACL,MAAM,CAAC,IAAK,EACb,CAAC;GAKF,YAAY,CAAC,EACX,YAAY,CAAC,UAAW,EACzB,CAAC;GAKF,UAAU,CAAC,EACT,UAAU,CAAC,QAAS,EACrB,CAAC;GAKF,eAAe,CAAC,EACd,eAAe;IAAC;IAAI;IAAQ;IAAc;GAAiB,EAC5D,CAAC;GAKF,WAAW,CAAC,EACV,WAAW,CAAC,SAAU,EACvB,CAAC;GAKF,cAAc,CAAC,EACb,cAAc,CAAC,SAAU,EAC1B,CAAC;GAKF,QAAQ,CAAC,EACP,QAAQ,CAAC,MAAO,EACjB,CAAC;GAKF,UAAU,CAAC,EACT,UAAU,CAAC,QAAS,EACrB,CAAC;GAKF,OAAO,CAAC,EACN,OAAO,CAAC,KAAM,EACf,CAAC;GAMF,mBAAmB,CAAC,EAClB,mBAAmB,CAAC,IAAI,MAAO,EAChC,CAAC;GAKF,iBAAiB,CAAC,EAChB,iBAAiB,CAAC,IAAK,EACxB,CAAC;GAKF,uBAAuB,CAAC,EACtB,uBAAuB,CAAC,UAAW,EACpC,CAAC;GAKF,qBAAqB,CAAC,EACpB,qBAAqB,CAAC,QAAS,EAChC,CAAC;GAKF,sBAAsB,CAAC,EACrB,sBAAsB,CAAC,SAAU,EAClC,CAAC;GAKF,uBAAuB,CAAC,EACtB,uBAAuB,CAAC,SAAU,EACnC,CAAC;GAKF,mBAAmB,CAAC,EAClB,mBAAmB,CAAC,MAAO,EAC5B,CAAC;GAKF,oBAAoB,CAAC,EACnB,oBAAoB,CAAC,OAAQ,EAC9B,CAAC;GAKF,qBAAqB,CAAC,EACpB,qBAAqB,CAAC,QAAS,EAChC,CAAC;GAKF,kBAAkB,CAAC,EACjB,kBAAkB,CAAC,KAAM,EAC1B,CAAC;GAMF,mBAAmB,CAAC,EAClB,QAAQ,CAAC,YAAY,UAAW,EACjC,CAAC;GAKF,kBAAkB,CAAC,EACjB,kBAAkB,CAAC,aAAc,EAClC,CAAC;GAKF,oBAAoB,CAAC,EACnB,oBAAoB,CAAC,aAAc,EACpC,CAAC;GAKF,oBAAoB,CAAC,EACnB,oBAAoB,CAAC,aAAc,EACpC,CAAC;GAKF,gBAAgB,CAAC,EACf,OAAO,CAAC,QAAQ,OAAQ,EACzB,CAAC;GAKF,SAAS,CAAC,EACR,SAAS,CAAC,OAAO,QAAS,EAC3B,CAAC;GAMF,YAAY,CAAC,EACX,YAAY;IAAC;IAAQ;IAAO;IAAI;IAAU;IAAW;IAAU;IAAa;GAAiB,EAC9F,CAAC;GAKF,UAAU,CAAC,EACT,UAAU,uBAAuB,CAClC,CAAC;GAKF,MAAM,CAAC,EACL,MAAM;IAAC;IAAU;IAAM;IAAO;IAAU;GAAiB,EAC1D,CAAC;GAKF,OAAO,CAAC,EACN,OAAO,uBAAuB,CAC/B,CAAC;GAKF,SAAS,CAAC,EACR,SAAS;IAAC;IAAQ;IAAQ;IAAQ;IAAS;IAAU;GAAiB,EACvE,CAAC;GAMF,WAAW,CAAC,EACV,WAAW;IAAC;IAAI;IAAO;GAAO,EAC/B,CAAC;GAKF,OAAO,CAAC,EACN,OAAO,CAAC,KAAM,EACf,CAAC;GAKF,WAAW,CAAC,EACV,WAAW,CAAC,KAAM,EACnB,CAAC;GAKF,WAAW,CAAC,EACV,WAAW,CAAC,KAAM,EACnB,CAAC;GAKF,QAAQ,CAAC,EACP,QAAQ,CAAC,WAAW,gBAAiB,EACtC,CAAC;GAKF,eAAe,CAAC,EACd,eAAe,CAAC,SAAU,EAC3B,CAAC;GAKF,eAAe,CAAC,EACd,eAAe,CAAC,SAAU,EAC3B,CAAC;GAKF,UAAU,CAAC,EACT,UAAU,CAAC,IAAK,EACjB,CAAC;GAKF,UAAU,CAAC,EACT,UAAU,CAAC,IAAK,EACjB,CAAC;GAKF,oBAAoB,CAAC,EACnB,QAAQ;IAAC;IAAU;IAAO;IAAa;IAAS;IAAgB;IAAU;IAAe;IAAQ;IAAY;GAAiB,EAC/H,CAAC;GAMF,QAAQ,CAAC,EACP,QAAQ,CAAC,QAAQ,MAAO,EACzB,CAAC;GAKF,YAAY,CAAC,EACX,YAAY,CAAC,QAAQ,MAAO,EAC7B,CAAC;GAKF,QAAQ,CAAC,EACP,QAAQ;IAAC;IAAQ;IAAW;IAAW;IAAQ;IAAQ;IAAQ;IAAQ;IAAe;IAAQ;IAAgB;IAAY;IAAQ;IAAa;IAAiB;IAAS;IAAQ;IAAW;IAAQ;IAAY;IAAc;IAAc;IAAc;IAAY;IAAY;IAAY;IAAY;IAAa;IAAa;IAAa;IAAa;IAAa;IAAa;IAAe;IAAe;IAAW;IAAY;GAAiB,EAC/b,CAAC;GAKF,eAAe,CAAC,EACd,OAAO,CAAC,MAAO,EAChB,CAAC;GAKF,kBAAkB,CAAC,EACjB,kBAAkB,CAAC,QAAQ,MAAO,EACnC,CAAC;GAKF,QAAQ,CAAC,EACP,QAAQ;IAAC;IAAQ;IAAK;IAAK;GAAG,EAC/B,CAAC;GAKF,mBAAmB,CAAC,EAClB,QAAQ,CAAC,QAAQ,QAAS,EAC3B,CAAC;GAKF,YAAY,CAAC,EACX,YAAY,yBAAyB,CACtC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,YAAY,CAAC,EACX,YAAY,yBAAyB,CACtC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,aAAa,CAAC,EACZ,aAAa,yBAAyB,CACvC,CAAC;GAKF,cAAc,CAAC,EACb,MAAM;IAAC;IAAS;IAAO;IAAU;GAAa,EAC/C,CAAC;GAKF,aAAa,CAAC,EACZ,MAAM,CAAC,UAAU,QAAS,EAC3B,CAAC;GAKF,aAAa,CAAC,EACZ,MAAM;IAAC;IAAQ;IAAK;IAAK;GAAO,EACjC,CAAC;GAKF,mBAAmB,CAAC,EAClB,MAAM,CAAC,aAAa,WAAY,EACjC,CAAC;GAKF,OAAO,CAAC,EACN,OAAO;IAAC;IAAQ;IAAQ;GAAe,EACxC,CAAC;GAKF,WAAW,CAAC,EACV,aAAa;IAAC;IAAK;IAAQ;GAAQ,EACpC,CAAC;GAKF,WAAW,CAAC,EACV,aAAa;IAAC;IAAK;IAAM;GAAO,EACjC,CAAC;GAKF,YAAY,CAAC,kBAAmB;GAKhC,QAAQ,CAAC,EACP,QAAQ;IAAC;IAAQ;IAAQ;IAAO;GAAO,EACxC,CAAC;GAKF,eAAe,CAAC,EACd,eAAe;IAAC;IAAQ;IAAU;IAAY;IAAa;GAAiB,EAC7E,CAAC;GAMF,MAAM,CAAC,EACL,MAAM,CAAC,QAAQ,MAAO,EACvB,CAAC;GAKF,YAAY,CAAC,EACX,QAAQ;IAAC;IAAU;IAAmB;GAAkB,EACzD,CAAC;GAKF,QAAQ,CAAC,EACP,QAAQ,CAAC,QAAQ,MAAO,EACzB,CAAC;GAMF,IAAI,CAAC,WAAW,aAAc;GAK9B,uBAAuB,CAAC,EACtB,uBAAuB,CAAC,QAAQ,MAAO,EACxC,CAAC;EACH;EACD,wBAAwB;GACtB,UAAU,CAAC,cAAc,YAAa;GACtC,YAAY,CAAC,gBAAgB,cAAe;GAC5C,OAAO;IAAC;IAAW;IAAW;IAAS;IAAO;IAAO;IAAS;IAAU;GAAO;GAC/E,WAAW,CAAC,SAAS,MAAO;GAC5B,WAAW,CAAC,OAAO,QAAS;GAC5B,MAAM;IAAC;IAAS;IAAQ;GAAS;GACjC,KAAK,CAAC,SAAS,OAAQ;GACvB,GAAG;IAAC;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;GAAK;GACnD,IAAI,CAAC,MAAM,IAAK;GAChB,IAAI,CAAC,MAAM,IAAK;GAChB,GAAG;IAAC;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;GAAK;GACnD,IAAI,CAAC,MAAM,IAAK;GAChB,IAAI,CAAC,MAAM,IAAK;GAChB,MAAM,CAAC,KAAK,GAAI;GAChB,aAAa,CAAC,SAAU;GACxB,cAAc;IAAC;IAAe;IAAoB;IAAc;IAAe;GAAe;GAC9F,eAAe,CAAC,YAAa;GAC7B,oBAAoB,CAAC,YAAa;GAClC,cAAc,CAAC,YAAa;GAC5B,eAAe,CAAC,YAAa;GAC7B,gBAAgB,CAAC,YAAa;GAC9B,cAAc,CAAC,WAAW,UAAW;GACrC,SAAS;IAAC;IAAa;IAAa;IAAa;IAAa;IAAa;IAAa;IAAc;IAAc;IAAc;IAAc;IAAc;IAAc;IAAc;GAAa;GACvM,aAAa,CAAC,cAAc,YAAa;GACzC,aAAa,CAAC,cAAc,YAAa;GACzC,aAAa,CAAC,cAAc,YAAa;GACzC,aAAa,CAAC,cAAc,YAAa;GACzC,aAAa,CAAC,cAAc,YAAa;GACzC,aAAa,CAAC,cAAc,YAAa;GACzC,kBAAkB,CAAC,oBAAoB,kBAAmB;GAC1D,YAAY;IAAC;IAAc;IAAc;IAAc;IAAc;IAAc;GAAa;GAChG,cAAc,CAAC,cAAc,YAAa;GAC1C,cAAc,CAAC,cAAc,YAAa;GAC1C,gBAAgB;IAAC;IAAkB;IAAkB;IAAkB;IAAkB;IAAkB;GAAiB;GAC5H,kBAAkB,CAAC,kBAAkB,gBAAiB;GACtD,kBAAkB,CAAC,kBAAkB,gBAAiB;GACtD,YAAY;IAAC;IAAa;IAAa;IAAa;IAAa;IAAa;IAAa;IAAa;GAAY;GACpH,aAAa,CAAC,aAAa,WAAY;GACvC,aAAa,CAAC,aAAa,WAAY;GACvC,YAAY;IAAC;IAAa;IAAa;IAAa;IAAa;IAAa;IAAa;IAAa;GAAY;GACpH,aAAa,CAAC,aAAa,WAAY;GACvC,aAAa,CAAC,aAAa,WAAY;GACvC,OAAO;IAAC;IAAW;IAAW;GAAW;GACzC,WAAW,CAAC,OAAQ;GACpB,WAAW,CAAC,OAAQ;GACpB,YAAY,CAAC,OAAQ;EACtB;EACD,gCAAgC,EAC9B,aAAa,CAAC,SAAU,EACzB;CACF;AACF;;;;;AAMD,MAAM,eAAe,CAAC,YAAY,EAChC,WACA,QACA,WACA,4BACA,SAAS,CAAE,GACX,WAAW,CAAE,GACd,KAAK;AACJ,kBAAiB,YAAY,aAAa,UAAU;AACpD,kBAAiB,YAAY,UAAU,OAAO;AAC9C,kBAAiB,YAAY,aAAa,UAAU;AACpD,kBAAiB,YAAY,8BAA8B,2BAA2B;AACtF,MAAK,MAAM,aAAa,SACtB,0BAAyB,WAAW,YAAY,SAAS,WAAW;AAEtE,MAAK,MAAM,OAAO,OAChB,uBAAsB,WAAW,MAAM,OAAO,KAAK;AAErD,QAAO;AACR;AACD,MAAM,mBAAmB,CAAC,YAAY,aAAa,kBAAkB;AACnE,KAAI,yBACF,YAAW,eAAe;AAE7B;AACD,MAAM,2BAA2B,CAAC,YAAY,mBAAmB;AAC/D,KAAI,eACF,MAAK,MAAM,OAAO,eAChB,kBAAiB,YAAY,KAAK,eAAe,KAAK;AAG3D;AACD,MAAM,wBAAwB,CAAC,YAAY,gBAAgB;AACzD,KAAI,YACF,MAAK,MAAM,OAAO,aAAa;EAC7B,MAAM,aAAa,YAAY;AAC/B,MAAI,sBACF,YAAW,OAAO,CAAC,WAAW,QAAQ,CAAE,GAAE,OAAO,WAAW;CAE/D;AAEJ;AACD,MAAM,sBAAsB,CAAC,iBAAiB,GAAG,wBAAwB,oBAAoB,aAAa,oBAAoB,kBAAkB,iBAAiB,GAAG,aAAa,GAAG,oBAAoB,MAAM,aAAa,kBAAkB,EAAE,gBAAgB,EAAE,GAAG,aAAa;AACjR,MAAM,0BAAuB,oBAAoB,iBAAiB"}