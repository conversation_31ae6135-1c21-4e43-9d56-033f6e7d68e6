{
	"compilerOptions": {
		"target": "es2021" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,
		"lib": [
			"es2021"
		] /* Specify a set of bundled library declaration files that describe the target runtime environment. */,
		"jsx": "react-jsx" /* Specify what JSX code is generated. */,
		"module": "ESNext" /* Specify what module code is generated. */,
		"moduleResolution": "Bundler" /* Specify how TypeScript looks up a file from a given module specifier. */,
		"types": [
			"@cloudflare/workers-types"
		] /* Specify type package names to be included without being referenced in a source file. */,
		"resolveJsonModule": true /* Enable importing .json files */,
		"allowJs": true /* Allow JavaScript files to be a part of your program. Use the `checkJS` option to get errors from these files. */,
		"noEmit": true /* Disable emitting files from a compilation. */,
		"isolatedModules": true /* Ensure that each file can be safely transpiled without relying on other imports. */,
		"allowSyntheticDefaultImports": true /* Allow 'import x from y' when a module doesn't have a default export. */,
		"strict": true /* Enable all strict type-checking options. */,
		"noUncheckedIndexedAccess": true,
		"skipLibCheck": true /* Skip type checking all .d.ts files. */
	}
}
