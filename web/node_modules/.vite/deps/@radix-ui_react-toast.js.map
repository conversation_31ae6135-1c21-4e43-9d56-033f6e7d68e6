{"version": 3, "file": "@radix-ui_react-toast.js", "names": ["Provider", "PROVIDER_NAME", "createCollectionScope", "useCollection", "Node", "handleAndDispatchCustomEvent", "React", "React", "useLayoutEffect", "Fragment"], "sources": ["../../@radix-ui/react-toast/node_modules/@radix-ui/primitive/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-context/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-collection/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-primitive/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-dismissable-layer/node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-portal/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-presence/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "../../@radix-ui/react-toast/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "../../@radix-ui/react-toast/dist/index.mjs"], "sourcesContent": ["// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/collection-legacy.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createCollection(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: /* @__PURE__ */ new Map() }\n  );\n  const CollectionProvider = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef(null);\n    const itemMap = React.useRef(/* @__PURE__ */ new Map()).current;\n    return /* @__PURE__ */ jsx(CollectionProviderImpl, { scope, itemMap, collectionRef: ref, children });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...itemData });\n        return () => void context.itemMap.delete(ref);\n      });\n      return /* @__PURE__ */ jsx(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useCollection(scope) {\n    const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope\n  ];\n}\n\n// src/collection.tsx\nimport React2 from \"react\";\nimport { createContextScope as createContextScope2 } from \"@radix-ui/react-context\";\nimport { useComposedRefs as useComposedRefs2 } from \"@radix-ui/react-compose-refs\";\nimport { createSlot as createSlot2 } from \"@radix-ui/react-slot\";\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n  #keys;\n  constructor(entries) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n  set(key, value) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n  insert(index, key, value) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n    if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n    const size = this.size + (has ? 0 : 1);\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n    const keys = [...this.#keys];\n    let nextValue;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i];\n        if (keys[i] === key) {\n          nextKey = keys[i + 1];\n        }\n        if (has) {\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1];\n        const currentValue = nextValue;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n  with(index, key, value) {\n    const copy = new _OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n  before(key) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n  after(key) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n  first() {\n    return this.entryAt(0);\n  }\n  last() {\n    return this.entryAt(-1);\n  }\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n  delete(key) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n  deleteAt(index) {\n    const key = this.keyAt(index);\n    if (key !== void 0) {\n      return this.delete(key);\n    }\n    return false;\n  }\n  at(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return this.get(key);\n    }\n  }\n  entryAt(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return [key, this.get(key)];\n    }\n  }\n  indexOf(key) {\n    return this.#keys.indexOf(key);\n  }\n  keyAt(index) {\n    return at(this.#keys, index);\n  }\n  from(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n  keyFrom(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n  find(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return void 0;\n  }\n  findIndex(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n  filter(predicate, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  map(callbackfn, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  reduce(...args) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0);\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n  reduceRight(...args) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1);\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index);\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n  toSorted(compareFn) {\n    const entries = [...this.entries()].sort(compareFn);\n    return new _OrderedDict(entries);\n  }\n  toReversed() {\n    const reversed = new _OrderedDict();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n  toSpliced(...args) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new _OrderedDict(entries);\n  }\n  slice(start, end) {\n    const result = new _OrderedDict();\n    let stop = this.size - 1;\n    if (start === void 0) {\n      return result;\n    }\n    if (start < 0) {\n      start = start + this.size;\n    }\n    if (end !== void 0 && end > 0) {\n      stop = end - 1;\n    }\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      result.set(key, element);\n    }\n    return result;\n  }\n  every(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n  some(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n};\nfunction at(array, index) {\n  if (\"at\" in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n\n// src/collection.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction createCollection2(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope2(PROVIDER_NAME);\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0\n    }\n  );\n  const CollectionProvider = ({ state, ...props }) => {\n    return state ? /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state }) : /* @__PURE__ */ jsx2(CollectionInit, { ...props });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const CollectionInit = (props) => {\n    const state = useInitCollection();\n    return /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state });\n  };\n  CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n  const CollectionProviderImpl = (props) => {\n    const { scope, children, state } = props;\n    const ref = React2.useRef(null);\n    const [collectionElement, setCollectionElement] = React2.useState(\n      null\n    );\n    const composeRefs = useComposedRefs2(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n    React2.useEffect(() => {\n      if (!collectionElement) return;\n      const observer = getChildListObserver(() => {\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n    return /* @__PURE__ */ jsx2(\n      CollectionContextProvider,\n      {\n        scope,\n        itemMap,\n        setItemMap,\n        collectionRef: composeRefs,\n        collectionRefObject: ref,\n        collectionElement,\n        children\n      }\n    );\n  };\n  CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot2(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs2(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx2(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot2(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React2.useRef(null);\n      const [element, setElement] = React2.useState(null);\n      const composedRefs = useComposedRefs2(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      const { setItemMap } = context;\n      const itemDataRef = React2.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n      React2.useEffect(() => {\n        const itemData2 = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n          if (!map.has(element)) {\n            map.set(element, { ...itemData2, element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n          return map.set(element, { ...itemData2, element }).toSorted(sortByDocumentPosition);\n        });\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n      return /* @__PURE__ */ jsx2(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useInitCollection() {\n    return React2.useState(new OrderedDict());\n  }\n  function useCollection(scope) {\n    const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n    return itemMap;\n  }\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection\n  };\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions\n  ];\n}\nfunction shallowEqual(a, b) {\n  if (a === b) return true;\n  if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\nfunction isElementPreceding(a, b) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n  return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === \"childList\") {\n        callback();\n        return;\n      }\n    }\n  });\n  return observer;\n}\nexport {\n  createCollection,\n  createCollection2 as unstable_createCollection\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/dismissable-layer.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = React.createContext({\n  layers: /* @__PURE__ */ new Set(),\n  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n  branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = \"none\";\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n          pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n          ...props.style\n        },\n        onFocusCapture: composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )\n      }\n    );\n  }\n);\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = React.forwardRef((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref: composedRefs });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {\n  });\n  React.useEffect(() => {\n    const handlePointerDown = (event) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        let handleAndDispatchPointerDownOutsideEvent2 = function() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        };\n        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n        const eventDetail = { originalEvent: event };\n        if (event.pointerType === \"touch\") {\n          ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n          ownerDocument.addEventListener(\"click\", handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent2();\n        }\n      } else {\n        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n      ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside);\n  const isFocusInsideReactTreeRef = React.useRef(false);\n  React.useEffect(() => {\n    const handleFocus = (event) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener(\"focusin\", handleFocus);\n    return () => ownerDocument.removeEventListener(\"focusin\", handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\nexport {\n  Branch,\n  DismissableLayer,\n  DismissableLayerBranch,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/portal.tsx\nimport * as React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PORTAL_NAME = \"Portal\";\nvar Portal = React.forwardRef((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || mounted && globalThis?.document?.body;\n  return container ? ReactDOM.createPortal(/* @__PURE__ */ jsx(Primitive.div, { ...portalProps, ref: forwardedRef }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\nexport {\n  Portal,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/presence.tsx\nimport * as React2 from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\n\n// src/use-state-machine.tsx\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : React2.Children.only(children);\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? React2.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = React2.useState();\n  const stylesRef = React2.useRef(null);\n  const prevPresentRef = React2.useRef(present);\n  const prevAnimationNameRef = React2.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  React2.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: React2.useCallback((node2) => {\n      stylesRef.current = node2 ? getComputedStyle(node2) : null;\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Presence;\nexport {\n  Presence,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/visually-hidden.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\nexport {\n  Root,\n  VISUALLY_HIDDEN_STYLES,\n  VisuallyHidden\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/toast.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport * as DismissableLayer from \"@radix-ui/react-dismissable-layer\";\nimport { Portal } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(\"Toast\");\nvar [createToastContext, createToastScope] = createContextScope(\"Toast\", [createCollectionScope]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props) => {\n  const {\n    __scopeToast,\n    label = \"Notification\",\n    duration = 5e3,\n    swipeDirection = \"right\",\n    swipeThreshold = 50,\n    children\n  } = props;\n  const [viewport, setViewport] = React.useState(null);\n  const [toastCount, setToastCount] = React.useState(0);\n  const isFocusedToastEscapeKeyDownRef = React.useRef(false);\n  const isClosePausedRef = React.useRef(false);\n  if (!label.trim()) {\n    console.error(\n      `Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`\n    );\n  }\n  return /* @__PURE__ */ jsx(Collection.Provider, { scope: __scopeToast, children: /* @__PURE__ */ jsx(\n    ToastProviderProvider,\n    {\n      scope: __scopeToast,\n      label,\n      duration,\n      swipeDirection,\n      swipeThreshold,\n      toastCount,\n      viewport,\n      onViewportChange: setViewport,\n      onToastAdd: React.useCallback(() => setToastCount((prevCount) => prevCount + 1), []),\n      onToastRemove: React.useCallback(() => setToastCount((prevCount) => prevCount - 1), []),\n      isFocusedToastEscapeKeyDownRef,\n      isClosePausedRef,\n      children\n    }\n  ) });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\"F8\"];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeToast,\n      hotkey = VIEWPORT_DEFAULT_HOTKEY,\n      label = \"Notifications ({hotkey})\",\n      ...viewportProps\n    } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = React.useRef(null);\n    const headFocusProxyRef = React.useRef(null);\n    const tailFocusProxyRef = React.useRef(null);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    React.useEffect(() => {\n      const handleKeyDown = (event) => {\n        const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key) => event[key] || event.code === key);\n        if (isHotkeyPressed) ref.current?.focus();\n      };\n      document.addEventListener(\"keydown\", handleKeyDown);\n      return () => document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [hotkey]);\n    React.useEffect(() => {\n      const wrapper = wrapperRef.current;\n      const viewport = ref.current;\n      if (hasToasts && wrapper && viewport) {\n        const handlePause = () => {\n          if (!context.isClosePausedRef.current) {\n            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n            viewport.dispatchEvent(pauseEvent);\n            context.isClosePausedRef.current = true;\n          }\n        };\n        const handleResume = () => {\n          if (context.isClosePausedRef.current) {\n            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n            viewport.dispatchEvent(resumeEvent);\n            context.isClosePausedRef.current = false;\n          }\n        };\n        const handleFocusOutResume = (event) => {\n          const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n          if (isFocusMovingOutside) handleResume();\n        };\n        const handlePointerLeaveResume = () => {\n          const isFocusInside = wrapper.contains(document.activeElement);\n          if (!isFocusInside) handleResume();\n        };\n        wrapper.addEventListener(\"focusin\", handlePause);\n        wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n        wrapper.addEventListener(\"pointermove\", handlePause);\n        wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n        window.addEventListener(\"blur\", handlePause);\n        window.addEventListener(\"focus\", handleResume);\n        return () => {\n          wrapper.removeEventListener(\"focusin\", handlePause);\n          wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n          wrapper.removeEventListener(\"pointermove\", handlePause);\n          wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n          window.removeEventListener(\"blur\", handlePause);\n          window.removeEventListener(\"focus\", handleResume);\n        };\n      }\n    }, [hasToasts, context.isClosePausedRef]);\n    const getSortedTabbableCandidates = React.useCallback(\n      ({ tabbingDirection }) => {\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem) => {\n          const toastNode = toastItem.ref.current;\n          const toastTabbableCandidates = [toastNode, ...getTabbableCandidates(toastNode)];\n          return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n      },\n      [getItems]\n    );\n    React.useEffect(() => {\n      const viewport = ref.current;\n      if (viewport) {\n        const handleKeyDown = (event) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === \"Tab\" && !isMetaKey;\n          if (isTabKey) {\n            const focusedElement = document.activeElement;\n            const isTabbingBackwards = event.shiftKey;\n            const targetIsViewport = event.target === viewport;\n            if (targetIsViewport && isTabbingBackwards) {\n              headFocusProxyRef.current?.focus();\n              return;\n            }\n            const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n            const sortedCandidates = getSortedTabbableCandidates({ tabbingDirection });\n            const index = sortedCandidates.findIndex((candidate) => candidate === focusedElement);\n            if (focusFirst(sortedCandidates.slice(index + 1))) {\n              event.preventDefault();\n            } else {\n              isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n            }\n          }\n        };\n        viewport.addEventListener(\"keydown\", handleKeyDown);\n        return () => viewport.removeEventListener(\"keydown\", handleKeyDown);\n      }\n    }, [getItems, getSortedTabbableCandidates]);\n    return /* @__PURE__ */ jsxs(\n      DismissableLayer.Branch,\n      {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: { pointerEvents: hasToasts ? void 0 : \"none\" },\n        children: [\n          hasToasts && /* @__PURE__ */ jsx(\n            FocusProxy,\n            {\n              ref: headFocusProxyRef,\n              onFocusFromOutsideViewport: () => {\n                const tabbableCandidates = getSortedTabbableCandidates({\n                  tabbingDirection: \"forwards\"\n                });\n                focusFirst(tabbableCandidates);\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeToast, children: /* @__PURE__ */ jsx(Primitive.ol, { tabIndex: -1, ...viewportProps, ref: composedRefs }) }),\n          hasToasts && /* @__PURE__ */ jsx(\n            FocusProxy,\n            {\n              ref: tailFocusProxyRef,\n              onFocusFromOutsideViewport: () => {\n                const tabbableCandidates = getSortedTabbableCandidates({\n                  tabbingDirection: \"backwards\"\n                });\n                focusFirst(tabbableCandidates);\n              }\n            }\n          )\n        ]\n      }\n    );\n  }\n);\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ jsx(\n      VisuallyHidden,\n      {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: { position: \"fixed\" },\n        onFocus: (event) => {\n          const prevFocusedElement = event.relatedTarget;\n          const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n          if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n      }\n    );\n  }\n);\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = React.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? true,\n      onChange: onOpenChange,\n      caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || open, children: /* @__PURE__ */ jsx(\n      ToastImpl,\n      {\n        open,\n        ...toastProps,\n        ref: forwardedRef,\n        onClose: () => setOpen(false),\n        onPause: useCallbackRef(props.onPause),\n        onResume: useCallbackRef(props.onResume),\n        onSwipeStart: composeEventHandlers(props.onSwipeStart, (event) => {\n          event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n        }),\n        onSwipeMove: composeEventHandlers(props.onSwipeMove, (event) => {\n          const { x, y } = event.detail.delta;\n          event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n        }),\n        onSwipeCancel: composeEventHandlers(props.onSwipeCancel, (event) => {\n          event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n        }),\n        onSwipeEnd: composeEventHandlers(props.onSwipeEnd, (event) => {\n          const { x, y } = event.detail.delta;\n          event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n          setOpen(false);\n        })\n      }\n    ) });\n  }\n);\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n  onClose() {\n  }\n});\nvar ToastImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeToast,\n      type = \"foreground\",\n      duration: durationProp,\n      open,\n      onClose,\n      onEscapeKeyDown,\n      onPause,\n      onResume,\n      onSwipeStart,\n      onSwipeMove,\n      onSwipeCancel,\n      onSwipeEnd,\n      ...toastProps\n    } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const pointerStartRef = React.useRef(null);\n    const swipeDeltaRef = React.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = React.useRef(0);\n    const closeTimerRemainingTimeRef = React.useRef(duration);\n    const closeTimerRef = React.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = useCallbackRef(() => {\n      const isFocusInToast = node?.contains(document.activeElement);\n      if (isFocusInToast) context.viewport?.focus();\n      onClose();\n    });\n    const startTimer = React.useCallback(\n      (duration2) => {\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = (/* @__PURE__ */ new Date()).getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n      },\n      [handleClose]\n    );\n    React.useEffect(() => {\n      const viewport = context.viewport;\n      if (viewport) {\n        const handleResume = () => {\n          startTimer(closeTimerRemainingTimeRef.current);\n          onResume?.();\n        };\n        const handlePause = () => {\n          const elapsedTime = (/* @__PURE__ */ new Date()).getTime() - closeTimerStartTimeRef.current;\n          closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n          window.clearTimeout(closeTimerRef.current);\n          onPause?.();\n        };\n        viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n        viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n        return () => {\n          viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n          viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n        };\n      }\n    }, [context.viewport, duration, onPause, onResume, startTimer]);\n    React.useEffect(() => {\n      if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [open, duration, context.isClosePausedRef, startTimer]);\n    React.useEffect(() => {\n      onToastAdd();\n      return () => onToastRemove();\n    }, [onToastAdd, onToastRemove]);\n    const announceTextContent = React.useMemo(() => {\n      return node ? getAnnounceTextContent(node) : null;\n    }, [node]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      announceTextContent && /* @__PURE__ */ jsx(\n        ToastAnnounce,\n        {\n          __scopeToast,\n          role: \"status\",\n          \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n          \"aria-atomic\": true,\n          children: announceTextContent\n        }\n      ),\n      /* @__PURE__ */ jsx(ToastInteractiveProvider, { scope: __scopeToast, onClose: handleClose, children: ReactDOM.createPortal(\n        /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: __scopeToast, children: /* @__PURE__ */ jsx(\n          DismissableLayer.Root,\n          {\n            asChild: true,\n            onEscapeKeyDown: composeEventHandlers(onEscapeKeyDown, () => {\n              if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n              context.isFocusedToastEscapeKeyDownRef.current = false;\n            }),\n            children: /* @__PURE__ */ jsx(\n              Primitive.li,\n              {\n                role: \"status\",\n                \"aria-live\": \"off\",\n                \"aria-atomic\": true,\n                tabIndex: 0,\n                \"data-state\": open ? \"open\" : \"closed\",\n                \"data-swipe-direction\": context.swipeDirection,\n                ...toastProps,\n                ref: composedRefs,\n                style: { userSelect: \"none\", touchAction: \"none\", ...props.style },\n                onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n                  if (event.key !== \"Escape\") return;\n                  onEscapeKeyDown?.(event.nativeEvent);\n                  if (!event.nativeEvent.defaultPrevented) {\n                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                    handleClose();\n                  }\n                }),\n                onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n                  if (event.button !== 0) return;\n                  pointerStartRef.current = { x: event.clientX, y: event.clientY };\n                }),\n                onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {\n                  if (!pointerStartRef.current) return;\n                  const x = event.clientX - pointerStartRef.current.x;\n                  const y = event.clientY - pointerStartRef.current.y;\n                  const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                  const isHorizontalSwipe = [\"left\", \"right\"].includes(context.swipeDirection);\n                  const clamp = [\"left\", \"up\"].includes(context.swipeDirection) ? Math.min : Math.max;\n                  const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                  const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                  const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                  const delta = { x: clampedX, y: clampedY };\n                  const eventDetail = { originalEvent: event, delta };\n                  if (hasSwipeMoveStarted) {\n                    swipeDeltaRef.current = delta;\n                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                      discrete: false\n                    });\n                  } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                    swipeDeltaRef.current = delta;\n                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                      discrete: false\n                    });\n                    event.target.setPointerCapture(event.pointerId);\n                  } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                    pointerStartRef.current = null;\n                  }\n                }),\n                onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n                  const delta = swipeDeltaRef.current;\n                  const target = event.target;\n                  if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                  }\n                  swipeDeltaRef.current = null;\n                  pointerStartRef.current = null;\n                  if (delta) {\n                    const toast = event.currentTarget;\n                    const eventDetail = { originalEvent: event, delta };\n                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                        discrete: true\n                      });\n                    } else {\n                      handleAndDispatchCustomEvent(\n                        TOAST_SWIPE_CANCEL,\n                        onSwipeCancel,\n                        eventDetail,\n                        {\n                          discrete: true\n                        }\n                      );\n                    }\n                    toast.addEventListener(\"click\", (event2) => event2.preventDefault(), {\n                      once: true\n                    });\n                  }\n                })\n              }\n            )\n          }\n        ) }),\n        context.viewport\n      ) })\n    ] });\n  }\n);\nvar ToastAnnounce = (props) => {\n  const { __scopeToast, children, ...announceProps } = props;\n  const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n  const [renderAnnounceText, setRenderAnnounceText] = React.useState(false);\n  const [isAnnounced, setIsAnnounced] = React.useState(false);\n  useNextFrame(() => setRenderAnnounceText(true));\n  React.useEffect(() => {\n    const timer = window.setTimeout(() => setIsAnnounced(true), 1e3);\n    return () => window.clearTimeout(timer);\n  }, []);\n  return isAnnounced ? null : /* @__PURE__ */ jsx(Portal, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { ...announceProps, children: renderAnnounceText && /* @__PURE__ */ jsxs(Fragment, { children: [\n    context.label,\n    \" \",\n    children\n  ] }) }) });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { ...titleProps, ref: forwardedRef });\n  }\n);\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { ...descriptionProps, ref: forwardedRef });\n  }\n);\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = React.forwardRef(\n  (props, forwardedRef) => {\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n      console.error(\n        `Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`\n      );\n      return null;\n    }\n    return /* @__PURE__ */ jsx(ToastAnnounceExclude, { altText, asChild: true, children: /* @__PURE__ */ jsx(ToastClose, { ...actionProps, ref: forwardedRef }) });\n  }\n);\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ jsx(ToastAnnounceExclude, { asChild: true, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, interactiveContext.onClose)\n      }\n    ) });\n  }\n);\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = React.forwardRef((props, forwardedRef) => {\n  const { __scopeToast, altText, ...announceExcludeProps } = props;\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"data-radix-toast-announce-exclude\": \"\",\n      \"data-radix-toast-announce-alt\": altText || void 0,\n      ...announceExcludeProps,\n      ref: forwardedRef\n    }\n  );\n});\nfunction getAnnounceTextContent(container) {\n  const textContent = [];\n  const childNodes = Array.from(container.childNodes);\n  childNodes.forEach((node) => {\n    if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n    if (isHTMLElement(node)) {\n      const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n      const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n      if (!isHidden) {\n        if (isExcluded) {\n          const altText = node.dataset.radixToastAnnounceAlt;\n          if (altText) textContent.push(altText);\n        } else {\n          textContent.push(...getAnnounceTextContent(node));\n        }\n      }\n    }\n  });\n  return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const currentTarget = detail.originalEvent.currentTarget;\n  const event = new CustomEvent(name, { bubbles: true, cancelable: true, detail });\n  if (handler) currentTarget.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(currentTarget, event);\n  } else {\n    currentTarget.dispatchEvent(event);\n  }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0) => {\n  const deltaX = Math.abs(delta.x);\n  const deltaY = Math.abs(delta.y);\n  const isDeltaX = deltaX > deltaY;\n  if (direction === \"left\" || direction === \"right\") {\n    return isDeltaX && deltaX > threshold;\n  } else {\n    return !isDeltaX && deltaY > threshold;\n  }\n};\nfunction useNextFrame(callback = () => {\n}) {\n  const fn = useCallbackRef(callback);\n  useLayoutEffect(() => {\n    let raf1 = 0;\n    let raf2 = 0;\n    raf1 = window.requestAnimationFrame(() => raf2 = window.requestAnimationFrame(fn));\n    return () => {\n      window.cancelAnimationFrame(raf1);\n      window.cancelAnimationFrame(raf2);\n    };\n  }, [fn]);\n}\nfunction isHTMLElement(node) {\n  return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction focusFirst(candidates) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\nexport {\n  Action,\n  Close,\n  Description,\n  Provider,\n  Root2 as Root,\n  Title,\n  Toast,\n  ToastAction,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n  Viewport,\n  createToastScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "mappings": ";;;;;;;;;;;AACA,SAAS,qBAAqB,sBAAsB,iBAAiB,EAAE,2BAA2B,MAAM,GAAG,CAAE,GAAE;AAC7G,QAAO,SAAS,YAAY,OAAO;AACjC,yBAAuB,MAAM;AAC7B,MAAI,6BAA6B,UAAU,MAAM,iBAC/C,QAAO,kBAAkB,MAAM;CAElC;AACF;;;;;ACND,SAAS,OAAO,KAAK,OAAO;AAC1B,YAAW,QAAQ,WACjB,QAAO,IAAI,MAAM;UACR,QAAQ,QAAQ,aAAa,EACtC,KAAI,UAAU;AAEjB;AACD,SAAS,YAAY,GAAG,MAAM;AAC5B,QAAO,CAAC,SAAS;EACf,IAAI,aAAa;EACjB,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;GACjC,MAAM,UAAU,OAAO,KAAK,KAAK;AACjC,QAAK,qBAAqB,WAAW,WACnC,cAAa;AAEf,UAAO;EACR,EAAC;AACF,MAAI,WACF,QAAO,MAAM;AACX,QAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;IACxC,MAAM,UAAU,SAAS;AACzB,eAAW,WAAW,WACpB,UAAS;QAET,QAAO,KAAK,IAAI,KAAK;GAExB;EACF;CAEJ;AACF;AACD,SAAS,gBAAgB,GAAG,MAAM;AAChC,QAAO,aAAM,YAAY,YAAY,GAAG,KAAK,EAAE,KAAK;AACrD;;;;;AChBD,SAAS,mBAAmB,WAAW,yBAAyB,CAAE,GAAE;CAClE,IAAI,kBAAkB,CAAE;CACxB,SAAS,eAAe,mBAAmB,gBAAgB;EACzD,MAAM,cAAc,aAAM,cAAc,eAAe;EACvD,MAAM,QAAQ,gBAAgB;AAC9B,oBAAkB,CAAC,GAAG,iBAAiB,cAAe;EACtD,MAAMA,aAAW,CAAC,UAAU;GAC1B,MAAM,EAAE,OAAO,SAAU,GAAG,SAAS,GAAG;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,QAAQ,aAAM,QAAQ,MAAM,SAAS,OAAO,OAAO,QAAQ,CAAC;AAClE,0BAAuB,4BAAI,QAAQ,UAAU;IAAE;IAAO;GAAU,EAAC;EAClE;AACD,aAAS,cAAc,oBAAoB;EAC3C,SAAS,YAAY,cAAc,OAAO;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,UAAU,aAAM,WAAW,QAAQ;AACzC,OAAI,QAAS,QAAO;AACpB,OAAI,wBAAwB,EAAG,QAAO;AACtC,SAAM,IAAI,OAAO,IAAI,aAAa,2BAA2B,kBAAkB;EAChF;AACD,SAAO,CAACA,YAAU,WAAY;CAC/B;CACD,MAAM,cAAc,MAAM;EACxB,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,UAAO,aAAM,cAAc,eAAe;EAC3C,EAAC;AACF,SAAO,SAAS,SAAS,OAAO;GAC9B,MAAM,WAAW,QAAQ,cAAc;AACvC,UAAO,aAAM,QACX,OAAO,IAAI,SAAS,cAAc;IAAE,GAAG;KAAQ,YAAY;GAAU,EAAE,IACvE,CAAC,OAAO,QAAS,EAClB;EACF;CACF;AACD,aAAY,YAAY;AACxB,QAAO,CAAC,gBAAgB,qBAAqB,aAAa,GAAG,uBAAuB,AAAC;AACtF;AACD,SAAS,qBAAqB,GAAG,QAAQ;CACvC,MAAM,YAAY,OAAO;AACzB,KAAI,OAAO,WAAW,EAAG,QAAO;CAChC,MAAM,cAAc,MAAM;EACxB,MAAM,aAAa,OAAO,IAAI,CAAC,kBAAkB;GAC/C,UAAU,cAAc;GACxB,WAAW,aAAa;EACzB,GAAE;AACH,SAAO,SAAS,kBAAkB,gBAAgB;GAChD,MAAM,aAAa,WAAW,OAAO,CAAC,aAAa,EAAE,UAAU,WAAW,KAAK;IAC7E,MAAM,aAAa,SAAS,eAAe;IAC3C,MAAM,eAAe,YAAY,SAAS;AAC1C,WAAO;KAAE,GAAG;KAAa,GAAG;IAAc;GAC3C,GAAE,CAAE,EAAC;AACN,UAAO,aAAM,QAAQ,OAAO,IAAI,SAAS,UAAU,cAAc,WAAY,IAAG,CAAC,UAAW,EAAC;EAC9F;CACF;AACD,aAAY,YAAY,UAAU;AAClC,QAAO;AACR;;;;ACnED,SAAS,iBAAiB,MAAM;CAC9B,MAAMC,kBAAgB,OAAO;CAC7B,MAAM,CAAC,yBAAyBC,wBAAsB,GAAG,mBAAmBD,gBAAc;CAC1F,MAAM,CAAC,wBAAwB,qBAAqB,GAAG,wBACrDA,iBACA;EAAE,eAAe,EAAE,SAAS,KAAM;EAAE,yBAAyB,IAAI;CAAO,EACzE;CACD,MAAM,qBAAqB,CAAC,UAAU;EACpC,MAAM,EAAE,OAAO,UAAU,GAAG;EAC5B,MAAM,MAAM,qBAAM,OAAO,KAAK;EAC9B,MAAM,UAAU,qBAAM,uBAAuB,IAAI,MAAM,CAAC;AACxD,yBAAuB,4BAAI,wBAAwB;GAAE;GAAO;GAAS,eAAe;GAAK;EAAU,EAAC;CACrG;AACD,oBAAmB,cAAcA;CACjC,MAAM,uBAAuB,OAAO;CACpC,MAAM,qBAAqB,WAAW,qBAAqB;CAC3D,MAAM,iBAAiB,qBAAM,WAC3B,CAAC,OAAO,iBAAiB;EACvB,MAAM,EAAE,OAAO,UAAU,GAAG;EAC5B,MAAM,UAAU,qBAAqB,sBAAsB,MAAM;EACjE,MAAM,eAAe,gBAAgB,cAAc,QAAQ,cAAc;AACzE,yBAAuB,4BAAI,oBAAoB;GAAE,KAAK;GAAc;EAAU,EAAC;CAChF,EACF;AACD,gBAAe,cAAc;CAC7B,MAAM,iBAAiB,OAAO;CAC9B,MAAM,iBAAiB;CACvB,MAAM,yBAAyB,WAAW,eAAe;CACzD,MAAM,qBAAqB,qBAAM,WAC/B,CAAC,OAAO,iBAAiB;EACvB,MAAM,EAAE,OAAO,SAAU,GAAG,UAAU,GAAG;EACzC,MAAM,MAAM,qBAAM,OAAO,KAAK;EAC9B,MAAM,eAAe,gBAAgB,cAAc,IAAI;EACvD,MAAM,UAAU,qBAAqB,gBAAgB,MAAM;AAC3D,uBAAM,UAAU,MAAM;AACpB,WAAQ,QAAQ,IAAI,KAAK;IAAE;IAAK,GAAG;GAAU,EAAC;AAC9C,UAAO,WAAW,QAAQ,QAAQ,OAAO,IAAI;EAC9C,EAAC;AACF,yBAAuB,4BAAI,wBAAwB;IAAQ,iBAAiB;GAAM,KAAK;GAAc;EAAU,EAAC;CACjH,EACF;AACD,oBAAmB,cAAc;CACjC,SAASE,gBAAc,OAAO;EAC5B,MAAM,UAAU,qBAAqB,OAAO,sBAAsB,MAAM;EACxE,MAAM,WAAW,qBAAM,YAAY,MAAM;GACvC,MAAM,iBAAiB,QAAQ,cAAc;AAC7C,QAAK,eAAgB,QAAO,CAAE;GAC9B,MAAM,eAAe,MAAM,KAAK,eAAe,kBAAkB,GAAG,eAAe,GAAG,CAAC;GACvF,MAAM,QAAQ,MAAM,KAAK,QAAQ,QAAQ,QAAQ,CAAC;GAClD,MAAM,eAAe,MAAM,KACzB,CAAC,GAAG,MAAM,aAAa,QAAQ,EAAE,IAAI,QAAQ,GAAG,aAAa,QAAQ,EAAE,IAAI,QAAQ,CACpF;AACD,UAAO;EACR,GAAE,CAAC,QAAQ,eAAe,QAAQ,OAAQ,EAAC;AAC5C,SAAO;CACR;AACD,QAAO;EACL;GAAE,UAAU;GAAoB,MAAM;GAAgB,UAAU;EAAoB;EACpFA;EACAD;CACD;AACF;;;;;AChED,IAAI,QAAQ;CACV;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACD;AACD,IAAI,YAAY,MAAM,OAAO,CAAC,WAAW,SAAS;CAChD,MAAM,OAAO,YAAY,YAAY,OAAO;CAC5C,MAAME,SAAO,aAAM,WAAW,CAAC,OAAO,iBAAiB;EACrD,MAAM,EAAE,QAAS,GAAG,gBAAgB,GAAG;EACvC,MAAM,OAAO,UAAU,OAAO;AAC9B,aAAW,WAAW,YACpB,QAAO,OAAO,IAAI,WAAW,IAAI;AAEnC,yBAAuB,4BAAI,MAAM;GAAE,GAAG;GAAgB,KAAK;EAAc,EAAC;CAC3E,EAAC;AACF,QAAK,eAAe,YAAY;AAChC,QAAO;EAAE,GAAG;GAAY,OAAOA;CAAM;AACtC,GAAE,CAAE,EAAC;AACN,SAAS,4BAA4B,QAAQ,OAAO;AAClD,KAAI,OAAQ,oBAAS,UAAU,MAAM,OAAO,cAAc,MAAM,CAAC;AAClE;;;;ACrCD,SAAS,eAAe,UAAU;CAChC,MAAM,cAAc,aAAM,OAAO,SAAS;AAC1C,cAAM,UAAU,MAAM;AACpB,cAAY,UAAU;CACvB,EAAC;AACF,QAAO,aAAM,QAAQ,MAAM,CAAC,GAAG,SAAS,YAAY,UAAU,GAAG,KAAK,EAAE,CAAE,EAAC;AAC5E;;;;ACLD,SAAS,iBAAiB,qBAAqB,gBAAgB,YAAY,UAAU;CACnF,MAAM,kBAAkB,eAAe,oBAAoB;AAC3D,cAAM,UAAU,MAAM;EACpB,MAAM,gBAAgB,CAAC,UAAU;AAC/B,OAAI,MAAM,QAAQ,SAChB,iBAAgB,MAAM;EAEzB;AACD,gBAAc,iBAAiB,WAAW,eAAe,EAAE,SAAS,KAAM,EAAC;AAC3E,SAAO,MAAM,cAAc,oBAAoB,WAAW,eAAe,EAAE,SAAS,KAAM,EAAC;CAC5F,GAAE,CAAC,iBAAiB,aAAc,EAAC;AACrC;;;;ACJD,IAAI,yBAAyB;AAC7B,IAAI,iBAAiB;AACrB,IAAI,uBAAuB;AAC3B,IAAI,gBAAgB;AACpB,IAAI;AACJ,IAAI,0BAA0B,aAAM,cAAc;CAChD,wBAAwB,IAAI;CAC5B,wDAAwD,IAAI;CAC5D,0BAA0B,IAAI;AAC/B,EAAC;AACF,IAAI,mBAAmB,aAAM,WAC3B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,8BAA8B,OAC9B,iBACA,sBACA,gBACA,mBACA,UACA,GAAG,YACJ,GAAG;CACJ,MAAM,UAAU,aAAM,WAAW,wBAAwB;CACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,aAAM,SAAS,KAAK;CAC5C,MAAM,gBAAgB,MAAM,iBAAiB,YAAY;CACzD,MAAM,GAAG,MAAM,GAAG,aAAM,SAAS,CAAE,EAAC;CACpC,MAAM,eAAe,gBAAgB,cAAc,CAAC,UAAU,QAAQ,MAAM,CAAC;CAC7E,MAAM,SAAS,MAAM,KAAK,QAAQ,OAAO;CACzC,MAAM,CAAC,6CAA6C,GAAG,CAAC,GAAG,QAAQ,sCAAuC,EAAC,MAAM,GAAG;CACpH,MAAM,oDAAoD,OAAO,QAAQ,6CAA6C;CACtH,MAAM,QAAQ,OAAO,OAAO,QAAQ,KAAK,GAAG;CAC5C,MAAM,8BAA8B,QAAQ,uCAAuC,OAAO;CAC1F,MAAM,yBAAyB,SAAS;CACxC,MAAM,qBAAqB,sBAAsB,CAAC,UAAU;EAC1D,MAAM,SAAS,MAAM;EACrB,MAAM,wBAAwB,CAAC,GAAG,QAAQ,QAAS,EAAC,KAAK,CAAC,WAAW,OAAO,SAAS,OAAO,CAAC;AAC7F,OAAK,0BAA0B,sBAAuB;AACtD,yBAAuB,MAAM;AAC7B,sBAAoB,MAAM;AAC1B,OAAK,MAAM,iBAAkB,cAAa;CAC3C,GAAE,cAAc;CACjB,MAAM,eAAe,gBAAgB,CAAC,UAAU;EAC9C,MAAM,SAAS,MAAM;EACrB,MAAM,kBAAkB,CAAC,GAAG,QAAQ,QAAS,EAAC,KAAK,CAAC,WAAW,OAAO,SAAS,OAAO,CAAC;AACvF,MAAI,gBAAiB;AACrB,mBAAiB,MAAM;AACvB,sBAAoB,MAAM;AAC1B,OAAK,MAAM,iBAAkB,cAAa;CAC3C,GAAE,cAAc;AACjB,kBAAiB,CAAC,UAAU;EAC1B,MAAM,iBAAiB,UAAU,QAAQ,OAAO,OAAO;AACvD,OAAK,eAAgB;AACrB,oBAAkB,MAAM;AACxB,OAAK,MAAM,oBAAoB,WAAW;AACxC,SAAM,gBAAgB;AACtB,cAAW;EACZ;CACF,GAAE,cAAc;AACjB,cAAM,UAAU,MAAM;AACpB,OAAK,KAAM;AACX,MAAI,6BAA6B;AAC/B,OAAI,QAAQ,uCAAuC,SAAS,GAAG;AAC7D,gCAA4B,cAAc,KAAK,MAAM;AACrD,kBAAc,KAAK,MAAM,gBAAgB;GAC1C;AACD,WAAQ,uCAAuC,IAAI,KAAK;EACzD;AACD,UAAQ,OAAO,IAAI,KAAK;AACxB,kBAAgB;AAChB,SAAO,MAAM;AACX,OAAI,+BAA+B,QAAQ,uCAAuC,SAAS,EACzF,eAAc,KAAK,MAAM,gBAAgB;EAE5C;CACF,GAAE;EAAC;EAAM;EAAe;EAA6B;CAAQ,EAAC;AAC/D,cAAM,UAAU,MAAM;AACpB,SAAO,MAAM;AACX,QAAK,KAAM;AACX,WAAQ,OAAO,OAAO,KAAK;AAC3B,WAAQ,uCAAuC,OAAO,KAAK;AAC3D,mBAAgB;EACjB;CACF,GAAE,CAAC,MAAM,OAAQ,EAAC;AACnB,cAAM,UAAU,MAAM;EACpB,MAAM,eAAe,MAAM,MAAM,CAAE,EAAC;AACpC,WAAS,iBAAiB,gBAAgB,aAAa;AACvD,SAAO,MAAM,SAAS,oBAAoB,gBAAgB,aAAa;CACxE,GAAE,CAAE,EAAC;AACN,wBAAuB,4BACrB,UAAU,KACV;EACE,GAAG;EACH,KAAK;EACL,OAAO;GACL,eAAe,8BAA8B,yBAAyB,SAAS,cAAc;GAC7F,GAAG,MAAM;EACV;EACD,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,eAAe;EACvF,eAAe,qBAAqB,MAAM,eAAe,aAAa,cAAc;EACpF,sBAAsB,qBACpB,MAAM,sBACN,mBAAmB,qBACpB;CACF,EACF;AACF,EACF;AACD,iBAAiB,cAAc;AAC/B,IAAI,cAAc;AAClB,IAAI,yBAAyB,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACrE,MAAM,UAAU,aAAM,WAAW,wBAAwB;CACzD,MAAM,MAAM,aAAM,OAAO,KAAK;CAC9B,MAAM,eAAe,gBAAgB,cAAc,IAAI;AACvD,cAAM,UAAU,MAAM;EACpB,MAAM,OAAO,IAAI;AACjB,MAAI,MAAM;AACR,WAAQ,SAAS,IAAI,KAAK;AAC1B,UAAO,MAAM;AACX,YAAQ,SAAS,OAAO,KAAK;GAC9B;EACF;CACF,GAAE,CAAC,QAAQ,QAAS,EAAC;AACtB,wBAAuB,4BAAI,UAAU,KAAK;EAAE,GAAG;EAAO,KAAK;CAAc,EAAC;AAC3E,EAAC;AACF,uBAAuB,cAAc;AACrC,SAAS,sBAAsB,sBAAsB,gBAAgB,YAAY,UAAU;CACzF,MAAM,2BAA2B,eAAe,qBAAqB;CACrE,MAAM,8BAA8B,aAAM,OAAO,MAAM;CACvD,MAAM,iBAAiB,aAAM,OAAO,MAAM,CACzC,EAAC;AACF,cAAM,UAAU,MAAM;EACpB,MAAM,oBAAoB,CAAC,UAAU;AACnC,OAAI,MAAM,WAAW,4BAA4B,SAAS;IACxD,IAAI,4CAA4C,WAAW;AACzD,oCACE,sBACA,0BACA,aACA,EAAE,UAAU,KAAM,EACnB;IACF;IACD,IAAI,2CAA2C;IAC/C,MAAM,cAAc,EAAE,eAAe,MAAO;AAC5C,QAAI,MAAM,gBAAgB,SAAS;AACjC,mBAAc,oBAAoB,SAAS,eAAe,QAAQ;AAClE,oBAAe,UAAU;AACzB,mBAAc,iBAAiB,SAAS,eAAe,SAAS,EAAE,MAAM,KAAM,EAAC;IAChF,MACC,4CAA2C;GAE9C,MACC,eAAc,oBAAoB,SAAS,eAAe,QAAQ;AAEpE,+BAA4B,UAAU;EACvC;EACD,MAAM,UAAU,OAAO,WAAW,MAAM;AACtC,iBAAc,iBAAiB,eAAe,kBAAkB;EACjE,GAAE,EAAE;AACL,SAAO,MAAM;AACX,UAAO,aAAa,QAAQ;AAC5B,iBAAc,oBAAoB,eAAe,kBAAkB;AACnE,iBAAc,oBAAoB,SAAS,eAAe,QAAQ;EACnE;CACF,GAAE,CAAC,eAAe,wBAAyB,EAAC;AAC7C,QAAO,EAEL,sBAAsB,MAAM,4BAA4B,UAAU,KACnE;AACF;AACD,SAAS,gBAAgB,gBAAgB,gBAAgB,YAAY,UAAU;CAC7E,MAAM,qBAAqB,eAAe,eAAe;CACzD,MAAM,4BAA4B,aAAM,OAAO,MAAM;AACrD,cAAM,UAAU,MAAM;EACpB,MAAM,cAAc,CAAC,UAAU;AAC7B,OAAI,MAAM,WAAW,0BAA0B,SAAS;IACtD,MAAM,cAAc,EAAE,eAAe,MAAO;AAC5C,mCAA6B,eAAe,oBAAoB,aAAa,EAC3E,UAAU,MACX,EAAC;GACH;EACF;AACD,gBAAc,iBAAiB,WAAW,YAAY;AACtD,SAAO,MAAM,cAAc,oBAAoB,WAAW,YAAY;CACvE,GAAE,CAAC,eAAe,kBAAmB,EAAC;AACvC,QAAO;EACL,gBAAgB,MAAM,0BAA0B,UAAU;EAC1D,eAAe,MAAM,0BAA0B,UAAU;CAC1D;AACF;AACD,SAAS,iBAAiB;CACxB,MAAM,QAAQ,IAAI,YAAY;AAC9B,UAAS,cAAc,MAAM;AAC9B;AACD,SAASC,+BAA6B,MAAM,SAAS,QAAQ,EAAE,UAAU,EAAE;CACzE,MAAM,SAAS,OAAO,cAAc;CACpC,MAAM,QAAQ,IAAI,YAAY,MAAM;EAAE,SAAS;EAAO,YAAY;EAAM;CAAQ;AAChF,KAAI,QAAS,QAAO,iBAAiB,MAAM,SAAS,EAAE,MAAM,KAAM,EAAC;AACnE,KAAI,SACF,6BAA4B,QAAQ,MAAM;KAE1C,QAAO,cAAc,MAAM;AAE9B;AACD,IAAI,OAAO;AACX,IAAI,SAAS;;;;ACnNb,IAAI,mBAAmB,YAAY,WAAWC,aAAM,kBAAkB,MAAM,CAC3E;;;;;ACKD,IAAI,cAAc;AAClB,IAAI,SAAS,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACrD,MAAM,EAAE,WAAW,cAAe,GAAG,aAAa,GAAG;CACrD,MAAM,CAAC,SAAS,WAAW,GAAG,aAAM,SAAS,MAAM;AACnD,kBAAgB,MAAM,WAAW,KAAK,EAAE,CAAE,EAAC;CAC3C,MAAM,YAAY,iBAAiB,WAAW,YAAY,UAAU;AACpE,QAAO,YAAY,mBAAS,6BAA6B,4BAAI,UAAU,KAAK;EAAE,GAAG;EAAa,KAAK;CAAc,EAAC,EAAE,UAAU,GAAG;AAClI,EAAC;AACF,OAAO,cAAc;;;;ACPrB,SAAS,gBAAgB,cAAc,SAAS;AAC9C,QAAO,aAAM,WAAW,CAAC,OAAO,UAAU;EACxC,MAAM,YAAY,QAAQ,OAAO;AACjC,SAAO,aAAa;CACrB,GAAE,aAAa;AACjB;AAGD,IAAI,WAAW,CAAC,UAAU;CACxB,MAAM,EAAE,SAAS,UAAU,GAAG;CAC9B,MAAM,WAAW,YAAY,QAAQ;CACrC,MAAM,eAAe,aAAa,aAAa,SAAS,EAAE,SAAS,SAAS,UAAW,EAAC,GAAG,aAAO,SAAS,KAAK,SAAS;CACzH,MAAM,MAAM,gBAAgB,SAAS,KAAK,cAAc,MAAM,CAAC;CAC/D,MAAM,oBAAoB,aAAa;AACvC,QAAO,cAAc,SAAS,YAAY,aAAO,aAAa,OAAO,EAAE,IAAK,EAAC,GAAG;AACjF;AACD,SAAS,cAAc;AACvB,SAAS,YAAY,SAAS;CAC5B,MAAM,CAAC,MAAM,QAAQ,GAAG,aAAO,UAAU;CACzC,MAAM,YAAY,aAAO,OAAO,KAAK;CACrC,MAAM,iBAAiB,aAAO,OAAO,QAAQ;CAC7C,MAAM,uBAAuB,aAAO,OAAO,OAAO;CAClD,MAAM,eAAe,UAAU,YAAY;CAC3C,MAAM,CAAC,OAAO,KAAK,GAAG,gBAAgB,cAAc;EAClD,SAAS;GACP,SAAS;GACT,eAAe;EAChB;EACD,kBAAkB;GAChB,OAAO;GACP,eAAe;EAChB;EACD,WAAW,EACT,OAAO,UACR;CACF,EAAC;AACF,cAAO,UAAU,MAAM;EACrB,MAAM,uBAAuB,iBAAiB,UAAU,QAAQ;AAChE,uBAAqB,UAAU,UAAU,YAAY,uBAAuB;CAC7E,GAAE,CAAC,KAAM,EAAC;AACX,kBAAgB,MAAM;EACpB,MAAM,SAAS,UAAU;EACzB,MAAM,aAAa,eAAe;EAClC,MAAM,oBAAoB,eAAe;AACzC,MAAI,mBAAmB;GACrB,MAAM,oBAAoB,qBAAqB;GAC/C,MAAM,uBAAuB,iBAAiB,OAAO;AACrD,OAAI,QACF,MAAK,QAAQ;YACJ,yBAAyB,UAAU,QAAQ,YAAY,OAChE,MAAK,UAAU;QACV;IACL,MAAM,cAAc,sBAAsB;AAC1C,QAAI,cAAc,YAChB,MAAK,gBAAgB;QAErB,MAAK,UAAU;GAElB;AACD,kBAAe,UAAU;EAC1B;CACF,GAAE,CAAC,SAAS,IAAK,EAAC;AACnB,kBAAgB,MAAM;AACpB,MAAI,MAAM;GACR,IAAI;GACJ,MAAM,cAAc,KAAK,cAAc,eAAe;GACtD,MAAM,qBAAqB,CAAC,UAAU;IACpC,MAAM,uBAAuB,iBAAiB,UAAU,QAAQ;IAChE,MAAM,qBAAqB,qBAAqB,SAAS,MAAM,cAAc;AAC7E,QAAI,MAAM,WAAW,QAAQ,oBAAoB;AAC/C,UAAK,gBAAgB;AACrB,UAAK,eAAe,SAAS;MAC3B,MAAM,kBAAkB,KAAK,MAAM;AACnC,WAAK,MAAM,oBAAoB;AAC/B,kBAAY,YAAY,WAAW,MAAM;AACvC,WAAI,KAAK,MAAM,sBAAsB,WACnC,MAAK,MAAM,oBAAoB;MAElC,EAAC;KACH;IACF;GACF;GACD,MAAM,uBAAuB,CAAC,UAAU;AACtC,QAAI,MAAM,WAAW,KACnB,sBAAqB,UAAU,iBAAiB,UAAU,QAAQ;GAErE;AACD,QAAK,iBAAiB,kBAAkB,qBAAqB;AAC7D,QAAK,iBAAiB,mBAAmB,mBAAmB;AAC5D,QAAK,iBAAiB,gBAAgB,mBAAmB;AACzD,UAAO,MAAM;AACX,gBAAY,aAAa,UAAU;AACnC,SAAK,oBAAoB,kBAAkB,qBAAqB;AAChE,SAAK,oBAAoB,mBAAmB,mBAAmB;AAC/D,SAAK,oBAAoB,gBAAgB,mBAAmB;GAC7D;EACF,MACC,MAAK,gBAAgB;CAExB,GAAE,CAAC,MAAM,IAAK,EAAC;AAChB,QAAO;EACL,WAAW,CAAC,WAAW,kBAAmB,EAAC,SAAS,MAAM;EAC1D,KAAK,aAAO,YAAY,CAAC,UAAU;AACjC,aAAU,UAAU,QAAQ,iBAAiB,MAAM,GAAG;AACtD,WAAQ,MAAM;EACf,GAAE,CAAE,EAAC;CACP;AACF;AACD,SAAS,iBAAiB,QAAQ;AAChC,QAAO,QAAQ,iBAAiB;AACjC;AACD,SAAS,cAAc,SAAS;CAC9B,IAAI,SAAS,OAAO,yBAAyB,QAAQ,OAAO,MAAM,EAAE;CACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,KAAI,QACF,QAAO,QAAQ;AAEjB,UAAS,OAAO,yBAAyB,SAAS,MAAM,EAAE;AAC1D,WAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,KAAI,QACF,QAAO,QAAQ,MAAM;AAEvB,QAAO,QAAQ,MAAM,OAAO,QAAQ;AACrC;;;;ACjID,IAAI,qBAAqBC,aAAM,uBAAuB,MAAM,CAAC,UAAU,KAAKC;AAC5E,SAAS,qBAAqB,EAC5B,MACA,aACA,WAAW,MAAM,CAChB,GACD,QACD,EAAE;CACD,MAAM,CAAC,kBAAkB,qBAAqB,YAAY,GAAG,qBAAqB;EAChF;EACA;CACD,EAAC;CACF,MAAM,eAAe,cAAc;CACnC,MAAM,QAAQ,eAAe,OAAO;CAC1B;EACR,MAAM,kBAAkB,aAAM,OAAO,cAAc,EAAE;AACrD,eAAM,UAAU,MAAM;GACpB,MAAM,gBAAgB,gBAAgB;AACtC,OAAI,kBAAkB,cAAc;IAClC,MAAM,OAAO,gBAAgB,eAAe;IAC5C,MAAM,KAAK,eAAe,eAAe;AACzC,YAAQ,QACH,OAAO,oBAAoB,KAAK,MAAM,GAAG,4KAC7C;GACF;AACD,mBAAgB,UAAU;EAC3B,GAAE,CAAC,cAAc,MAAO,EAAC;CAC3B;CACD,MAAM,WAAW,aAAM,YACrB,CAAC,cAAc;AACb,MAAI,cAAc;GAChB,MAAM,SAAS,WAAW,UAAU,GAAG,UAAU,KAAK,GAAG;AACzD,OAAI,WAAW,KACb,aAAY,UAAU,OAAO;EAEhC,MACC,qBAAoB,UAAU;CAEjC,GACD;EAAC;EAAc;EAAM;EAAqB;CAAY,EACvD;AACD,QAAO,CAAC,OAAO,QAAS;AACzB;AACD,SAAS,qBAAqB,EAC5B,aACA,UACD,EAAE;CACD,MAAM,CAAC,OAAO,SAAS,GAAG,aAAM,SAAS,YAAY;CACrD,MAAM,eAAe,aAAM,OAAO,MAAM;CACxC,MAAM,cAAc,aAAM,OAAO,SAAS;AAC1C,oBAAmB,MAAM;AACvB,cAAY,UAAU;CACvB,GAAE,CAAC,QAAS,EAAC;AACd,cAAM,UAAU,MAAM;AACpB,MAAI,aAAa,YAAY,OAAO;AAClC,eAAY,UAAU,MAAM;AAC5B,gBAAa,UAAU;EACxB;CACF,GAAE,CAAC,OAAO,YAAa,EAAC;AACzB,QAAO;EAAC;EAAO;EAAU;CAAY;AACtC;AACD,SAAS,WAAW,OAAO;AACzB,eAAc,UAAU;AACzB;AAKD,IAAI,aAAa,OAAO,mBAAmB;;;;ACnE3C,IAAI,yBAAyB,OAAO,OAAO;CAEzC,UAAU;CACV,QAAQ;CACR,OAAO;CACP,QAAQ;CACR,SAAS;CACT,QAAQ;CACR,UAAU;CACV,MAAM;CACN,YAAY;CACZ,UAAU;AACX,EAAC;AACF,IAAI,OAAO;AACX,IAAI,iBAAiB,aAAM,WACzB,CAAC,OAAO,iBAAiB;AACvB,wBAAuB,4BACrB,UAAU,MACV;EACE,GAAG;EACH,KAAK;EACL,OAAO;GAAE,GAAG;GAAwB,GAAG,MAAM;EAAO;CACrD,EACF;AACF,EACF;AACD,eAAe,cAAc;;;;;ACZ7B,IAAI,gBAAgB;AACpB,IAAI,CAAC,YAAY,eAAe,sBAAsB,GAAG,iBAAiB,QAAQ;AAClF,IAAI,CAAC,oBAAoB,iBAAiB,GAAG,mBAAmB,SAAS,CAAC,qBAAsB,EAAC;AACjG,IAAI,CAAC,uBAAuB,wBAAwB,GAAG,mBAAmB,cAAc;AACxF,IAAI,gBAAgB,CAAC,UAAU;CAC7B,MAAM,EACJ,cACA,QAAQ,gBACR,WAAW,KACX,iBAAiB,SACjB,iBAAiB,IACjB,UACD,GAAG;CACJ,MAAM,CAAC,UAAU,YAAY,GAAG,aAAM,SAAS,KAAK;CACpD,MAAM,CAAC,YAAY,cAAc,GAAG,aAAM,SAAS,EAAE;CACrD,MAAM,iCAAiC,aAAM,OAAO,MAAM;CAC1D,MAAM,mBAAmB,aAAM,OAAO,MAAM;AAC5C,MAAK,MAAM,MAAM,CACf,SAAQ,OACL,uCAAuC,cAAc,oCACvD;AAEH,wBAAuB,4BAAI,WAAW,UAAU;EAAE,OAAO;EAAc,0BAA0B,4BAC/F,uBACA;GACE,OAAO;GACP;GACA;GACA;GACA;GACA;GACA;GACA,kBAAkB;GAClB,YAAY,aAAM,YAAY,MAAM,cAAc,CAAC,cAAc,YAAY,EAAE,EAAE,CAAE,EAAC;GACpF,eAAe,aAAM,YAAY,MAAM,cAAc,CAAC,cAAc,YAAY,EAAE,EAAE,CAAE,EAAC;GACvF;GACA;GACA;EACD,EACF;CAAE,EAAC;AACL;AACD,cAAc,cAAc;AAC5B,IAAI,gBAAgB;AACpB,IAAI,0BAA0B,CAAC,IAAK;AACpC,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,gBAAgB,aAAM,WACxB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,cACA,SAAS,yBACT,QAAQ,2BACR,GAAG,eACJ,GAAG;CACJ,MAAM,UAAU,wBAAwB,eAAe,aAAa;CACpE,MAAM,WAAW,cAAc,aAAa;CAC5C,MAAM,aAAa,aAAM,OAAO,KAAK;CACrC,MAAM,oBAAoB,aAAM,OAAO,KAAK;CAC5C,MAAM,oBAAoB,aAAM,OAAO,KAAK;CAC5C,MAAM,MAAM,aAAM,OAAO,KAAK;CAC9B,MAAM,eAAe,gBAAgB,cAAc,KAAK,QAAQ,iBAAiB;CACjF,MAAM,cAAc,OAAO,KAAK,IAAI,CAAC,QAAQ,QAAQ,GAAG,CAAC,QAAQ,UAAU,GAAG;CAC9E,MAAM,YAAY,QAAQ,aAAa;AACvC,cAAM,UAAU,MAAM;EACpB,MAAM,gBAAgB,CAAC,UAAU;GAC/B,MAAM,kBAAkB,OAAO,WAAW,KAAK,OAAO,MAAM,CAAC,QAAQ,MAAM,QAAQ,MAAM,SAAS,IAAI;AACtG,OAAI,gBAAiB,KAAI,SAAS,OAAO;EAC1C;AACD,WAAS,iBAAiB,WAAW,cAAc;AACnD,SAAO,MAAM,SAAS,oBAAoB,WAAW,cAAc;CACpE,GAAE,CAAC,MAAO,EAAC;AACZ,cAAM,UAAU,MAAM;EACpB,MAAM,UAAU,WAAW;EAC3B,MAAM,WAAW,IAAI;AACrB,MAAI,aAAa,WAAW,UAAU;GACpC,MAAM,cAAc,MAAM;AACxB,SAAK,QAAQ,iBAAiB,SAAS;KACrC,MAAM,aAAa,IAAI,YAAY;AACnC,cAAS,cAAc,WAAW;AAClC,aAAQ,iBAAiB,UAAU;IACpC;GACF;GACD,MAAM,eAAe,MAAM;AACzB,QAAI,QAAQ,iBAAiB,SAAS;KACpC,MAAM,cAAc,IAAI,YAAY;AACpC,cAAS,cAAc,YAAY;AACnC,aAAQ,iBAAiB,UAAU;IACpC;GACF;GACD,MAAM,uBAAuB,CAAC,UAAU;IACtC,MAAM,wBAAwB,QAAQ,SAAS,MAAM,cAAc;AACnE,QAAI,qBAAsB,eAAc;GACzC;GACD,MAAM,2BAA2B,MAAM;IACrC,MAAM,gBAAgB,QAAQ,SAAS,SAAS,cAAc;AAC9D,SAAK,cAAe,eAAc;GACnC;AACD,WAAQ,iBAAiB,WAAW,YAAY;AAChD,WAAQ,iBAAiB,YAAY,qBAAqB;AAC1D,WAAQ,iBAAiB,eAAe,YAAY;AACpD,WAAQ,iBAAiB,gBAAgB,yBAAyB;AAClE,UAAO,iBAAiB,QAAQ,YAAY;AAC5C,UAAO,iBAAiB,SAAS,aAAa;AAC9C,UAAO,MAAM;AACX,YAAQ,oBAAoB,WAAW,YAAY;AACnD,YAAQ,oBAAoB,YAAY,qBAAqB;AAC7D,YAAQ,oBAAoB,eAAe,YAAY;AACvD,YAAQ,oBAAoB,gBAAgB,yBAAyB;AACrE,WAAO,oBAAoB,QAAQ,YAAY;AAC/C,WAAO,oBAAoB,SAAS,aAAa;GAClD;EACF;CACF,GAAE,CAAC,WAAW,QAAQ,gBAAiB,EAAC;CACzC,MAAM,8BAA8B,aAAM,YACxC,CAAC,EAAE,kBAAkB,KAAK;EACxB,MAAM,aAAa,UAAU;EAC7B,MAAM,qBAAqB,WAAW,IAAI,CAAC,cAAc;GACvD,MAAM,YAAY,UAAU,IAAI;GAChC,MAAM,0BAA0B,CAAC,WAAW,GAAG,sBAAsB,UAAU,AAAC;AAChF,UAAO,qBAAqB,aAAa,0BAA0B,wBAAwB,SAAS;EACrG,EAAC;AACF,SAAO,CAAC,qBAAqB,aAAa,mBAAmB,SAAS,GAAG,oBAAoB,MAAM;CACpG,GACD,CAAC,QAAS,EACX;AACD,cAAM,UAAU,MAAM;EACpB,MAAM,WAAW,IAAI;AACrB,MAAI,UAAU;GACZ,MAAM,gBAAgB,CAAC,UAAU;IAC/B,MAAM,YAAY,MAAM,UAAU,MAAM,WAAW,MAAM;IACzD,MAAM,WAAW,MAAM,QAAQ,UAAU;AACzC,QAAI,UAAU;KACZ,MAAM,iBAAiB,SAAS;KAChC,MAAM,qBAAqB,MAAM;KACjC,MAAM,mBAAmB,MAAM,WAAW;AAC1C,SAAI,oBAAoB,oBAAoB;AAC1C,wBAAkB,SAAS,OAAO;AAClC;KACD;KACD,MAAM,mBAAmB,qBAAqB,cAAc;KAC5D,MAAM,mBAAmB,4BAA4B,EAAE,iBAAkB,EAAC;KAC1E,MAAM,QAAQ,iBAAiB,UAAU,CAAC,cAAc,cAAc,eAAe;AACrF,SAAI,WAAW,iBAAiB,MAAM,QAAQ,EAAE,CAAC,CAC/C,OAAM,gBAAgB;SAEtB,sBAAqB,kBAAkB,SAAS,OAAO,GAAG,kBAAkB,SAAS,OAAO;IAE/F;GACF;AACD,YAAS,iBAAiB,WAAW,cAAc;AACnD,UAAO,MAAM,SAAS,oBAAoB,WAAW,cAAc;EACpE;CACF,GAAE,CAAC,UAAU,2BAA4B,EAAC;AAC3C,wBAAuB,qCAErB;EACE,KAAK;EACL,MAAM;EACN,cAAc,MAAM,QAAQ,YAAY,YAAY;EACpD,UAAU;EACV,OAAO,EAAE,eAAe,iBAAiB,IAAI,OAAQ;EACrD,UAAU;GACR,6BAA6B,4BAC3B,YACA;IACE,KAAK;IACL,4BAA4B,MAAM;KAChC,MAAM,qBAAqB,4BAA4B,EACrD,kBAAkB,WACnB,EAAC;AACF,gBAAW,mBAAmB;IAC/B;GACF,EACF;mBACe,4BAAI,WAAW,MAAM;IAAE,OAAO;IAAc,0BAA0B,4BAAI,UAAU,IAAI;KAAE,UAAU;KAAI,GAAG;KAAe,KAAK;IAAc,EAAC;GAAE,EAAC;GACjK,6BAA6B,4BAC3B,YACA;IACE,KAAK;IACL,4BAA4B,MAAM;KAChC,MAAM,qBAAqB,4BAA4B,EACrD,kBAAkB,YACnB,EAAC;AACF,gBAAW,mBAAmB;IAC/B;GACF,EACF;EACF;CACF,EACF;AACF,EACF;AACD,cAAc,cAAc;AAC5B,IAAI,mBAAmB;AACvB,IAAI,aAAa,aAAM,WACrB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,cAAc,2BAA4B,GAAG,YAAY,GAAG;CACpE,MAAM,UAAU,wBAAwB,kBAAkB,aAAa;AACvE,wBAAuB,4BACrB,gBACA;EACE,eAAe;EACf,UAAU;EACV,GAAG;EACH,KAAK;EACL,OAAO,EAAE,UAAU,QAAS;EAC5B,SAAS,CAAC,UAAU;GAClB,MAAM,qBAAqB,MAAM;GACjC,MAAM,8BAA8B,QAAQ,UAAU,SAAS,mBAAmB;AAClF,OAAI,2BAA4B,6BAA4B;EAC7D;CACF,EACF;AACF,EACF;AACD,WAAW,cAAc;AACzB,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,kBAAkB;AACtB,IAAI,QAAQ,aAAM,WAChB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,YAAY,MAAM,UAAU,aAAa,aAAc,GAAG,YAAY,GAAG;CACjF,MAAM,CAAC,MAAM,QAAQ,GAAG,qBAAqB;EAC3C,MAAM;EACN,aAAa,eAAe;EAC5B,UAAU;EACV,QAAQ;CACT,EAAC;AACF,wBAAuB,4BAAI,UAAU;EAAE,SAAS,cAAc;EAAM,0BAA0B,4BAC5F,WACA;GACE;GACA,GAAG;GACH,KAAK;GACL,SAAS,MAAM,QAAQ,MAAM;GAC7B,SAAS,eAAe,MAAM,QAAQ;GACtC,UAAU,eAAe,MAAM,SAAS;GACxC,cAAc,qBAAqB,MAAM,cAAc,CAAC,UAAU;AAChE,UAAM,cAAc,aAAa,cAAc,QAAQ;GACxD,EAAC;GACF,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;IAC9D,MAAM,EAAE,GAAG,GAAG,GAAG,MAAM,OAAO;AAC9B,UAAM,cAAc,aAAa,cAAc,OAAO;AACtD,UAAM,cAAc,MAAM,YAAY,iCAAiC,EAAE,IAAI;AAC7E,UAAM,cAAc,MAAM,YAAY,iCAAiC,EAAE,IAAI;GAC9E,EAAC;GACF,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,UAAM,cAAc,aAAa,cAAc,SAAS;AACxD,UAAM,cAAc,MAAM,eAAe,6BAA6B;AACtE,UAAM,cAAc,MAAM,eAAe,6BAA6B;AACtE,UAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,UAAM,cAAc,MAAM,eAAe,4BAA4B;GACtE,EAAC;GACF,YAAY,qBAAqB,MAAM,YAAY,CAAC,UAAU;IAC5D,MAAM,EAAE,GAAG,GAAG,GAAG,MAAM,OAAO;AAC9B,UAAM,cAAc,aAAa,cAAc,MAAM;AACrD,UAAM,cAAc,MAAM,eAAe,6BAA6B;AACtE,UAAM,cAAc,MAAM,eAAe,6BAA6B;AACtE,UAAM,cAAc,MAAM,YAAY,gCAAgC,EAAE,IAAI;AAC5E,UAAM,cAAc,MAAM,YAAY,gCAAgC,EAAE,IAAI;AAC5E,YAAQ,MAAM;GACf,EAAC;EACH,EACF;CAAE,EAAC;AACL,EACF;AACD,MAAM,cAAc;AACpB,IAAI,CAAC,0BAA0B,2BAA2B,GAAG,mBAAmB,YAAY,EAC1F,UAAU,CACT,EACF,EAAC;AACF,IAAI,YAAY,aAAM,WACpB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,cACA,OAAO,cACP,UAAU,cACV,MACA,SACA,iBACA,SACA,UACA,cACA,aACA,eACA,WACA,GAAG,YACJ,GAAG;CACJ,MAAM,UAAU,wBAAwB,YAAY,aAAa;CACjE,MAAM,CAAC,MAAM,QAAQ,GAAG,aAAM,SAAS,KAAK;CAC5C,MAAM,eAAe,gBAAgB,cAAc,CAAC,UAAU,QAAQ,MAAM,CAAC;CAC7E,MAAM,kBAAkB,aAAM,OAAO,KAAK;CAC1C,MAAM,gBAAgB,aAAM,OAAO,KAAK;CACxC,MAAM,WAAW,gBAAgB,QAAQ;CACzC,MAAM,yBAAyB,aAAM,OAAO,EAAE;CAC9C,MAAM,6BAA6B,aAAM,OAAO,SAAS;CACzD,MAAM,gBAAgB,aAAM,OAAO,EAAE;CACrC,MAAM,EAAE,YAAY,eAAe,GAAG;CACtC,MAAM,cAAc,eAAe,MAAM;EACvC,MAAM,iBAAiB,MAAM,SAAS,SAAS,cAAc;AAC7D,MAAI,eAAgB,SAAQ,UAAU,OAAO;AAC7C,WAAS;CACV,EAAC;CACF,MAAM,aAAa,aAAM,YACvB,CAAC,cAAc;AACb,OAAK,aAAa,cAAc,SAAU;AAC1C,SAAO,aAAa,cAAc,QAAQ;AAC1C,yBAAuB,UAAU,iBAAiB,IAAI,QAAQ,SAAS;AACvE,gBAAc,UAAU,OAAO,WAAW,aAAa,UAAU;CAClE,GACD,CAAC,WAAY,EACd;AACD,cAAM,UAAU,MAAM;EACpB,MAAM,WAAW,QAAQ;AACzB,MAAI,UAAU;GACZ,MAAM,eAAe,MAAM;AACzB,eAAW,2BAA2B,QAAQ;AAC9C,gBAAY;GACb;GACD,MAAM,cAAc,MAAM;IACxB,MAAM,cAAc,iBAAiB,IAAI,QAAQ,SAAS,GAAG,uBAAuB;AACpF,+BAA2B,UAAU,2BAA2B,UAAU;AAC1E,WAAO,aAAa,cAAc,QAAQ;AAC1C,eAAW;GACZ;AACD,YAAS,iBAAiB,gBAAgB,YAAY;AACtD,YAAS,iBAAiB,iBAAiB,aAAa;AACxD,UAAO,MAAM;AACX,aAAS,oBAAoB,gBAAgB,YAAY;AACzD,aAAS,oBAAoB,iBAAiB,aAAa;GAC5D;EACF;CACF,GAAE;EAAC,QAAQ;EAAU;EAAU;EAAS;EAAU;CAAW,EAAC;AAC/D,cAAM,UAAU,MAAM;AACpB,MAAI,SAAS,QAAQ,iBAAiB,QAAS,YAAW,SAAS;CACpE,GAAE;EAAC;EAAM;EAAU,QAAQ;EAAkB;CAAW,EAAC;AAC1D,cAAM,UAAU,MAAM;AACpB,cAAY;AACZ,SAAO,MAAM,eAAe;CAC7B,GAAE,CAAC,YAAY,aAAc,EAAC;CAC/B,MAAM,sBAAsB,aAAM,QAAQ,MAAM;AAC9C,SAAO,OAAO,uBAAuB,KAAK,GAAG;CAC9C,GAAE,CAAC,IAAK,EAAC;AACV,MAAK,QAAQ,SAAU,QAAO;AAC9B,wBAAuB,6BAAKC,6BAAU,EAAE,UAAU,CAChD,uCAAuC,4BACrC,eACA;EACE;EACA,MAAM;EACN,aAAa,SAAS,eAAe,cAAc;EACnD,eAAe;EACf,UAAU;CACX,EACF,kBACe,4BAAI,0BAA0B;EAAE,OAAO;EAAc,SAAS;EAAa,UAAU,iBAAS,6BAC5F,4BAAI,WAAW,UAAU;GAAE,OAAO;GAAc,0BAA0B,kCAExF;IACE,SAAS;IACT,iBAAiB,qBAAqB,iBAAiB,MAAM;AAC3D,UAAK,QAAQ,+BAA+B,QAAS,cAAa;AAClE,aAAQ,+BAA+B,UAAU;IAClD,EAAC;IACF,0BAA0B,4BACxB,UAAU,IACV;KACE,MAAM;KACN,aAAa;KACb,eAAe;KACf,UAAU;KACV,cAAc,OAAO,SAAS;KAC9B,wBAAwB,QAAQ;KAChC,GAAG;KACH,KAAK;KACL,OAAO;MAAE,YAAY;MAAQ,aAAa;MAAQ,GAAG,MAAM;KAAO;KAClE,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,UAAI,MAAM,QAAQ,SAAU;AAC5B,wBAAkB,MAAM,YAAY;AACpC,WAAK,MAAM,YAAY,kBAAkB;AACvC,eAAQ,+BAA+B,UAAU;AACjD,oBAAa;MACd;KACF,EAAC;KACF,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,UAAI,MAAM,WAAW,EAAG;AACxB,sBAAgB,UAAU;OAAE,GAAG,MAAM;OAAS,GAAG,MAAM;MAAS;KACjE,EAAC;KACF,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,WAAK,gBAAgB,QAAS;MAC9B,MAAM,IAAI,MAAM,UAAU,gBAAgB,QAAQ;MAClD,MAAM,IAAI,MAAM,UAAU,gBAAgB,QAAQ;MAClD,MAAM,sBAAsB,QAAQ,cAAc,QAAQ;MAC1D,MAAM,oBAAoB,CAAC,QAAQ,OAAQ,EAAC,SAAS,QAAQ,eAAe;MAC5E,MAAM,QAAQ,CAAC,QAAQ,IAAK,EAAC,SAAS,QAAQ,eAAe,GAAG,KAAK,MAAM,KAAK;MAChF,MAAM,WAAW,oBAAoB,MAAM,GAAG,EAAE,GAAG;MACnD,MAAM,YAAY,oBAAoB,MAAM,GAAG,EAAE,GAAG;MACpD,MAAM,kBAAkB,MAAM,gBAAgB,UAAU,KAAK;MAC7D,MAAM,QAAQ;OAAE,GAAG;OAAU,GAAG;MAAU;MAC1C,MAAM,cAAc;OAAE,eAAe;OAAO;MAAO;AACnD,UAAI,qBAAqB;AACvB,qBAAc,UAAU;AACxB,oCAA6B,kBAAkB,aAAa,aAAa,EACvE,UAAU,MACX,EAAC;MACH,WAAU,mBAAmB,OAAO,QAAQ,gBAAgB,gBAAgB,EAAE;AAC7E,qBAAc,UAAU;AACxB,oCAA6B,mBAAmB,cAAc,aAAa,EACzE,UAAU,MACX,EAAC;AACF,aAAM,OAAO,kBAAkB,MAAM,UAAU;MAChD,WAAU,KAAK,IAAI,EAAE,GAAG,mBAAmB,KAAK,IAAI,EAAE,GAAG,gBACxD,iBAAgB,UAAU;KAE7B,EAAC;KACF,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;MAC9D,MAAM,QAAQ,cAAc;MAC5B,MAAM,SAAS,MAAM;AACrB,UAAI,OAAO,kBAAkB,MAAM,UAAU,CAC3C,QAAO,sBAAsB,MAAM,UAAU;AAE/C,oBAAc,UAAU;AACxB,sBAAgB,UAAU;AAC1B,UAAI,OAAO;OACT,MAAM,QAAQ,MAAM;OACpB,MAAM,cAAc;QAAE,eAAe;QAAO;OAAO;AACnD,WAAI,mBAAmB,OAAO,QAAQ,gBAAgB,QAAQ,eAAe,CAC3E,8BAA6B,iBAAiB,YAAY,aAAa,EACrE,UAAU,KACX,EAAC;WAEF,8BACE,oBACA,eACA,aACA,EACE,UAAU,KACX,EACF;AAEH,aAAM,iBAAiB,SAAS,CAAC,WAAW,OAAO,gBAAgB,EAAE,EACnE,MAAM,KACP,EAAC;MACH;KACF,EAAC;IACH,EACF;GACF,EACF;EAAE,EAAC,EACJ,QAAQ,SACT;CAAE,EAAC,AACL,EAAE,EAAC;AACL,EACF;AACD,IAAI,gBAAgB,CAAC,UAAU;CAC7B,MAAM,EAAE,cAAc,SAAU,GAAG,eAAe,GAAG;CACrD,MAAM,UAAU,wBAAwB,YAAY,aAAa;CACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,aAAM,SAAS,MAAM;CACzE,MAAM,CAAC,aAAa,eAAe,GAAG,aAAM,SAAS,MAAM;AAC3D,cAAa,MAAM,sBAAsB,KAAK,CAAC;AAC/C,cAAM,UAAU,MAAM;EACpB,MAAM,QAAQ,OAAO,WAAW,MAAM,eAAe,KAAK,EAAE,IAAI;AAChE,SAAO,MAAM,OAAO,aAAa,MAAM;CACxC,GAAE,CAAE,EAAC;AACN,QAAO,cAAc,uBAAuB,4BAAI,QAAQ;EAAE,SAAS;EAAM,0BAA0B,4BAAI,gBAAgB;GAAE,GAAG;GAAe,UAAU,sCAAsC,6BAAKA,6BAAU,EAAE,UAAU;IACpN,QAAQ;IACR;IACA;GACD,EAAE,EAAC;EAAE,EAAC;CAAE,EAAC;AACX;AACD,IAAI,aAAa;AACjB,IAAI,aAAa,aAAM,WACrB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,aAAc,GAAG,YAAY,GAAG;AACxC,wBAAuB,4BAAI,UAAU,KAAK;EAAE,GAAG;EAAY,KAAK;CAAc,EAAC;AAChF,EACF;AACD,WAAW,cAAc;AACzB,IAAI,mBAAmB;AACvB,IAAI,mBAAmB,aAAM,WAC3B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,aAAc,GAAG,kBAAkB,GAAG;AAC9C,wBAAuB,4BAAI,UAAU,KAAK;EAAE,GAAG;EAAkB,KAAK;CAAc,EAAC;AACtF,EACF;AACD,iBAAiB,cAAc;AAC/B,IAAI,cAAc;AAClB,IAAI,cAAc,aAAM,WACtB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,QAAS,GAAG,aAAa,GAAG;AACpC,MAAK,QAAQ,MAAM,EAAE;AACnB,UAAQ,OACL,yCAAyC,YAAY,oCACvD;AACD,SAAO;CACR;AACD,wBAAuB,4BAAI,sBAAsB;EAAE;EAAS,SAAS;EAAM,0BAA0B,4BAAI,YAAY;GAAE,GAAG;GAAa,KAAK;EAAc,EAAC;CAAE,EAAC;AAC/J,EACF;AACD,YAAY,cAAc;AAC1B,IAAI,aAAa;AACjB,IAAI,aAAa,aAAM,WACrB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,aAAc,GAAG,YAAY,GAAG;CACxC,MAAM,qBAAqB,2BAA2B,YAAY,aAAa;AAC/E,wBAAuB,4BAAI,sBAAsB;EAAE,SAAS;EAAM,0BAA0B,4BAC1F,UAAU,QACV;GACE,MAAM;GACN,GAAG;GACH,KAAK;GACL,SAAS,qBAAqB,MAAM,SAAS,mBAAmB,QAAQ;EACzE,EACF;CAAE,EAAC;AACL,EACF;AACD,WAAW,cAAc;AACzB,IAAI,uBAAuB,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACnE,MAAM,EAAE,cAAc,QAAS,GAAG,sBAAsB,GAAG;AAC3D,wBAAuB,4BACrB,UAAU,KACV;EACE,qCAAqC;EACrC,iCAAiC,gBAAgB;EACjD,GAAG;EACH,KAAK;CACN,EACF;AACF,EAAC;AACF,SAAS,uBAAuB,WAAW;CACzC,MAAM,cAAc,CAAE;CACtB,MAAM,aAAa,MAAM,KAAK,UAAU,WAAW;AACnD,YAAW,QAAQ,CAAC,SAAS;AAC3B,MAAI,KAAK,aAAa,KAAK,aAAa,KAAK,YAAa,aAAY,KAAK,KAAK,YAAY;AAC5F,MAAI,cAAc,KAAK,EAAE;GACvB,MAAM,WAAW,KAAK,cAAc,KAAK,UAAU,KAAK,MAAM,YAAY;GAC1E,MAAM,aAAa,KAAK,QAAQ,8BAA8B;AAC9D,QAAK,SACH,KAAI,YAAY;IACd,MAAM,UAAU,KAAK,QAAQ;AAC7B,QAAI,QAAS,aAAY,KAAK,QAAQ;GACvC,MACC,aAAY,KAAK,GAAG,uBAAuB,KAAK,CAAC;EAGtD;CACF,EAAC;AACF,QAAO;AACR;AACD,SAAS,6BAA6B,MAAM,SAAS,QAAQ,EAAE,UAAU,EAAE;CACzE,MAAM,gBAAgB,OAAO,cAAc;CAC3C,MAAM,QAAQ,IAAI,YAAY,MAAM;EAAE,SAAS;EAAM,YAAY;EAAM;CAAQ;AAC/E,KAAI,QAAS,eAAc,iBAAiB,MAAM,SAAS,EAAE,MAAM,KAAM,EAAC;AAC1E,KAAI,SACF,6BAA4B,eAAe,MAAM;KAEjD,eAAc,cAAc,MAAM;AAErC;AACD,IAAI,qBAAqB,CAAC,OAAO,WAAW,YAAY,MAAM;CAC5D,MAAM,SAAS,KAAK,IAAI,MAAM,EAAE;CAChC,MAAM,SAAS,KAAK,IAAI,MAAM,EAAE;CAChC,MAAM,WAAW,SAAS;AAC1B,KAAI,cAAc,UAAU,cAAc,QACxC,QAAO,YAAY,SAAS;KAE5B,SAAQ,YAAY,SAAS;AAEhC;AACD,SAAS,aAAa,WAAW,MAAM,CACtC,GAAE;CACD,MAAM,KAAK,eAAe,SAAS;AACnC,kBAAgB,MAAM;EACpB,IAAI,OAAO;EACX,IAAI,OAAO;AACX,SAAO,OAAO,sBAAsB,MAAM,OAAO,OAAO,sBAAsB,GAAG,CAAC;AAClF,SAAO,MAAM;AACX,UAAO,qBAAqB,KAAK;AACjC,UAAO,qBAAqB,KAAK;EAClC;CACF,GAAE,CAAC,EAAG,EAAC;AACT;AACD,SAAS,cAAc,MAAM;AAC3B,QAAO,KAAK,aAAa,KAAK;AAC/B;AACD,SAAS,sBAAsB,WAAW;CACxC,MAAM,QAAQ,CAAE;CAChB,MAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc,EAC3E,YAAY,CAAC,SAAS;EACpB,MAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,MAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AACrE,SAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;CACnE,EACF,EAAC;AACF,QAAO,OAAO,UAAU,CAAE,OAAM,KAAK,OAAO,YAAY;AACxD,QAAO;AACR;AACD,SAAS,WAAW,YAAY;CAC9B,MAAM,2BAA2B,SAAS;AAC1C,QAAO,WAAW,KAAK,CAAC,cAAc;AACpC,MAAI,cAAc,yBAA0B,QAAO;AACnD,YAAU,OAAO;AACjB,SAAO,SAAS,kBAAkB;CACnC,EAAC;AACH;AACD,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,SAAS;AACb,IAAI,QAAQ"}