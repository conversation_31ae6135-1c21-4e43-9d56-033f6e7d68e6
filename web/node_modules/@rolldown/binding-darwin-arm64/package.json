{"name": "@rolldown/binding-darwin-arm64", "version": "1.0.0-beta.21", "cpu": ["arm64"], "main": "rolldown-binding.darwin-arm64.node", "files": ["rolldown-binding.darwin-arm64.node"], "description": "Fast JavaScript/TypeScript bundler in Rust with Rollup-compatible API.", "keywords": ["webpack", "parcel", "esbuild", "rollup", "bundler", "rolldown"], "homepage": "https://rolldown.rs/", "license": "MIT", "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "https://github.com/rolldown/rolldown.git", "directory": "packages/rolldown"}, "os": ["darwin"]}