{"version": 3, "file": "dist-CzHXh4Qo.js", "names": ["React", "React"], "sources": ["../../@radix-ui/react-use-effect-event/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../@radix-ui/react-use-effect-event/dist/index.mjs"], "sourcesContent": ["// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-effect-event.tsx\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport * as React from \"react\";\nvar useReactEffectEvent = React[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = React[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = React.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    useLayoutEffect(() => {\n      ref.current = callback;\n    });\n  }\n  return React.useMemo(() => (...args) => ref.current?.(...args), []);\n}\nexport {\n  useEffectEvent\n};\n//# sourceMappingURL=index.mjs.map\n"], "x_google_ignoreList": [0], "mappings": ";;;;;AAEA,IAAI,mBAAmB,YAAY,WAAWA,aAAM,kBAAkB,MAAM,CAC3E;;;;ACAD,IAAI,sBAAsBC,aAAM,mBAAmB,MAAM,CAAC,UAAU;AACpE,IAAI,0BAA0BA,aAAM,uBAAuB,MAAM,CAAC,UAAU"}