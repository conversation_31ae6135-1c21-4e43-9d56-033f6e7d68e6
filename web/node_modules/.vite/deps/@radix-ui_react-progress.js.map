{"version": 3, "file": "@radix-ui_react-progress.js", "names": [], "sources": ["../../@radix-ui/react-progress/node_modules/@radix-ui/react-context/dist/index.mjs", "../../@radix-ui/react-progress/node_modules/@radix-ui/react-primitive/dist/index.mjs", "../../@radix-ui/react-progress/dist/index.mjs"], "sourcesContent": ["// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/progress.tsx\nimport * as React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PROGRESS_NAME = \"Progress\";\nvar DEFAULT_MAX = 100;\nvar [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\nvar [ProgressProvider, useProgressContext] = createProgressContext(PROGRESS_NAME);\nvar Progress = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, \"Progress\"));\n    }\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, \"Progress\"));\n    }\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : void 0;\n    return /* @__PURE__ */ jsx(ProgressProvider, { scope: __scopeProgress, value, max, children: /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"aria-valuemax\": max,\n        \"aria-valuemin\": 0,\n        \"aria-valuenow\": isNumber(value) ? value : void 0,\n        \"aria-valuetext\": valueLabel,\n        role: \"progressbar\",\n        \"data-state\": getProgressState(value, max),\n        \"data-value\": value ?? void 0,\n        \"data-max\": max,\n        ...progressProps,\n        ref: forwardedRef\n      }\n    ) });\n  }\n);\nProgress.displayName = PROGRESS_NAME;\nvar INDICATOR_NAME = \"ProgressIndicator\";\nvar ProgressIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"data-state\": getProgressState(context.value, context.max),\n        \"data-value\": context.value ?? void 0,\n        \"data-max\": context.max,\n        ...indicatorProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nProgressIndicator.displayName = INDICATOR_NAME;\nfunction defaultGetValueLabel(value, max) {\n  return `${Math.round(value / max * 100)}%`;\n}\nfunction getProgressState(value, maxValue) {\n  return value == null ? \"indeterminate\" : value === maxValue ? \"complete\" : \"loading\";\n}\nfunction isNumber(value) {\n  return typeof value === \"number\";\n}\nfunction isValidMaxNumber(max) {\n  return isNumber(max) && !isNaN(max) && max > 0;\n}\nfunction isValidValueNumber(value, max) {\n  return isNumber(value) && !isNaN(value) && value <= max && value >= 0;\n}\nfunction getInvalidMaxError(propValue, componentName) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\nfunction getInvalidValueError(propValue, componentName) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\nvar Root = Progress;\nvar Indicator = ProgressIndicator;\nexport {\n  Indicator,\n  Progress,\n  ProgressIndicator,\n  Root,\n  createProgressScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "x_google_ignoreList": [0, 1], "mappings": ";;;;;;;;;;;;AAmBA,SAAS,mBAAmB,WAAW,yBAAyB,CAAE,GAAE;CAClE,IAAI,kBAAkB,CAAE;CACxB,SAAS,eAAe,mBAAmB,gBAAgB;EACzD,MAAM,cAAc,aAAM,cAAc,eAAe;EACvD,MAAM,QAAQ,gBAAgB;AAC9B,oBAAkB,CAAC,GAAG,iBAAiB,cAAe;EACtD,MAAM,WAAW,CAAC,UAAU;GAC1B,MAAM,EAAE,OAAO,SAAU,GAAG,SAAS,GAAG;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,QAAQ,aAAM,QAAQ,MAAM,SAAS,OAAO,OAAO,QAAQ,CAAC;AAClE,0BAAuB,4BAAI,QAAQ,UAAU;IAAE;IAAO;GAAU,EAAC;EAClE;AACD,WAAS,cAAc,oBAAoB;EAC3C,SAAS,YAAY,cAAc,OAAO;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,UAAU,aAAM,WAAW,QAAQ;AACzC,OAAI,QAAS,QAAO;AACpB,OAAI,wBAAwB,EAAG,QAAO;AACtC,SAAM,IAAI,OAAO,IAAI,aAAa,2BAA2B,kBAAkB;EAChF;AACD,SAAO,CAAC,UAAU,WAAY;CAC/B;CACD,MAAM,cAAc,MAAM;EACxB,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,UAAO,aAAM,cAAc,eAAe;EAC3C,EAAC;AACF,SAAO,SAAS,SAAS,OAAO;GAC9B,MAAM,WAAW,QAAQ,cAAc;AACvC,UAAO,aAAM,QACX,OAAO,IAAI,SAAS,cAAc;IAAE,GAAG;KAAQ,YAAY;GAAU,EAAE,IACvE,CAAC,OAAO,QAAS,EAClB;EACF;CACF;AACD,aAAY,YAAY;AACxB,QAAO,CAAC,gBAAgB,qBAAqB,aAAa,GAAG,uBAAuB,AAAC;AACtF;AACD,SAAS,qBAAqB,GAAG,QAAQ;CACvC,MAAM,YAAY,OAAO;AACzB,KAAI,OAAO,WAAW,EAAG,QAAO;CAChC,MAAM,cAAc,MAAM;EACxB,MAAM,aAAa,OAAO,IAAI,CAAC,kBAAkB;GAC/C,UAAU,cAAc;GACxB,WAAW,aAAa;EACzB,GAAE;AACH,SAAO,SAAS,kBAAkB,gBAAgB;GAChD,MAAM,aAAa,WAAW,OAAO,CAAC,aAAa,EAAE,UAAU,WAAW,KAAK;IAC7E,MAAM,aAAa,SAAS,eAAe;IAC3C,MAAM,eAAe,YAAY,SAAS;AAC1C,WAAO;KAAE,GAAG;KAAa,GAAG;IAAc;GAC3C,GAAE,CAAE,EAAC;AACN,UAAO,aAAM,QAAQ,OAAO,IAAI,SAAS,UAAU,cAAc,WAAY,IAAG,CAAC,UAAW,EAAC;EAC9F;CACF;AACD,aAAY,YAAY,UAAU;AAClC,QAAO;AACR;;;;;ACtED,IAAI,QAAQ;CACV;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACD;AACD,IAAI,YAAY,MAAM,OAAO,CAAC,WAAW,SAAS;CAChD,MAAM,OAAO,YAAY,YAAY,OAAO;CAC5C,MAAM,OAAO,aAAM,WAAW,CAAC,OAAO,iBAAiB;EACrD,MAAM,EAAE,QAAS,GAAG,gBAAgB,GAAG;EACvC,MAAM,OAAO,UAAU,OAAO;AAC9B,aAAW,WAAW,YACpB,QAAO,OAAO,IAAI,WAAW,IAAI;AAEnC,yBAAuB,4BAAI,MAAM;GAAE,GAAG;GAAgB,KAAK;EAAc,EAAC;CAC3E,EAAC;AACF,MAAK,eAAe,YAAY;AAChC,QAAO;EAAE,GAAG;GAAY,OAAO;CAAM;AACtC,GAAE,CAAE,EAAC;;;;AC7BN,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,CAAC,uBAAuB,oBAAoB,GAAG,mBAAmB,cAAc;AACpF,IAAI,CAAC,kBAAkB,mBAAmB,GAAG,sBAAsB,cAAc;AACjF,IAAI,WAAW,aAAM,WACnB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,iBACA,OAAO,YAAY,MACnB,KAAK,SACL,gBAAgB,qBAChB,GAAG,eACJ,GAAG;AACJ,MAAK,WAAW,YAAY,OAAO,iBAAiB,QAAQ,CAC1D,SAAQ,MAAM,sBAAsB,WAAW,WAAW,CAAC;CAE7D,MAAM,MAAM,iBAAiB,QAAQ,GAAG,UAAU;AAClD,KAAI,cAAc,SAAS,mBAAmB,WAAW,IAAI,CAC3D,SAAQ,MAAM,wBAAwB,aAAa,WAAW,CAAC;CAEjE,MAAM,QAAQ,mBAAmB,WAAW,IAAI,GAAG,YAAY;CAC/D,MAAM,aAAa,SAAS,MAAM,GAAG,cAAc,OAAO,IAAI,QAAQ;AACtE,wBAAuB,4BAAI,kBAAkB;EAAE,OAAO;EAAiB;EAAO;EAAK,0BAA0B,4BAC3G,UAAU,KACV;GACE,iBAAiB;GACjB,iBAAiB;GACjB,iBAAiB,SAAS,MAAM,GAAG,aAAa;GAChD,kBAAkB;GAClB,MAAM;GACN,cAAc,iBAAiB,OAAO,IAAI;GAC1C,cAAc,cAAc;GAC5B,YAAY;GACZ,GAAG;GACH,KAAK;EACN,EACF;CAAE,EAAC;AACL,EACF;AACD,SAAS,cAAc;AACvB,IAAI,iBAAiB;AACrB,IAAI,oBAAoB,aAAM,WAC5B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,gBAAiB,GAAG,gBAAgB,GAAG;CAC/C,MAAM,UAAU,mBAAmB,gBAAgB,gBAAgB;AACnE,wBAAuB,4BACrB,UAAU,KACV;EACE,cAAc,iBAAiB,QAAQ,OAAO,QAAQ,IAAI;EAC1D,cAAc,QAAQ,cAAc;EACpC,YAAY,QAAQ;EACpB,GAAG;EACH,KAAK;CACN,EACF;AACF,EACF;AACD,kBAAkB,cAAc;AAChC,SAAS,qBAAqB,OAAO,KAAK;AACxC,WAAU,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC;AACzC;AACD,SAAS,iBAAiB,OAAO,UAAU;AACzC,QAAO,SAAS,OAAO,kBAAkB,UAAU,WAAW,aAAa;AAC5E;AACD,SAAS,SAAS,OAAO;AACvB,eAAc,UAAU;AACzB;AACD,SAAS,iBAAiB,KAAK;AAC7B,QAAO,SAAS,IAAI,KAAK,MAAM,IAAI,IAAI,MAAM;AAC9C;AACD,SAAS,mBAAmB,OAAO,KAAK;AACtC,QAAO,SAAS,MAAM,KAAK,MAAM,MAAM,IAAI,SAAS,OAAO,SAAS;AACrE;AACD,SAAS,mBAAmB,WAAW,eAAe;AACpD,SAAQ,kCAAkC,UAAU,mBAAmB,cAAc,wEAAwE,YAAY;AAC1K;AACD,SAAS,qBAAqB,WAAW,eAAe;AACtD,SAAQ,oCAAoC,UAAU,mBAAmB,cAAc;;gDAEzC,YAAY;;;;AAI3D;AACD,IAAI,OAAO;AACX,IAAI,YAAY"}