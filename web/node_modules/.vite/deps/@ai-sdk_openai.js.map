{"version": 3, "file": "@ai-sdk_openai.js", "names": ["UnsupportedFunctionalityError2", "UnsupportedFunctionalityError3", "UnsupportedFunctionalityError4", "UnsupportedFunctionalityError5", "UnsupportedFunctionalityError6", "UnsupportedFunctionalityError7"], "sources": ["../../@ai-sdk/openai/dist/index.mjs"], "sourcesContent": ["// src/openai-provider.ts\nimport {\n  loadApi<PERSON><PERSON>,\n  withoutTrailingSlash\n} from \"@ai-sdk/provider-utils\";\n\n// src/openai-chat-language-model.ts\nimport {\n  InvalidResponseDataError,\n  UnsupportedFunctionalityError as UnsupportedFunctionalityError3\n} from \"@ai-sdk/provider\";\nimport {\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  generateId,\n  isParsable<PERSON>son,\n  postJsonToApi\n} from \"@ai-sdk/provider-utils\";\nimport { z as z2 } from \"zod\";\n\n// src/convert-to-openai-chat-messages.ts\nimport {\n  UnsupportedFunctionalityError\n} from \"@ai-sdk/provider\";\nimport { convertUint8ArrayToBase64 } from \"@ai-sdk/provider-utils\";\nfunction convertToOpenAIChatMessages({\n  prompt,\n  useLegacyFunctionCalling = false,\n  systemMessageMode = \"system\"\n}) {\n  const messages = [];\n  const warnings = [];\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        switch (systemMessageMode) {\n          case \"system\": {\n            messages.push({ role: \"system\", content });\n            break;\n          }\n          case \"developer\": {\n            messages.push({ role: \"developer\", content });\n            break;\n          }\n          case \"remove\": {\n            warnings.push({\n              type: \"other\",\n              message: \"system messages are removed for this model\"\n            });\n            break;\n          }\n          default: {\n            const _exhaustiveCheck = systemMessageMode;\n            throw new Error(\n              `Unsupported system message mode: ${_exhaustiveCheck}`\n            );\n          }\n        }\n        break;\n      }\n      case \"user\": {\n        if (content.length === 1 && content[0].type === \"text\") {\n          messages.push({ role: \"user\", content: content[0].text });\n          break;\n        }\n        messages.push({\n          role: \"user\",\n          content: content.map((part, index) => {\n            var _a, _b, _c, _d;\n            switch (part.type) {\n              case \"text\": {\n                return { type: \"text\", text: part.text };\n              }\n              case \"image\": {\n                return {\n                  type: \"image_url\",\n                  image_url: {\n                    url: part.image instanceof URL ? part.image.toString() : `data:${(_a = part.mimeType) != null ? _a : \"image/jpeg\"};base64,${convertUint8ArrayToBase64(part.image)}`,\n                    // OpenAI specific extension: image detail\n                    detail: (_c = (_b = part.providerMetadata) == null ? void 0 : _b.openai) == null ? void 0 : _c.imageDetail\n                  }\n                };\n              }\n              case \"file\": {\n                if (part.data instanceof URL) {\n                  throw new UnsupportedFunctionalityError({\n                    functionality: \"'File content parts with URL data' functionality not supported.\"\n                  });\n                }\n                switch (part.mimeType) {\n                  case \"audio/wav\": {\n                    return {\n                      type: \"input_audio\",\n                      input_audio: { data: part.data, format: \"wav\" }\n                    };\n                  }\n                  case \"audio/mp3\":\n                  case \"audio/mpeg\": {\n                    return {\n                      type: \"input_audio\",\n                      input_audio: { data: part.data, format: \"mp3\" }\n                    };\n                  }\n                  case \"application/pdf\": {\n                    return {\n                      type: \"file\",\n                      file: {\n                        filename: (_d = part.filename) != null ? _d : `part-${index}.pdf`,\n                        file_data: `data:application/pdf;base64,${part.data}`\n                      }\n                    };\n                  }\n                  default: {\n                    throw new UnsupportedFunctionalityError({\n                      functionality: `File content part type ${part.mimeType} in user messages`\n                    });\n                  }\n                }\n              }\n            }\n          })\n        });\n        break;\n      }\n      case \"assistant\": {\n        let text = \"\";\n        const toolCalls = [];\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              text += part.text;\n              break;\n            }\n            case \"tool-call\": {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: \"function\",\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args)\n                }\n              });\n              break;\n            }\n          }\n        }\n        if (useLegacyFunctionCalling) {\n          if (toolCalls.length > 1) {\n            throw new UnsupportedFunctionalityError({\n              functionality: \"useLegacyFunctionCalling with multiple tool calls in one message\"\n            });\n          }\n          messages.push({\n            role: \"assistant\",\n            content: text,\n            function_call: toolCalls.length > 0 ? toolCalls[0].function : void 0\n          });\n        } else {\n          messages.push({\n            role: \"assistant\",\n            content: text,\n            tool_calls: toolCalls.length > 0 ? toolCalls : void 0\n          });\n        }\n        break;\n      }\n      case \"tool\": {\n        for (const toolResponse of content) {\n          if (useLegacyFunctionCalling) {\n            messages.push({\n              role: \"function\",\n              name: toolResponse.toolName,\n              content: JSON.stringify(toolResponse.result)\n            });\n          } else {\n            messages.push({\n              role: \"tool\",\n              tool_call_id: toolResponse.toolCallId,\n              content: JSON.stringify(toolResponse.result)\n            });\n          }\n        }\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return { messages, warnings };\n}\n\n// src/map-openai-chat-logprobs.ts\nfunction mapOpenAIChatLogProbsOutput(logprobs) {\n  var _a, _b;\n  return (_b = (_a = logprobs == null ? void 0 : logprobs.content) == null ? void 0 : _a.map(({ token, logprob, top_logprobs }) => ({\n    token,\n    logprob,\n    topLogprobs: top_logprobs ? top_logprobs.map(({ token: token2, logprob: logprob2 }) => ({\n      token: token2,\n      logprob: logprob2\n    })) : []\n  }))) != null ? _b : void 0;\n}\n\n// src/map-openai-finish-reason.ts\nfunction mapOpenAIFinishReason(finishReason) {\n  switch (finishReason) {\n    case \"stop\":\n      return \"stop\";\n    case \"length\":\n      return \"length\";\n    case \"content_filter\":\n      return \"content-filter\";\n    case \"function_call\":\n    case \"tool_calls\":\n      return \"tool-calls\";\n    default:\n      return \"unknown\";\n  }\n}\n\n// src/openai-error.ts\nimport { z } from \"zod\";\nimport { createJsonErrorResponseHandler } from \"@ai-sdk/provider-utils\";\nvar openaiErrorDataSchema = z.object({\n  error: z.object({\n    message: z.string(),\n    // The additional information below is handled loosely to support\n    // OpenAI-compatible providers that have slightly different error\n    // responses:\n    type: z.string().nullish(),\n    param: z.any().nullish(),\n    code: z.union([z.string(), z.number()]).nullish()\n  })\n});\nvar openaiFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: openaiErrorDataSchema,\n  errorToMessage: (data) => data.error.message\n});\n\n// src/get-response-metadata.ts\nfunction getResponseMetadata({\n  id,\n  model,\n  created\n}) {\n  return {\n    id: id != null ? id : void 0,\n    modelId: model != null ? model : void 0,\n    timestamp: created != null ? new Date(created * 1e3) : void 0\n  };\n}\n\n// src/openai-prepare-tools.ts\nimport {\n  UnsupportedFunctionalityError as UnsupportedFunctionalityError2\n} from \"@ai-sdk/provider\";\nfunction prepareTools({\n  mode,\n  useLegacyFunctionCalling = false,\n  structuredOutputs\n}) {\n  var _a;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  const toolWarnings = [];\n  if (tools == null) {\n    return { tools: void 0, tool_choice: void 0, toolWarnings };\n  }\n  const toolChoice = mode.toolChoice;\n  if (useLegacyFunctionCalling) {\n    const openaiFunctions = [];\n    for (const tool of tools) {\n      if (tool.type === \"provider-defined\") {\n        toolWarnings.push({ type: \"unsupported-tool\", tool });\n      } else {\n        openaiFunctions.push({\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters\n        });\n      }\n    }\n    if (toolChoice == null) {\n      return {\n        functions: openaiFunctions,\n        function_call: void 0,\n        toolWarnings\n      };\n    }\n    const type2 = toolChoice.type;\n    switch (type2) {\n      case \"auto\":\n      case \"none\":\n      case void 0:\n        return {\n          functions: openaiFunctions,\n          function_call: void 0,\n          toolWarnings\n        };\n      case \"required\":\n        throw new UnsupportedFunctionalityError2({\n          functionality: \"useLegacyFunctionCalling and toolChoice: required\"\n        });\n      default:\n        return {\n          functions: openaiFunctions,\n          function_call: { name: toolChoice.toolName },\n          toolWarnings\n        };\n    }\n  }\n  const openaiTools2 = [];\n  for (const tool of tools) {\n    if (tool.type === \"provider-defined\") {\n      toolWarnings.push({ type: \"unsupported-tool\", tool });\n    } else {\n      openaiTools2.push({\n        type: \"function\",\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: structuredOutputs ? true : void 0\n        }\n      });\n    }\n  }\n  if (toolChoice == null) {\n    return { tools: openaiTools2, tool_choice: void 0, toolWarnings };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n    case \"required\":\n      return { tools: openaiTools2, tool_choice: type, toolWarnings };\n    case \"tool\":\n      return {\n        tools: openaiTools2,\n        tool_choice: {\n          type: \"function\",\n          function: {\n            name: toolChoice.toolName\n          }\n        },\n        toolWarnings\n      };\n    default: {\n      const _exhaustiveCheck = type;\n      throw new UnsupportedFunctionalityError2({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`\n      });\n    }\n  }\n}\n\n// src/openai-chat-language-model.ts\nvar OpenAIChatLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get supportsStructuredOutputs() {\n    var _a;\n    return (_a = this.settings.structuredOutputs) != null ? _a : isReasoningModel(this.modelId);\n  }\n  get defaultObjectGenerationMode() {\n    if (isAudioModel(this.modelId)) {\n      return \"tool\";\n    }\n    return this.supportsStructuredOutputs ? \"json\" : \"tool\";\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get supportsImageUrls() {\n    return !this.settings.downloadImages;\n  }\n  getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata\n  }) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if ((responseFormat == null ? void 0 : responseFormat.type) === \"json\" && responseFormat.schema != null && !this.supportsStructuredOutputs) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format schema is only supported with structuredOutputs\"\n      });\n    }\n    const useLegacyFunctionCalling = this.settings.useLegacyFunctionCalling;\n    if (useLegacyFunctionCalling && this.settings.parallelToolCalls === true) {\n      throw new UnsupportedFunctionalityError3({\n        functionality: \"useLegacyFunctionCalling with parallelToolCalls\"\n      });\n    }\n    if (useLegacyFunctionCalling && this.supportsStructuredOutputs) {\n      throw new UnsupportedFunctionalityError3({\n        functionality: \"structuredOutputs with useLegacyFunctionCalling\"\n      });\n    }\n    const { messages, warnings: messageWarnings } = convertToOpenAIChatMessages(\n      {\n        prompt,\n        useLegacyFunctionCalling,\n        systemMessageMode: getSystemMessageMode(this.modelId)\n      }\n    );\n    warnings.push(...messageWarnings);\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs: this.settings.logprobs === true || typeof this.settings.logprobs === \"number\" ? true : void 0,\n      top_logprobs: typeof this.settings.logprobs === \"number\" ? this.settings.logprobs : typeof this.settings.logprobs === \"boolean\" ? this.settings.logprobs ? 0 : void 0 : void 0,\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      response_format: (responseFormat == null ? void 0 : responseFormat.type) === \"json\" ? this.supportsStructuredOutputs && responseFormat.schema != null ? {\n        type: \"json_schema\",\n        json_schema: {\n          schema: responseFormat.schema,\n          strict: true,\n          name: (_a = responseFormat.name) != null ? _a : \"response\",\n          description: responseFormat.description\n        }\n      } : { type: \"json_object\" } : void 0,\n      stop: stopSequences,\n      seed,\n      // openai specific settings:\n      // TODO remove in next major version; we auto-map maxTokens now\n      max_completion_tokens: (_b = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _b.maxCompletionTokens,\n      store: (_c = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _c.store,\n      metadata: (_d = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _d.metadata,\n      prediction: (_e = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _e.prediction,\n      reasoning_effort: (_g = (_f = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _f.reasoningEffort) != null ? _g : this.settings.reasoningEffort,\n      // messages:\n      messages\n    };\n    if (isReasoningModel(this.modelId)) {\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"temperature\",\n          details: \"temperature is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.top_p != null) {\n        baseArgs.top_p = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"topP\",\n          details: \"topP is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.frequency_penalty != null) {\n        baseArgs.frequency_penalty = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"frequencyPenalty\",\n          details: \"frequencyPenalty is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.presence_penalty != null) {\n        baseArgs.presence_penalty = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"presencePenalty\",\n          details: \"presencePenalty is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.logit_bias != null) {\n        baseArgs.logit_bias = void 0;\n        warnings.push({\n          type: \"other\",\n          message: \"logitBias is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.logprobs != null) {\n        baseArgs.logprobs = void 0;\n        warnings.push({\n          type: \"other\",\n          message: \"logprobs is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.top_logprobs != null) {\n        baseArgs.top_logprobs = void 0;\n        warnings.push({\n          type: \"other\",\n          message: \"topLogprobs is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.max_tokens != null) {\n        if (baseArgs.max_completion_tokens == null) {\n          baseArgs.max_completion_tokens = baseArgs.max_tokens;\n        }\n        baseArgs.max_tokens = void 0;\n      }\n    } else if (this.modelId.startsWith(\"gpt-4o-search-preview\") || this.modelId.startsWith(\"gpt-4o-mini-search-preview\")) {\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"temperature\",\n          details: \"temperature is not supported for the search preview models and has been removed.\"\n        });\n      }\n    }\n    switch (type) {\n      case \"regular\": {\n        const { tools, tool_choice, functions, function_call, toolWarnings } = prepareTools({\n          mode,\n          useLegacyFunctionCalling,\n          structuredOutputs: this.supportsStructuredOutputs\n        });\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice,\n            functions,\n            function_call\n          },\n          warnings: [...warnings, ...toolWarnings]\n        };\n      }\n      case \"object-json\": {\n        return {\n          args: {\n            ...baseArgs,\n            response_format: this.supportsStructuredOutputs && mode.schema != null ? {\n              type: \"json_schema\",\n              json_schema: {\n                schema: mode.schema,\n                strict: true,\n                name: (_h = mode.name) != null ? _h : \"response\",\n                description: mode.description\n              }\n            } : { type: \"json_object\" }\n          },\n          warnings\n        };\n      }\n      case \"object-tool\": {\n        return {\n          args: useLegacyFunctionCalling ? {\n            ...baseArgs,\n            function_call: {\n              name: mode.tool.name\n            },\n            functions: [\n              {\n                name: mode.tool.name,\n                description: mode.tool.description,\n                parameters: mode.tool.parameters\n              }\n            ]\n          } : {\n            ...baseArgs,\n            tool_choice: {\n              type: \"function\",\n              function: { name: mode.tool.name }\n            },\n            tools: [\n              {\n                type: \"function\",\n                function: {\n                  name: mode.tool.name,\n                  description: mode.tool.description,\n                  parameters: mode.tool.parameters,\n                  strict: this.supportsStructuredOutputs ? true : void 0\n                }\n              }\n            ]\n          },\n          warnings\n        };\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    const { args: body, warnings } = this.getArgs(options);\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await postJsonToApi({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        openaiChatResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = body;\n    const choice = response.choices[0];\n    const completionTokenDetails = (_a = response.usage) == null ? void 0 : _a.completion_tokens_details;\n    const promptTokenDetails = (_b = response.usage) == null ? void 0 : _b.prompt_tokens_details;\n    const providerMetadata = { openai: {} };\n    if ((completionTokenDetails == null ? void 0 : completionTokenDetails.reasoning_tokens) != null) {\n      providerMetadata.openai.reasoningTokens = completionTokenDetails == null ? void 0 : completionTokenDetails.reasoning_tokens;\n    }\n    if ((completionTokenDetails == null ? void 0 : completionTokenDetails.accepted_prediction_tokens) != null) {\n      providerMetadata.openai.acceptedPredictionTokens = completionTokenDetails == null ? void 0 : completionTokenDetails.accepted_prediction_tokens;\n    }\n    if ((completionTokenDetails == null ? void 0 : completionTokenDetails.rejected_prediction_tokens) != null) {\n      providerMetadata.openai.rejectedPredictionTokens = completionTokenDetails == null ? void 0 : completionTokenDetails.rejected_prediction_tokens;\n    }\n    if ((promptTokenDetails == null ? void 0 : promptTokenDetails.cached_tokens) != null) {\n      providerMetadata.openai.cachedPromptTokens = promptTokenDetails == null ? void 0 : promptTokenDetails.cached_tokens;\n    }\n    return {\n      text: (_c = choice.message.content) != null ? _c : void 0,\n      toolCalls: this.settings.useLegacyFunctionCalling && choice.message.function_call ? [\n        {\n          toolCallType: \"function\",\n          toolCallId: generateId(),\n          toolName: choice.message.function_call.name,\n          args: choice.message.function_call.arguments\n        }\n      ] : (_d = choice.message.tool_calls) == null ? void 0 : _d.map((toolCall) => {\n        var _a2;\n        return {\n          toolCallType: \"function\",\n          toolCallId: (_a2 = toolCall.id) != null ? _a2 : generateId(),\n          toolName: toolCall.function.name,\n          args: toolCall.function.arguments\n        };\n      }),\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: (_f = (_e = response.usage) == null ? void 0 : _e.prompt_tokens) != null ? _f : NaN,\n        completionTokens: (_h = (_g = response.usage) == null ? void 0 : _g.completion_tokens) != null ? _h : NaN\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      request: { body: JSON.stringify(body) },\n      response: getResponseMetadata(response),\n      warnings,\n      logprobs: mapOpenAIChatLogProbsOutput(choice.logprobs),\n      providerMetadata\n    };\n  }\n  async doStream(options) {\n    if (this.settings.simulateStreaming) {\n      const result = await this.doGenerate(options);\n      const simulatedStream = new ReadableStream({\n        start(controller) {\n          controller.enqueue({ type: \"response-metadata\", ...result.response });\n          if (result.text) {\n            controller.enqueue({\n              type: \"text-delta\",\n              textDelta: result.text\n            });\n          }\n          if (result.toolCalls) {\n            for (const toolCall of result.toolCalls) {\n              controller.enqueue({\n                type: \"tool-call-delta\",\n                toolCallType: \"function\",\n                toolCallId: toolCall.toolCallId,\n                toolName: toolCall.toolName,\n                argsTextDelta: toolCall.args\n              });\n              controller.enqueue({\n                type: \"tool-call\",\n                ...toolCall\n              });\n            }\n          }\n          controller.enqueue({\n            type: \"finish\",\n            finishReason: result.finishReason,\n            usage: result.usage,\n            logprobs: result.logprobs,\n            providerMetadata: result.providerMetadata\n          });\n          controller.close();\n        }\n      });\n      return {\n        stream: simulatedStream,\n        rawCall: result.rawCall,\n        rawResponse: result.rawResponse,\n        warnings: result.warnings\n      };\n    }\n    const { args, warnings } = this.getArgs(options);\n    const body = {\n      ...args,\n      stream: true,\n      // only include stream_options when in strict compatibility mode:\n      stream_options: this.config.compatibility === \"strict\" ? { include_usage: true } : void 0\n    };\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        openaiChatChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const toolCalls = [];\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: void 0,\n      completionTokens: void 0\n    };\n    let logprobs;\n    let isFirstChunk = true;\n    const { useLegacyFunctionCalling } = this.settings;\n    const providerMetadata = { openai: {} };\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n            }\n            if (value.usage != null) {\n              const {\n                prompt_tokens,\n                completion_tokens,\n                prompt_tokens_details,\n                completion_tokens_details\n              } = value.usage;\n              usage = {\n                promptTokens: prompt_tokens != null ? prompt_tokens : void 0,\n                completionTokens: completion_tokens != null ? completion_tokens : void 0\n              };\n              if ((completion_tokens_details == null ? void 0 : completion_tokens_details.reasoning_tokens) != null) {\n                providerMetadata.openai.reasoningTokens = completion_tokens_details == null ? void 0 : completion_tokens_details.reasoning_tokens;\n              }\n              if ((completion_tokens_details == null ? void 0 : completion_tokens_details.accepted_prediction_tokens) != null) {\n                providerMetadata.openai.acceptedPredictionTokens = completion_tokens_details == null ? void 0 : completion_tokens_details.accepted_prediction_tokens;\n              }\n              if ((completion_tokens_details == null ? void 0 : completion_tokens_details.rejected_prediction_tokens) != null) {\n                providerMetadata.openai.rejectedPredictionTokens = completion_tokens_details == null ? void 0 : completion_tokens_details.rejected_prediction_tokens;\n              }\n              if ((prompt_tokens_details == null ? void 0 : prompt_tokens_details.cached_tokens) != null) {\n                providerMetadata.openai.cachedPromptTokens = prompt_tokens_details == null ? void 0 : prompt_tokens_details.cached_tokens;\n              }\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.delta) == null) {\n              return;\n            }\n            const delta = choice.delta;\n            if (delta.content != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: delta.content\n              });\n            }\n            const mappedLogprobs = mapOpenAIChatLogProbsOutput(\n              choice == null ? void 0 : choice.logprobs\n            );\n            if (mappedLogprobs == null ? void 0 : mappedLogprobs.length) {\n              if (logprobs === void 0) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n            const mappedToolCalls = useLegacyFunctionCalling && delta.function_call != null ? [\n              {\n                type: \"function\",\n                id: generateId(),\n                function: delta.function_call,\n                index: 0\n              }\n            ] : delta.tool_calls;\n            if (mappedToolCalls != null) {\n              for (const toolCallDelta of mappedToolCalls) {\n                const index = toolCallDelta.index;\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== \"function\") {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`\n                    });\n                  }\n                  if (toolCallDelta.id == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`\n                    });\n                  }\n                  if (((_a = toolCallDelta.function) == null ? void 0 : _a.name) == null) {\n                    throw new InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`\n                    });\n                  }\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: \"function\",\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: (_b = toolCallDelta.function.arguments) != null ? _b : \"\"\n                    },\n                    hasFinished: false\n                  };\n                  const toolCall2 = toolCalls[index];\n                  if (((_c = toolCall2.function) == null ? void 0 : _c.name) != null && ((_d = toolCall2.function) == null ? void 0 : _d.arguments) != null) {\n                    if (toolCall2.function.arguments.length > 0) {\n                      controller.enqueue({\n                        type: \"tool-call-delta\",\n                        toolCallType: \"function\",\n                        toolCallId: toolCall2.id,\n                        toolName: toolCall2.function.name,\n                        argsTextDelta: toolCall2.function.arguments\n                      });\n                    }\n                    if (isParsableJson(toolCall2.function.arguments)) {\n                      controller.enqueue({\n                        type: \"tool-call\",\n                        toolCallType: \"function\",\n                        toolCallId: (_e = toolCall2.id) != null ? _e : generateId(),\n                        toolName: toolCall2.function.name,\n                        args: toolCall2.function.arguments\n                      });\n                      toolCall2.hasFinished = true;\n                    }\n                  }\n                  continue;\n                }\n                const toolCall = toolCalls[index];\n                if (toolCall.hasFinished) {\n                  continue;\n                }\n                if (((_f = toolCallDelta.function) == null ? void 0 : _f.arguments) != null) {\n                  toolCall.function.arguments += (_h = (_g = toolCallDelta.function) == null ? void 0 : _g.arguments) != null ? _h : \"\";\n                }\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: (_i = toolCallDelta.function.arguments) != null ? _i : \"\"\n                });\n                if (((_j = toolCall.function) == null ? void 0 : _j.name) != null && ((_k = toolCall.function) == null ? void 0 : _k.arguments) != null && isParsableJson(toolCall.function.arguments)) {\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: (_l = toolCall.id) != null ? _l : generateId(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments\n                  });\n                  toolCall.hasFinished = true;\n                }\n              }\n            }\n          },\n          flush(controller) {\n            var _a, _b;\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage: {\n                promptTokens: (_a = usage.promptTokens) != null ? _a : NaN,\n                completionTokens: (_b = usage.completionTokens) != null ? _b : NaN\n              },\n              ...providerMetadata != null ? { providerMetadata } : {}\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings\n    };\n  }\n};\nvar openaiTokenUsageSchema = z2.object({\n  prompt_tokens: z2.number().nullish(),\n  completion_tokens: z2.number().nullish(),\n  prompt_tokens_details: z2.object({\n    cached_tokens: z2.number().nullish()\n  }).nullish(),\n  completion_tokens_details: z2.object({\n    reasoning_tokens: z2.number().nullish(),\n    accepted_prediction_tokens: z2.number().nullish(),\n    rejected_prediction_tokens: z2.number().nullish()\n  }).nullish()\n}).nullish();\nvar openaiChatResponseSchema = z2.object({\n  id: z2.string().nullish(),\n  created: z2.number().nullish(),\n  model: z2.string().nullish(),\n  choices: z2.array(\n    z2.object({\n      message: z2.object({\n        role: z2.literal(\"assistant\").nullish(),\n        content: z2.string().nullish(),\n        function_call: z2.object({\n          arguments: z2.string(),\n          name: z2.string()\n        }).nullish(),\n        tool_calls: z2.array(\n          z2.object({\n            id: z2.string().nullish(),\n            type: z2.literal(\"function\"),\n            function: z2.object({\n              name: z2.string(),\n              arguments: z2.string()\n            })\n          })\n        ).nullish()\n      }),\n      index: z2.number(),\n      logprobs: z2.object({\n        content: z2.array(\n          z2.object({\n            token: z2.string(),\n            logprob: z2.number(),\n            top_logprobs: z2.array(\n              z2.object({\n                token: z2.string(),\n                logprob: z2.number()\n              })\n            )\n          })\n        ).nullable()\n      }).nullish(),\n      finish_reason: z2.string().nullish()\n    })\n  ),\n  usage: openaiTokenUsageSchema\n});\nvar openaiChatChunkSchema = z2.union([\n  z2.object({\n    id: z2.string().nullish(),\n    created: z2.number().nullish(),\n    model: z2.string().nullish(),\n    choices: z2.array(\n      z2.object({\n        delta: z2.object({\n          role: z2.enum([\"assistant\"]).nullish(),\n          content: z2.string().nullish(),\n          function_call: z2.object({\n            name: z2.string().optional(),\n            arguments: z2.string().optional()\n          }).nullish(),\n          tool_calls: z2.array(\n            z2.object({\n              index: z2.number(),\n              id: z2.string().nullish(),\n              type: z2.literal(\"function\").nullish(),\n              function: z2.object({\n                name: z2.string().nullish(),\n                arguments: z2.string().nullish()\n              })\n            })\n          ).nullish()\n        }).nullish(),\n        logprobs: z2.object({\n          content: z2.array(\n            z2.object({\n              token: z2.string(),\n              logprob: z2.number(),\n              top_logprobs: z2.array(\n                z2.object({\n                  token: z2.string(),\n                  logprob: z2.number()\n                })\n              )\n            })\n          ).nullable()\n        }).nullish(),\n        finish_reason: z2.string().nullish(),\n        index: z2.number()\n      })\n    ),\n    usage: openaiTokenUsageSchema\n  }),\n  openaiErrorDataSchema\n]);\nfunction isReasoningModel(modelId) {\n  return modelId.startsWith(\"o\");\n}\nfunction isAudioModel(modelId) {\n  return modelId.startsWith(\"gpt-4o-audio-preview\");\n}\nfunction getSystemMessageMode(modelId) {\n  var _a, _b;\n  if (!isReasoningModel(modelId)) {\n    return \"system\";\n  }\n  return (_b = (_a = reasoningModels[modelId]) == null ? void 0 : _a.systemMessageMode) != null ? _b : \"developer\";\n}\nvar reasoningModels = {\n  \"o1-mini\": {\n    systemMessageMode: \"remove\"\n  },\n  \"o1-mini-2024-09-12\": {\n    systemMessageMode: \"remove\"\n  },\n  \"o1-preview\": {\n    systemMessageMode: \"remove\"\n  },\n  \"o1-preview-2024-09-12\": {\n    systemMessageMode: \"remove\"\n  },\n  o3: {\n    systemMessageMode: \"developer\"\n  },\n  \"o3-2025-04-16\": {\n    systemMessageMode: \"developer\"\n  },\n  \"o3-mini\": {\n    systemMessageMode: \"developer\"\n  },\n  \"o3-mini-2025-01-31\": {\n    systemMessageMode: \"developer\"\n  },\n  \"o4-mini\": {\n    systemMessageMode: \"developer\"\n  },\n  \"o4-mini-2025-04-16\": {\n    systemMessageMode: \"developer\"\n  }\n};\n\n// src/openai-completion-language-model.ts\nimport {\n  UnsupportedFunctionalityError as UnsupportedFunctionalityError5\n} from \"@ai-sdk/provider\";\nimport {\n  combineHeaders as combineHeaders2,\n  createEventSourceResponseHandler as createEventSourceResponseHandler2,\n  createJsonResponseHandler as createJsonResponseHandler2,\n  postJsonToApi as postJsonToApi2\n} from \"@ai-sdk/provider-utils\";\nimport { z as z3 } from \"zod\";\n\n// src/convert-to-openai-completion-prompt.ts\nimport {\n  InvalidPromptError,\n  UnsupportedFunctionalityError as UnsupportedFunctionalityError4\n} from \"@ai-sdk/provider\";\nfunction convertToOpenAICompletionPrompt({\n  prompt,\n  inputFormat,\n  user = \"user\",\n  assistant = \"assistant\"\n}) {\n  if (inputFormat === \"prompt\" && prompt.length === 1 && prompt[0].role === \"user\" && prompt[0].content.length === 1 && prompt[0].content[0].type === \"text\") {\n    return { prompt: prompt[0].content[0].text };\n  }\n  let text = \"\";\n  if (prompt[0].role === \"system\") {\n    text += `${prompt[0].content}\n\n`;\n    prompt = prompt.slice(1);\n  }\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        throw new InvalidPromptError({\n          message: \"Unexpected system message in prompt: ${content}\",\n          prompt\n        });\n      }\n      case \"user\": {\n        const userMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"image\": {\n              throw new UnsupportedFunctionalityError4({\n                functionality: \"images\"\n              });\n            }\n          }\n        }).join(\"\");\n        text += `${user}:\n${userMessage}\n\n`;\n        break;\n      }\n      case \"assistant\": {\n        const assistantMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"tool-call\": {\n              throw new UnsupportedFunctionalityError4({\n                functionality: \"tool-call messages\"\n              });\n            }\n          }\n        }).join(\"\");\n        text += `${assistant}:\n${assistantMessage}\n\n`;\n        break;\n      }\n      case \"tool\": {\n        throw new UnsupportedFunctionalityError4({\n          functionality: \"tool messages\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  text += `${assistant}:\n`;\n  return {\n    prompt: text,\n    stopSequences: [`\n${user}:`]\n  };\n}\n\n// src/map-openai-completion-logprobs.ts\nfunction mapOpenAICompletionLogProbs(logprobs) {\n  return logprobs == null ? void 0 : logprobs.tokens.map((token, index) => ({\n    token,\n    logprob: logprobs.token_logprobs[index],\n    topLogprobs: logprobs.top_logprobs ? Object.entries(logprobs.top_logprobs[index]).map(\n      ([token2, logprob]) => ({\n        token: token2,\n        logprob\n      })\n    ) : []\n  }));\n}\n\n// src/openai-completion-language-model.ts\nvar OpenAICompletionLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = void 0;\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    mode,\n    inputFormat,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences: userStopSequences,\n    responseFormat,\n    seed\n  }) {\n    var _a;\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if (responseFormat != null && responseFormat.type !== \"text\") {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format is not supported.\"\n      });\n    }\n    const { prompt: completionPrompt, stopSequences } = convertToOpenAICompletionPrompt({ prompt, inputFormat });\n    const stop = [...stopSequences != null ? stopSequences : [], ...userStopSequences != null ? userStopSequences : []];\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // model specific settings:\n      echo: this.settings.echo,\n      logit_bias: this.settings.logitBias,\n      logprobs: typeof this.settings.logprobs === \"number\" ? this.settings.logprobs : typeof this.settings.logprobs === \"boolean\" ? this.settings.logprobs ? 0 : void 0 : void 0,\n      suffix: this.settings.suffix,\n      user: this.settings.user,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n      // prompt:\n      prompt: completionPrompt,\n      // stop sequences:\n      stop: stop.length > 0 ? stop : void 0\n    };\n    switch (type) {\n      case \"regular\": {\n        if ((_a = mode.tools) == null ? void 0 : _a.length) {\n          throw new UnsupportedFunctionalityError5({\n            functionality: \"tools\"\n          });\n        }\n        if (mode.toolChoice) {\n          throw new UnsupportedFunctionalityError5({\n            functionality: \"toolChoice\"\n          });\n        }\n        return { args: baseArgs, warnings };\n      }\n      case \"object-json\": {\n        throw new UnsupportedFunctionalityError5({\n          functionality: \"object-json mode\"\n        });\n      }\n      case \"object-tool\": {\n        throw new UnsupportedFunctionalityError5({\n          functionality: \"object-tool mode\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    const { args, warnings } = this.getArgs(options);\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await postJsonToApi2({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders2(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler2(\n        openaiCompletionResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n    return {\n      text: choice.text,\n      usage: {\n        promptTokens: response.usage.prompt_tokens,\n        completionTokens: response.usage.completion_tokens\n      },\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      logprobs: mapOpenAICompletionLogProbs(choice.logprobs),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      response: getResponseMetadata(response),\n      warnings,\n      request: { body: JSON.stringify(args) }\n    };\n  }\n  async doStream(options) {\n    const { args, warnings } = this.getArgs(options);\n    const body = {\n      ...args,\n      stream: true,\n      // only include stream_options when in strict compatibility mode:\n      stream_options: this.config.compatibility === \"strict\" ? { include_usage: true } : void 0\n    };\n    const { responseHeaders, value: response } = await postJsonToApi2({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders2(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler2(\n        openaiCompletionChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN\n    };\n    let logprobs;\n    let isFirstChunk = true;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n            }\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens\n              };\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.text) != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: choice.text\n              });\n            }\n            const mappedLogprobs = mapOpenAICompletionLogProbs(\n              choice == null ? void 0 : choice.logprobs\n            );\n            if (mappedLogprobs == null ? void 0 : mappedLogprobs.length) {\n              if (logprobs === void 0) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body: JSON.stringify(body) }\n    };\n  }\n};\nvar openaiCompletionResponseSchema = z3.object({\n  id: z3.string().nullish(),\n  created: z3.number().nullish(),\n  model: z3.string().nullish(),\n  choices: z3.array(\n    z3.object({\n      text: z3.string(),\n      finish_reason: z3.string(),\n      logprobs: z3.object({\n        tokens: z3.array(z3.string()),\n        token_logprobs: z3.array(z3.number()),\n        top_logprobs: z3.array(z3.record(z3.string(), z3.number())).nullable()\n      }).nullish()\n    })\n  ),\n  usage: z3.object({\n    prompt_tokens: z3.number(),\n    completion_tokens: z3.number()\n  })\n});\nvar openaiCompletionChunkSchema = z3.union([\n  z3.object({\n    id: z3.string().nullish(),\n    created: z3.number().nullish(),\n    model: z3.string().nullish(),\n    choices: z3.array(\n      z3.object({\n        text: z3.string(),\n        finish_reason: z3.string().nullish(),\n        index: z3.number(),\n        logprobs: z3.object({\n          tokens: z3.array(z3.string()),\n          token_logprobs: z3.array(z3.number()),\n          top_logprobs: z3.array(z3.record(z3.string(), z3.number())).nullable()\n        }).nullish()\n      })\n    ),\n    usage: z3.object({\n      prompt_tokens: z3.number(),\n      completion_tokens: z3.number()\n    }).nullish()\n  }),\n  openaiErrorDataSchema\n]);\n\n// src/openai-embedding-model.ts\nimport {\n  TooManyEmbeddingValuesForCallError\n} from \"@ai-sdk/provider\";\nimport {\n  combineHeaders as combineHeaders3,\n  createJsonResponseHandler as createJsonResponseHandler3,\n  postJsonToApi as postJsonToApi3\n} from \"@ai-sdk/provider-utils\";\nimport { z as z4 } from \"zod\";\nvar OpenAIEmbeddingModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get maxEmbeddingsPerCall() {\n    var _a;\n    return (_a = this.settings.maxEmbeddingsPerCall) != null ? _a : 2048;\n  }\n  get supportsParallelCalls() {\n    var _a;\n    return (_a = this.settings.supportsParallelCalls) != null ? _a : true;\n  }\n  async doEmbed({\n    values,\n    headers,\n    abortSignal\n  }) {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values\n      });\n    }\n    const { responseHeaders, value: response } = await postJsonToApi3({\n      url: this.config.url({\n        path: \"/embeddings\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders3(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        input: values,\n        encoding_format: \"float\",\n        dimensions: this.settings.dimensions,\n        user: this.settings.user\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler3(\n        openaiTextEmbeddingResponseSchema\n      ),\n      abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      embeddings: response.data.map((item) => item.embedding),\n      usage: response.usage ? { tokens: response.usage.prompt_tokens } : void 0,\n      rawResponse: { headers: responseHeaders }\n    };\n  }\n};\nvar openaiTextEmbeddingResponseSchema = z4.object({\n  data: z4.array(z4.object({ embedding: z4.array(z4.number()) })),\n  usage: z4.object({ prompt_tokens: z4.number() }).nullish()\n});\n\n// src/openai-image-model.ts\nimport {\n  combineHeaders as combineHeaders4,\n  createJsonResponseHandler as createJsonResponseHandler4,\n  postJsonToApi as postJsonToApi4\n} from \"@ai-sdk/provider-utils\";\nimport { z as z5 } from \"zod\";\n\n// src/openai-image-settings.ts\nvar modelMaxImagesPerCall = {\n  \"dall-e-3\": 1,\n  \"dall-e-2\": 10,\n  \"gpt-image-1\": 10\n};\nvar hasDefaultResponseFormat = /* @__PURE__ */ new Set([\"gpt-image-1\"]);\n\n// src/openai-image-model.ts\nvar OpenAIImageModel = class {\n  constructor(modelId, settings, config) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n    this.specificationVersion = \"v1\";\n  }\n  get maxImagesPerCall() {\n    var _a, _b;\n    return (_b = (_a = this.settings.maxImagesPerCall) != null ? _a : modelMaxImagesPerCall[this.modelId]) != null ? _b : 1;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  async doGenerate({\n    prompt,\n    n,\n    size,\n    aspectRatio,\n    seed,\n    providerOptions,\n    headers,\n    abortSignal\n  }) {\n    var _a, _b, _c, _d;\n    const warnings = [];\n    if (aspectRatio != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"aspectRatio\",\n        details: \"This model does not support aspect ratio. Use `size` instead.\"\n      });\n    }\n    if (seed != null) {\n      warnings.push({ type: \"unsupported-setting\", setting: \"seed\" });\n    }\n    const currentDate = (_c = (_b = (_a = this.config._internal) == null ? void 0 : _a.currentDate) == null ? void 0 : _b.call(_a)) != null ? _c : /* @__PURE__ */ new Date();\n    const { value: response, responseHeaders } = await postJsonToApi4({\n      url: this.config.url({\n        path: \"/images/generations\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders4(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        prompt,\n        n,\n        size,\n        ...(_d = providerOptions.openai) != null ? _d : {},\n        ...!hasDefaultResponseFormat.has(this.modelId) ? { response_format: \"b64_json\" } : {}\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler4(\n        openaiImageResponseSchema\n      ),\n      abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      images: response.data.map((item) => item.b64_json),\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders\n      }\n    };\n  }\n};\nvar openaiImageResponseSchema = z5.object({\n  data: z5.array(z5.object({ b64_json: z5.string() }))\n});\n\n// src/openai-transcription-model.ts\nimport {\n  combineHeaders as combineHeaders5,\n  convertBase64ToUint8Array,\n  createJsonResponseHandler as createJsonResponseHandler5,\n  parseProviderOptions,\n  postFormDataToApi\n} from \"@ai-sdk/provider-utils\";\nimport { z as z6 } from \"zod\";\nvar openAIProviderOptionsSchema = z6.object({\n  include: z6.array(z6.string()).nullish(),\n  language: z6.string().nullish(),\n  prompt: z6.string().nullish(),\n  temperature: z6.number().min(0).max(1).nullish().default(0),\n  timestampGranularities: z6.array(z6.enum([\"word\", \"segment\"])).nullish().default([\"segment\"])\n});\nvar languageMap = {\n  afrikaans: \"af\",\n  arabic: \"ar\",\n  armenian: \"hy\",\n  azerbaijani: \"az\",\n  belarusian: \"be\",\n  bosnian: \"bs\",\n  bulgarian: \"bg\",\n  catalan: \"ca\",\n  chinese: \"zh\",\n  croatian: \"hr\",\n  czech: \"cs\",\n  danish: \"da\",\n  dutch: \"nl\",\n  english: \"en\",\n  estonian: \"et\",\n  finnish: \"fi\",\n  french: \"fr\",\n  galician: \"gl\",\n  german: \"de\",\n  greek: \"el\",\n  hebrew: \"he\",\n  hindi: \"hi\",\n  hungarian: \"hu\",\n  icelandic: \"is\",\n  indonesian: \"id\",\n  italian: \"it\",\n  japanese: \"ja\",\n  kannada: \"kn\",\n  kazakh: \"kk\",\n  korean: \"ko\",\n  latvian: \"lv\",\n  lithuanian: \"lt\",\n  macedonian: \"mk\",\n  malay: \"ms\",\n  marathi: \"mr\",\n  maori: \"mi\",\n  nepali: \"ne\",\n  norwegian: \"no\",\n  persian: \"fa\",\n  polish: \"pl\",\n  portuguese: \"pt\",\n  romanian: \"ro\",\n  russian: \"ru\",\n  serbian: \"sr\",\n  slovak: \"sk\",\n  slovenian: \"sl\",\n  spanish: \"es\",\n  swahili: \"sw\",\n  swedish: \"sv\",\n  tagalog: \"tl\",\n  tamil: \"ta\",\n  thai: \"th\",\n  turkish: \"tr\",\n  ukrainian: \"uk\",\n  urdu: \"ur\",\n  vietnamese: \"vi\",\n  welsh: \"cy\"\n};\nvar OpenAITranscriptionModel = class {\n  constructor(modelId, config) {\n    this.modelId = modelId;\n    this.config = config;\n    this.specificationVersion = \"v1\";\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    audio,\n    mediaType,\n    providerOptions\n  }) {\n    var _a, _b, _c, _d, _e;\n    const warnings = [];\n    const openAIOptions = parseProviderOptions({\n      provider: \"openai\",\n      providerOptions,\n      schema: openAIProviderOptionsSchema\n    });\n    const formData = new FormData();\n    const blob = audio instanceof Uint8Array ? new Blob([audio]) : new Blob([convertBase64ToUint8Array(audio)]);\n    formData.append(\"model\", this.modelId);\n    formData.append(\"file\", new File([blob], \"audio\", { type: mediaType }));\n    if (openAIOptions) {\n      const transcriptionModelOptions = {\n        include: (_a = openAIOptions.include) != null ? _a : void 0,\n        language: (_b = openAIOptions.language) != null ? _b : void 0,\n        prompt: (_c = openAIOptions.prompt) != null ? _c : void 0,\n        temperature: (_d = openAIOptions.temperature) != null ? _d : void 0,\n        timestamp_granularities: (_e = openAIOptions.timestampGranularities) != null ? _e : void 0\n      };\n      for (const key in transcriptionModelOptions) {\n        const value = transcriptionModelOptions[key];\n        if (value !== void 0) {\n          formData.append(key, String(value));\n        }\n      }\n    }\n    return {\n      formData,\n      warnings\n    };\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f;\n    const currentDate = (_c = (_b = (_a = this.config._internal) == null ? void 0 : _a.currentDate) == null ? void 0 : _b.call(_a)) != null ? _c : /* @__PURE__ */ new Date();\n    const { formData, warnings } = this.getArgs(options);\n    const {\n      value: response,\n      responseHeaders,\n      rawValue: rawResponse\n    } = await postFormDataToApi({\n      url: this.config.url({\n        path: \"/audio/transcriptions\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders5(this.config.headers(), options.headers),\n      formData,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler5(\n        openaiTranscriptionResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const language = response.language != null && response.language in languageMap ? languageMap[response.language] : void 0;\n    return {\n      text: response.text,\n      segments: (_e = (_d = response.words) == null ? void 0 : _d.map((word) => ({\n        text: word.word,\n        startSecond: word.start,\n        endSecond: word.end\n      }))) != null ? _e : [],\n      language,\n      durationInSeconds: (_f = response.duration) != null ? _f : void 0,\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse\n      }\n    };\n  }\n};\nvar openaiTranscriptionResponseSchema = z6.object({\n  text: z6.string(),\n  language: z6.string().nullish(),\n  duration: z6.number().nullish(),\n  words: z6.array(\n    z6.object({\n      word: z6.string(),\n      start: z6.number(),\n      end: z6.number()\n    })\n  ).nullish()\n});\n\n// src/responses/openai-responses-language-model.ts\nimport {\n  combineHeaders as combineHeaders6,\n  createEventSourceResponseHandler as createEventSourceResponseHandler3,\n  createJsonResponseHandler as createJsonResponseHandler6,\n  generateId as generateId2,\n  parseProviderOptions as parseProviderOptions2,\n  postJsonToApi as postJsonToApi5\n} from \"@ai-sdk/provider-utils\";\nimport { z as z7 } from \"zod\";\n\n// src/responses/convert-to-openai-responses-messages.ts\nimport {\n  UnsupportedFunctionalityError as UnsupportedFunctionalityError6\n} from \"@ai-sdk/provider\";\nimport { convertUint8ArrayToBase64 as convertUint8ArrayToBase642 } from \"@ai-sdk/provider-utils\";\nfunction convertToOpenAIResponsesMessages({\n  prompt,\n  systemMessageMode\n}) {\n  const messages = [];\n  const warnings = [];\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        switch (systemMessageMode) {\n          case \"system\": {\n            messages.push({ role: \"system\", content });\n            break;\n          }\n          case \"developer\": {\n            messages.push({ role: \"developer\", content });\n            break;\n          }\n          case \"remove\": {\n            warnings.push({\n              type: \"other\",\n              message: \"system messages are removed for this model\"\n            });\n            break;\n          }\n          default: {\n            const _exhaustiveCheck = systemMessageMode;\n            throw new Error(\n              `Unsupported system message mode: ${_exhaustiveCheck}`\n            );\n          }\n        }\n        break;\n      }\n      case \"user\": {\n        messages.push({\n          role: \"user\",\n          content: content.map((part, index) => {\n            var _a, _b, _c, _d;\n            switch (part.type) {\n              case \"text\": {\n                return { type: \"input_text\", text: part.text };\n              }\n              case \"image\": {\n                return {\n                  type: \"input_image\",\n                  image_url: part.image instanceof URL ? part.image.toString() : `data:${(_a = part.mimeType) != null ? _a : \"image/jpeg\"};base64,${convertUint8ArrayToBase642(part.image)}`,\n                  // OpenAI specific extension: image detail\n                  detail: (_c = (_b = part.providerMetadata) == null ? void 0 : _b.openai) == null ? void 0 : _c.imageDetail\n                };\n              }\n              case \"file\": {\n                if (part.data instanceof URL) {\n                  throw new UnsupportedFunctionalityError6({\n                    functionality: \"File URLs in user messages\"\n                  });\n                }\n                switch (part.mimeType) {\n                  case \"application/pdf\": {\n                    return {\n                      type: \"input_file\",\n                      filename: (_d = part.filename) != null ? _d : `part-${index}.pdf`,\n                      file_data: `data:application/pdf;base64,${part.data}`\n                    };\n                  }\n                  default: {\n                    throw new UnsupportedFunctionalityError6({\n                      functionality: \"Only PDF files are supported in user messages\"\n                    });\n                  }\n                }\n              }\n            }\n          })\n        });\n        break;\n      }\n      case \"assistant\": {\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              messages.push({\n                role: \"assistant\",\n                content: [{ type: \"output_text\", text: part.text }]\n              });\n              break;\n            }\n            case \"tool-call\": {\n              messages.push({\n                type: \"function_call\",\n                call_id: part.toolCallId,\n                name: part.toolName,\n                arguments: JSON.stringify(part.args)\n              });\n              break;\n            }\n          }\n        }\n        break;\n      }\n      case \"tool\": {\n        for (const part of content) {\n          messages.push({\n            type: \"function_call_output\",\n            call_id: part.toolCallId,\n            output: JSON.stringify(part.result)\n          });\n        }\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return { messages, warnings };\n}\n\n// src/responses/map-openai-responses-finish-reason.ts\nfunction mapOpenAIResponseFinishReason({\n  finishReason,\n  hasToolCalls\n}) {\n  switch (finishReason) {\n    case void 0:\n    case null:\n      return hasToolCalls ? \"tool-calls\" : \"stop\";\n    case \"max_output_tokens\":\n      return \"length\";\n    case \"content_filter\":\n      return \"content-filter\";\n    default:\n      return hasToolCalls ? \"tool-calls\" : \"unknown\";\n  }\n}\n\n// src/responses/openai-responses-prepare-tools.ts\nimport {\n  UnsupportedFunctionalityError as UnsupportedFunctionalityError7\n} from \"@ai-sdk/provider\";\nfunction prepareResponsesTools({\n  mode,\n  strict\n}) {\n  var _a;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  const toolWarnings = [];\n  if (tools == null) {\n    return { tools: void 0, tool_choice: void 0, toolWarnings };\n  }\n  const toolChoice = mode.toolChoice;\n  const openaiTools2 = [];\n  for (const tool of tools) {\n    switch (tool.type) {\n      case \"function\":\n        openaiTools2.push({\n          type: \"function\",\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: strict ? true : void 0\n        });\n        break;\n      case \"provider-defined\":\n        switch (tool.id) {\n          case \"openai.web_search_preview\":\n            openaiTools2.push({\n              type: \"web_search_preview\",\n              search_context_size: tool.args.searchContextSize,\n              user_location: tool.args.userLocation\n            });\n            break;\n          default:\n            toolWarnings.push({ type: \"unsupported-tool\", tool });\n            break;\n        }\n        break;\n      default:\n        toolWarnings.push({ type: \"unsupported-tool\", tool });\n        break;\n    }\n  }\n  if (toolChoice == null) {\n    return { tools: openaiTools2, tool_choice: void 0, toolWarnings };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n    case \"required\":\n      return { tools: openaiTools2, tool_choice: type, toolWarnings };\n    case \"tool\": {\n      if (toolChoice.toolName === \"web_search_preview\") {\n        return {\n          tools: openaiTools2,\n          tool_choice: {\n            type: \"web_search_preview\"\n          },\n          toolWarnings\n        };\n      }\n      return {\n        tools: openaiTools2,\n        tool_choice: {\n          type: \"function\",\n          name: toolChoice.toolName\n        },\n        toolWarnings\n      };\n    }\n    default: {\n      const _exhaustiveCheck = type;\n      throw new UnsupportedFunctionalityError7({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`\n      });\n    }\n  }\n}\n\n// src/responses/openai-responses-language-model.ts\nvar OpenAIResponsesLanguageModel = class {\n  constructor(modelId, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = \"json\";\n    this.supportsStructuredOutputs = true;\n    this.modelId = modelId;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    mode,\n    maxTokens,\n    temperature,\n    stopSequences,\n    topP,\n    topK,\n    presencePenalty,\n    frequencyPenalty,\n    seed,\n    prompt,\n    providerMetadata,\n    responseFormat\n  }) {\n    var _a, _b, _c;\n    const warnings = [];\n    const modelConfig = getResponsesModelConfig(this.modelId);\n    const type = mode.type;\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if (seed != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"seed\"\n      });\n    }\n    if (presencePenalty != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"presencePenalty\"\n      });\n    }\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"frequencyPenalty\"\n      });\n    }\n    if (stopSequences != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"stopSequences\"\n      });\n    }\n    const { messages, warnings: messageWarnings } = convertToOpenAIResponsesMessages({\n      prompt,\n      systemMessageMode: modelConfig.systemMessageMode\n    });\n    warnings.push(...messageWarnings);\n    const openaiOptions = parseProviderOptions2({\n      provider: \"openai\",\n      providerOptions: providerMetadata,\n      schema: openaiResponsesProviderOptionsSchema\n    });\n    const isStrict = (_a = openaiOptions == null ? void 0 : openaiOptions.strictSchemas) != null ? _a : true;\n    const baseArgs = {\n      model: this.modelId,\n      input: messages,\n      temperature,\n      top_p: topP,\n      max_output_tokens: maxTokens,\n      ...(responseFormat == null ? void 0 : responseFormat.type) === \"json\" && {\n        text: {\n          format: responseFormat.schema != null ? {\n            type: \"json_schema\",\n            strict: isStrict,\n            name: (_b = responseFormat.name) != null ? _b : \"response\",\n            description: responseFormat.description,\n            schema: responseFormat.schema\n          } : { type: \"json_object\" }\n        }\n      },\n      // provider options:\n      metadata: openaiOptions == null ? void 0 : openaiOptions.metadata,\n      parallel_tool_calls: openaiOptions == null ? void 0 : openaiOptions.parallelToolCalls,\n      previous_response_id: openaiOptions == null ? void 0 : openaiOptions.previousResponseId,\n      store: openaiOptions == null ? void 0 : openaiOptions.store,\n      user: openaiOptions == null ? void 0 : openaiOptions.user,\n      instructions: openaiOptions == null ? void 0 : openaiOptions.instructions,\n      // model-specific settings:\n      ...modelConfig.isReasoningModel && ((openaiOptions == null ? void 0 : openaiOptions.reasoningEffort) != null || (openaiOptions == null ? void 0 : openaiOptions.reasoningSummary) != null) && {\n        reasoning: {\n          ...(openaiOptions == null ? void 0 : openaiOptions.reasoningEffort) != null && {\n            effort: openaiOptions.reasoningEffort\n          },\n          ...(openaiOptions == null ? void 0 : openaiOptions.reasoningSummary) != null && {\n            summary: openaiOptions.reasoningSummary\n          }\n        }\n      },\n      ...modelConfig.requiredAutoTruncation && {\n        truncation: \"auto\"\n      }\n    };\n    if (modelConfig.isReasoningModel) {\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"temperature\",\n          details: \"temperature is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.top_p != null) {\n        baseArgs.top_p = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"topP\",\n          details: \"topP is not supported for reasoning models\"\n        });\n      }\n    }\n    switch (type) {\n      case \"regular\": {\n        const { tools, tool_choice, toolWarnings } = prepareResponsesTools({\n          mode,\n          strict: isStrict\n          // TODO support provider options on tools\n        });\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice\n          },\n          warnings: [...warnings, ...toolWarnings]\n        };\n      }\n      case \"object-json\": {\n        return {\n          args: {\n            ...baseArgs,\n            text: {\n              format: mode.schema != null ? {\n                type: \"json_schema\",\n                strict: isStrict,\n                name: (_c = mode.name) != null ? _c : \"response\",\n                description: mode.description,\n                schema: mode.schema\n              } : { type: \"json_object\" }\n            }\n          },\n          warnings\n        };\n      }\n      case \"object-tool\": {\n        return {\n          args: {\n            ...baseArgs,\n            tool_choice: { type: \"function\", name: mode.tool.name },\n            tools: [\n              {\n                type: \"function\",\n                name: mode.tool.name,\n                description: mode.tool.description,\n                parameters: mode.tool.parameters,\n                strict: isStrict\n              }\n            ]\n          },\n          warnings\n        };\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    const { args: body, warnings } = this.getArgs(options);\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await postJsonToApi5({\n      url: this.config.url({\n        path: \"/responses\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders6(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler6(\n        z7.object({\n          id: z7.string(),\n          created_at: z7.number(),\n          model: z7.string(),\n          output: z7.array(\n            z7.discriminatedUnion(\"type\", [\n              z7.object({\n                type: z7.literal(\"message\"),\n                role: z7.literal(\"assistant\"),\n                content: z7.array(\n                  z7.object({\n                    type: z7.literal(\"output_text\"),\n                    text: z7.string(),\n                    annotations: z7.array(\n                      z7.object({\n                        type: z7.literal(\"url_citation\"),\n                        start_index: z7.number(),\n                        end_index: z7.number(),\n                        url: z7.string(),\n                        title: z7.string()\n                      })\n                    )\n                  })\n                )\n              }),\n              z7.object({\n                type: z7.literal(\"function_call\"),\n                call_id: z7.string(),\n                name: z7.string(),\n                arguments: z7.string()\n              }),\n              z7.object({\n                type: z7.literal(\"web_search_call\")\n              }),\n              z7.object({\n                type: z7.literal(\"computer_call\")\n              }),\n              z7.object({\n                type: z7.literal(\"reasoning\"),\n                summary: z7.array(\n                  z7.object({\n                    type: z7.literal(\"summary_text\"),\n                    text: z7.string()\n                  })\n                )\n              })\n            ])\n          ),\n          incomplete_details: z7.object({ reason: z7.string() }).nullable(),\n          usage: usageSchema\n        })\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const outputTextElements = response.output.filter((output) => output.type === \"message\").flatMap((output) => output.content).filter((content) => content.type === \"output_text\");\n    const toolCalls = response.output.filter((output) => output.type === \"function_call\").map((output) => ({\n      toolCallType: \"function\",\n      toolCallId: output.call_id,\n      toolName: output.name,\n      args: output.arguments\n    }));\n    const reasoningSummary = (_b = (_a = response.output.find((item) => item.type === \"reasoning\")) == null ? void 0 : _a.summary) != null ? _b : null;\n    return {\n      text: outputTextElements.map((content) => content.text).join(\"\\n\"),\n      sources: outputTextElements.flatMap(\n        (content) => content.annotations.map((annotation) => {\n          var _a2, _b2, _c2;\n          return {\n            sourceType: \"url\",\n            id: (_c2 = (_b2 = (_a2 = this.config).generateId) == null ? void 0 : _b2.call(_a2)) != null ? _c2 : generateId2(),\n            url: annotation.url,\n            title: annotation.title\n          };\n        })\n      ),\n      finishReason: mapOpenAIResponseFinishReason({\n        finishReason: (_c = response.incomplete_details) == null ? void 0 : _c.reason,\n        hasToolCalls: toolCalls.length > 0\n      }),\n      toolCalls: toolCalls.length > 0 ? toolCalls : void 0,\n      reasoning: reasoningSummary ? reasoningSummary.map((summary) => ({\n        type: \"text\",\n        text: summary.text\n      })) : void 0,\n      usage: {\n        promptTokens: response.usage.input_tokens,\n        completionTokens: response.usage.output_tokens\n      },\n      rawCall: {\n        rawPrompt: void 0,\n        rawSettings: {}\n      },\n      rawResponse: {\n        headers: responseHeaders,\n        body: rawResponse\n      },\n      request: {\n        body: JSON.stringify(body)\n      },\n      response: {\n        id: response.id,\n        timestamp: new Date(response.created_at * 1e3),\n        modelId: response.model\n      },\n      providerMetadata: {\n        openai: {\n          responseId: response.id,\n          cachedPromptTokens: (_e = (_d = response.usage.input_tokens_details) == null ? void 0 : _d.cached_tokens) != null ? _e : null,\n          reasoningTokens: (_g = (_f = response.usage.output_tokens_details) == null ? void 0 : _f.reasoning_tokens) != null ? _g : null\n        }\n      },\n      warnings\n    };\n  }\n  async doStream(options) {\n    const { args: body, warnings } = this.getArgs(options);\n    const { responseHeaders, value: response } = await postJsonToApi5({\n      url: this.config.url({\n        path: \"/responses\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders6(this.config.headers(), options.headers),\n      body: {\n        ...body,\n        stream: true\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler3(\n        openaiResponsesChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const self = this;\n    let finishReason = \"unknown\";\n    let promptTokens = NaN;\n    let completionTokens = NaN;\n    let cachedPromptTokens = null;\n    let reasoningTokens = null;\n    let responseId = null;\n    const ongoingToolCalls = {};\n    let hasToolCalls = false;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a, _b, _c, _d, _e, _f, _g, _h;\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (isResponseOutputItemAddedChunk(value)) {\n              if (value.item.type === \"function_call\") {\n                ongoingToolCalls[value.output_index] = {\n                  toolName: value.item.name,\n                  toolCallId: value.item.call_id\n                };\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: value.item.call_id,\n                  toolName: value.item.name,\n                  argsTextDelta: value.item.arguments\n                });\n              }\n            } else if (isResponseFunctionCallArgumentsDeltaChunk(value)) {\n              const toolCall = ongoingToolCalls[value.output_index];\n              if (toolCall != null) {\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.toolCallId,\n                  toolName: toolCall.toolName,\n                  argsTextDelta: value.delta\n                });\n              }\n            } else if (isResponseCreatedChunk(value)) {\n              responseId = value.response.id;\n              controller.enqueue({\n                type: \"response-metadata\",\n                id: value.response.id,\n                timestamp: new Date(value.response.created_at * 1e3),\n                modelId: value.response.model\n              });\n            } else if (isTextDeltaChunk(value)) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: value.delta\n              });\n            } else if (isResponseReasoningSummaryTextDeltaChunk(value)) {\n              controller.enqueue({\n                type: \"reasoning\",\n                textDelta: value.delta\n              });\n            } else if (isResponseOutputItemDoneChunk(value) && value.item.type === \"function_call\") {\n              ongoingToolCalls[value.output_index] = void 0;\n              hasToolCalls = true;\n              controller.enqueue({\n                type: \"tool-call\",\n                toolCallType: \"function\",\n                toolCallId: value.item.call_id,\n                toolName: value.item.name,\n                args: value.item.arguments\n              });\n            } else if (isResponseFinishedChunk(value)) {\n              finishReason = mapOpenAIResponseFinishReason({\n                finishReason: (_a = value.response.incomplete_details) == null ? void 0 : _a.reason,\n                hasToolCalls\n              });\n              promptTokens = value.response.usage.input_tokens;\n              completionTokens = value.response.usage.output_tokens;\n              cachedPromptTokens = (_c = (_b = value.response.usage.input_tokens_details) == null ? void 0 : _b.cached_tokens) != null ? _c : cachedPromptTokens;\n              reasoningTokens = (_e = (_d = value.response.usage.output_tokens_details) == null ? void 0 : _d.reasoning_tokens) != null ? _e : reasoningTokens;\n            } else if (isResponseAnnotationAddedChunk(value)) {\n              controller.enqueue({\n                type: \"source\",\n                source: {\n                  sourceType: \"url\",\n                  id: (_h = (_g = (_f = self.config).generateId) == null ? void 0 : _g.call(_f)) != null ? _h : generateId2(),\n                  url: value.annotation.url,\n                  title: value.annotation.title\n                }\n              });\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              usage: { promptTokens, completionTokens },\n              ...(cachedPromptTokens != null || reasoningTokens != null) && {\n                providerMetadata: {\n                  openai: {\n                    responseId,\n                    cachedPromptTokens,\n                    reasoningTokens\n                  }\n                }\n              }\n            });\n          }\n        })\n      ),\n      rawCall: {\n        rawPrompt: void 0,\n        rawSettings: {}\n      },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings\n    };\n  }\n};\nvar usageSchema = z7.object({\n  input_tokens: z7.number(),\n  input_tokens_details: z7.object({ cached_tokens: z7.number().nullish() }).nullish(),\n  output_tokens: z7.number(),\n  output_tokens_details: z7.object({ reasoning_tokens: z7.number().nullish() }).nullish()\n});\nvar textDeltaChunkSchema = z7.object({\n  type: z7.literal(\"response.output_text.delta\"),\n  delta: z7.string()\n});\nvar responseFinishedChunkSchema = z7.object({\n  type: z7.enum([\"response.completed\", \"response.incomplete\"]),\n  response: z7.object({\n    incomplete_details: z7.object({ reason: z7.string() }).nullish(),\n    usage: usageSchema\n  })\n});\nvar responseCreatedChunkSchema = z7.object({\n  type: z7.literal(\"response.created\"),\n  response: z7.object({\n    id: z7.string(),\n    created_at: z7.number(),\n    model: z7.string()\n  })\n});\nvar responseOutputItemDoneSchema = z7.object({\n  type: z7.literal(\"response.output_item.done\"),\n  output_index: z7.number(),\n  item: z7.discriminatedUnion(\"type\", [\n    z7.object({\n      type: z7.literal(\"message\")\n    }),\n    z7.object({\n      type: z7.literal(\"function_call\"),\n      id: z7.string(),\n      call_id: z7.string(),\n      name: z7.string(),\n      arguments: z7.string(),\n      status: z7.literal(\"completed\")\n    })\n  ])\n});\nvar responseFunctionCallArgumentsDeltaSchema = z7.object({\n  type: z7.literal(\"response.function_call_arguments.delta\"),\n  item_id: z7.string(),\n  output_index: z7.number(),\n  delta: z7.string()\n});\nvar responseOutputItemAddedSchema = z7.object({\n  type: z7.literal(\"response.output_item.added\"),\n  output_index: z7.number(),\n  item: z7.discriminatedUnion(\"type\", [\n    z7.object({\n      type: z7.literal(\"message\")\n    }),\n    z7.object({\n      type: z7.literal(\"function_call\"),\n      id: z7.string(),\n      call_id: z7.string(),\n      name: z7.string(),\n      arguments: z7.string()\n    })\n  ])\n});\nvar responseAnnotationAddedSchema = z7.object({\n  type: z7.literal(\"response.output_text.annotation.added\"),\n  annotation: z7.object({\n    type: z7.literal(\"url_citation\"),\n    url: z7.string(),\n    title: z7.string()\n  })\n});\nvar responseReasoningSummaryTextDeltaSchema = z7.object({\n  type: z7.literal(\"response.reasoning_summary_text.delta\"),\n  item_id: z7.string(),\n  output_index: z7.number(),\n  summary_index: z7.number(),\n  delta: z7.string()\n});\nvar openaiResponsesChunkSchema = z7.union([\n  textDeltaChunkSchema,\n  responseFinishedChunkSchema,\n  responseCreatedChunkSchema,\n  responseOutputItemDoneSchema,\n  responseFunctionCallArgumentsDeltaSchema,\n  responseOutputItemAddedSchema,\n  responseAnnotationAddedSchema,\n  responseReasoningSummaryTextDeltaSchema,\n  z7.object({ type: z7.string() }).passthrough()\n  // fallback for unknown chunks\n]);\nfunction isTextDeltaChunk(chunk) {\n  return chunk.type === \"response.output_text.delta\";\n}\nfunction isResponseOutputItemDoneChunk(chunk) {\n  return chunk.type === \"response.output_item.done\";\n}\nfunction isResponseFinishedChunk(chunk) {\n  return chunk.type === \"response.completed\" || chunk.type === \"response.incomplete\";\n}\nfunction isResponseCreatedChunk(chunk) {\n  return chunk.type === \"response.created\";\n}\nfunction isResponseFunctionCallArgumentsDeltaChunk(chunk) {\n  return chunk.type === \"response.function_call_arguments.delta\";\n}\nfunction isResponseOutputItemAddedChunk(chunk) {\n  return chunk.type === \"response.output_item.added\";\n}\nfunction isResponseAnnotationAddedChunk(chunk) {\n  return chunk.type === \"response.output_text.annotation.added\";\n}\nfunction isResponseReasoningSummaryTextDeltaChunk(chunk) {\n  return chunk.type === \"response.reasoning_summary_text.delta\";\n}\nfunction getResponsesModelConfig(modelId) {\n  if (modelId.startsWith(\"o\")) {\n    if (modelId.startsWith(\"o1-mini\") || modelId.startsWith(\"o1-preview\")) {\n      return {\n        isReasoningModel: true,\n        systemMessageMode: \"remove\",\n        requiredAutoTruncation: false\n      };\n    }\n    return {\n      isReasoningModel: true,\n      systemMessageMode: \"developer\",\n      requiredAutoTruncation: false\n    };\n  }\n  return {\n    isReasoningModel: false,\n    systemMessageMode: \"system\",\n    requiredAutoTruncation: false\n  };\n}\nvar openaiResponsesProviderOptionsSchema = z7.object({\n  metadata: z7.any().nullish(),\n  parallelToolCalls: z7.boolean().nullish(),\n  previousResponseId: z7.string().nullish(),\n  store: z7.boolean().nullish(),\n  user: z7.string().nullish(),\n  reasoningEffort: z7.string().nullish(),\n  strictSchemas: z7.boolean().nullish(),\n  instructions: z7.string().nullish(),\n  reasoningSummary: z7.string().nullish()\n});\n\n// src/openai-tools.ts\nimport { z as z8 } from \"zod\";\nvar WebSearchPreviewParameters = z8.object({});\nfunction webSearchPreviewTool({\n  searchContextSize,\n  userLocation\n} = {}) {\n  return {\n    type: \"provider-defined\",\n    id: \"openai.web_search_preview\",\n    args: {\n      searchContextSize,\n      userLocation\n    },\n    parameters: WebSearchPreviewParameters\n  };\n}\nvar openaiTools = {\n  webSearchPreview: webSearchPreviewTool\n};\n\n// src/openai-speech-model.ts\nimport {\n  combineHeaders as combineHeaders7,\n  createBinaryResponseHandler,\n  parseProviderOptions as parseProviderOptions3,\n  postJsonToApi as postJsonToApi6\n} from \"@ai-sdk/provider-utils\";\nimport { z as z9 } from \"zod\";\nvar OpenAIProviderOptionsSchema = z9.object({\n  instructions: z9.string().nullish(),\n  speed: z9.number().min(0.25).max(4).default(1).nullish()\n});\nvar OpenAISpeechModel = class {\n  constructor(modelId, config) {\n    this.modelId = modelId;\n    this.config = config;\n    this.specificationVersion = \"v1\";\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    text,\n    voice = \"alloy\",\n    outputFormat = \"mp3\",\n    speed,\n    instructions,\n    providerOptions\n  }) {\n    const warnings = [];\n    const openAIOptions = parseProviderOptions3({\n      provider: \"openai\",\n      providerOptions,\n      schema: OpenAIProviderOptionsSchema\n    });\n    const requestBody = {\n      model: this.modelId,\n      input: text,\n      voice,\n      response_format: \"mp3\",\n      speed,\n      instructions\n    };\n    if (outputFormat) {\n      if ([\"mp3\", \"opus\", \"aac\", \"flac\", \"wav\", \"pcm\"].includes(outputFormat)) {\n        requestBody.response_format = outputFormat;\n      } else {\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"outputFormat\",\n          details: `Unsupported output format: ${outputFormat}. Using mp3 instead.`\n        });\n      }\n    }\n    if (openAIOptions) {\n      const speechModelOptions = {};\n      for (const key in speechModelOptions) {\n        const value = speechModelOptions[key];\n        if (value !== void 0) {\n          requestBody[key] = value;\n        }\n      }\n    }\n    return {\n      requestBody,\n      warnings\n    };\n  }\n  async doGenerate(options) {\n    var _a, _b, _c;\n    const currentDate = (_c = (_b = (_a = this.config._internal) == null ? void 0 : _a.currentDate) == null ? void 0 : _b.call(_a)) != null ? _c : /* @__PURE__ */ new Date();\n    const { requestBody, warnings } = this.getArgs(options);\n    const {\n      value: audio,\n      responseHeaders,\n      rawValue: rawResponse\n    } = await postJsonToApi6({\n      url: this.config.url({\n        path: \"/audio/speech\",\n        modelId: this.modelId\n      }),\n      headers: combineHeaders7(this.config.headers(), options.headers),\n      body: requestBody,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: createBinaryResponseHandler(),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      audio,\n      warnings,\n      request: {\n        body: JSON.stringify(requestBody)\n      },\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n        body: rawResponse\n      }\n    };\n  }\n};\n\n// src/openai-provider.ts\nfunction createOpenAI(options = {}) {\n  var _a, _b, _c;\n  const baseURL = (_a = withoutTrailingSlash(options.baseURL)) != null ? _a : \"https://api.openai.com/v1\";\n  const compatibility = (_b = options.compatibility) != null ? _b : \"compatible\";\n  const providerName = (_c = options.name) != null ? _c : \"openai\";\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: \"OPENAI_API_KEY\",\n      description: \"OpenAI\"\n    })}`,\n    \"OpenAI-Organization\": options.organization,\n    \"OpenAI-Project\": options.project,\n    ...options.headers\n  });\n  const createChatModel = (modelId, settings = {}) => new OpenAIChatLanguageModel(modelId, settings, {\n    provider: `${providerName}.chat`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    compatibility,\n    fetch: options.fetch\n  });\n  const createCompletionModel = (modelId, settings = {}) => new OpenAICompletionLanguageModel(modelId, settings, {\n    provider: `${providerName}.completion`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    compatibility,\n    fetch: options.fetch\n  });\n  const createEmbeddingModel = (modelId, settings = {}) => new OpenAIEmbeddingModel(modelId, settings, {\n    provider: `${providerName}.embedding`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createImageModel = (modelId, settings = {}) => new OpenAIImageModel(modelId, settings, {\n    provider: `${providerName}.image`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createTranscriptionModel = (modelId) => new OpenAITranscriptionModel(modelId, {\n    provider: `${providerName}.transcription`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createSpeechModel = (modelId) => new OpenAISpeechModel(modelId, {\n    provider: `${providerName}.speech`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createLanguageModel = (modelId, settings) => {\n    if (new.target) {\n      throw new Error(\n        \"The OpenAI model function cannot be called with the new keyword.\"\n      );\n    }\n    if (modelId === \"gpt-3.5-turbo-instruct\") {\n      return createCompletionModel(\n        modelId,\n        settings\n      );\n    }\n    return createChatModel(modelId, settings);\n  };\n  const createResponsesModel = (modelId) => {\n    return new OpenAIResponsesLanguageModel(modelId, {\n      provider: `${providerName}.responses`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch\n    });\n  };\n  const provider = function(modelId, settings) {\n    return createLanguageModel(modelId, settings);\n  };\n  provider.languageModel = createLanguageModel;\n  provider.chat = createChatModel;\n  provider.completion = createCompletionModel;\n  provider.responses = createResponsesModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n  provider.image = createImageModel;\n  provider.imageModel = createImageModel;\n  provider.transcription = createTranscriptionModel;\n  provider.transcriptionModel = createTranscriptionModel;\n  provider.speech = createSpeechModel;\n  provider.speechModel = createSpeechModel;\n  provider.tools = openaiTools;\n  return provider;\n}\nvar openai = createOpenAI({\n  compatibility: \"strict\"\n  // strict for OpenAI API\n});\nexport {\n  createOpenAI,\n  openai\n};\n//# sourceMappingURL=index.mjs.map"], "mappings": ";;;AA0BA,SAAS,4BAA4B,EACnC,QACA,2BAA2B,OAC3B,oBAAoB,UACrB,EAAE;CACD,MAAM,WAAW,CAAE;CACnB,MAAM,WAAW,CAAE;AACnB,MAAK,MAAM,EAAE,MAAM,SAAS,IAAI,OAC9B,SAAQ,MAAR;EACE,KAAK,UAAU;AACb,WAAQ,mBAAR;IACE,KAAK,UAAU;AACb,cAAS,KAAK;MAAE,MAAM;MAAU;KAAS,EAAC;AAC1C;IACD;IACD,KAAK,aAAa;AAChB,cAAS,KAAK;MAAE,MAAM;MAAa;KAAS,EAAC;AAC7C;IACD;IACD,KAAK,UAAU;AACb,cAAS,KAAK;MACZ,MAAM;MACN,SAAS;KACV,EAAC;AACF;IACD;IACD,SAAS;KACP,MAAM,mBAAmB;AACzB,WAAM,IAAI,OACP,mCAAmC;IAEvC;GACF;AACD;EACD;EACD,KAAK,QAAQ;AACX,OAAI,QAAQ,WAAW,KAAK,QAAQ,GAAG,SAAS,QAAQ;AACtD,aAAS,KAAK;KAAE,MAAM;KAAQ,SAAS,QAAQ,GAAG;IAAM,EAAC;AACzD;GACD;AACD,YAAS,KAAK;IACZ,MAAM;IACN,SAAS,QAAQ,IAAI,CAAC,MAAM,UAAU;KACpC,IAAI,IAAI,IAAI,IAAI;AAChB,aAAQ,KAAK,MAAb;MACE,KAAK,OACH,QAAO;OAAE,MAAM;OAAQ,MAAM,KAAK;MAAM;MAE1C,KAAK,QACH,QAAO;OACL,MAAM;OACN,WAAW;QACT,KAAK,KAAK,iBAAiB,MAAM,KAAK,MAAM,UAAU,IAAI,QAAQ,KAAK,KAAK,aAAa,OAAO,KAAK,aAAa,UAAU,0BAA0B,KAAK,MAAM;QAEjK,SAAS,MAAM,KAAK,KAAK,qBAAqB,YAAY,IAAI,GAAG,WAAW,YAAY,IAAI,GAAG;OAChG;MACF;MAEH,KAAK,QAAQ;AACX,WAAI,KAAK,gBAAgB,IACvB,OAAM,IAAI,8BAA8B,EACtC,eAAe,kEAChB;AAEH,eAAQ,KAAK,UAAb;QACE,KAAK,YACH,QAAO;SACL,MAAM;SACN,aAAa;UAAE,MAAM,KAAK;UAAM,QAAQ;SAAO;QAChD;QAEH,KAAK;QACL,KAAK,aACH,QAAO;SACL,MAAM;SACN,aAAa;UAAE,MAAM,KAAK;UAAM,QAAQ;SAAO;QAChD;QAEH,KAAK,kBACH,QAAO;SACL,MAAM;SACN,MAAM;UACJ,WAAW,KAAK,KAAK,aAAa,OAAO,MAAM,OAAO,MAAM;UAC5D,YAAY,8BAA8B,KAAK;SAChD;QACF;QAEH,QACE,OAAM,IAAI,8BAA8B,EACtC,gBAAgB,yBAAyB,KAAK,SAAS,mBACxD;OAEJ;MACF;KACF;IACF,EAAC;GACH,EAAC;AACF;EACD;EACD,KAAK,aAAa;GAChB,IAAI,OAAO;GACX,MAAM,YAAY,CAAE;AACpB,QAAK,MAAM,QAAQ,QACjB,SAAQ,KAAK,MAAb;IACE,KAAK,QAAQ;AACX,aAAQ,KAAK;AACb;IACD;IACD,KAAK,aAAa;AAChB,eAAU,KAAK;MACb,IAAI,KAAK;MACT,MAAM;MACN,UAAU;OACR,MAAM,KAAK;OACX,WAAW,KAAK,UAAU,KAAK,KAAK;MACrC;KACF,EAAC;AACF;IACD;GACF;AAEH,OAAI,0BAA0B;AAC5B,QAAI,UAAU,SAAS,EACrB,OAAM,IAAI,8BAA8B,EACtC,eAAe,mEAChB;AAEH,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;KACT,eAAe,UAAU,SAAS,IAAI,UAAU,GAAG,gBAAgB;IACpE,EAAC;GACH,MACC,UAAS,KAAK;IACZ,MAAM;IACN,SAAS;IACT,YAAY,UAAU,SAAS,IAAI,iBAAiB;GACrD,EAAC;AAEJ;EACD;EACD,KAAK,QAAQ;AACX,QAAK,MAAM,gBAAgB,QACzB,KAAI,yBACF,UAAS,KAAK;IACZ,MAAM;IACN,MAAM,aAAa;IACnB,SAAS,KAAK,UAAU,aAAa,OAAO;GAC7C,EAAC;OAEF,UAAS,KAAK;IACZ,MAAM;IACN,cAAc,aAAa;IAC3B,SAAS,KAAK,UAAU,aAAa,OAAO;GAC7C,EAAC;AAGN;EACD;EACD,SAAS;GACP,MAAM,mBAAmB;AACzB,SAAM,IAAI,OAAO,oBAAoB;EACtC;CACF;AAEH,QAAO;EAAE;EAAU;CAAU;AAC9B;AAGD,SAAS,4BAA4B,UAAU;CAC7C,IAAI,IAAI;AACR,SAAQ,MAAM,KAAK,YAAY,YAAY,IAAI,SAAS,YAAY,YAAY,IAAI,GAAG,IAAI,CAAC,EAAE,OAAO,SAAS,cAAc,MAAM;EAChI;EACA;EACA,aAAa,eAAe,aAAa,IAAI,CAAC,EAAE,OAAO,QAAQ,SAAS,UAAU,MAAM;GACtF,OAAO;GACP,SAAS;EACV,GAAE,GAAG,CAAE;CACT,GAAE,KAAK,OAAO,UAAU;AAC1B;AAGD,SAAS,sBAAsB,cAAc;AAC3C,SAAQ,cAAR;EACE,KAAK,OACH,QAAO;EACT,KAAK,SACH,QAAO;EACT,KAAK,iBACH,QAAO;EACT,KAAK;EACL,KAAK,aACH,QAAO;EACT,QACE,QAAO;CACV;AACF;AAKD,IAAI,wBAAwB,WAAS,EACnC,OAAO,WAAS;CACd,SAAS,YAAU;CAInB,MAAM,YAAU,CAAC,SAAS;CAC1B,OAAO,SAAO,CAAC,SAAS;CACxB,MAAM,UAAQ,CAAC,YAAU,EAAE,YAAU,AAAC,EAAC,CAAC,SAAS;AAClD,EAAC,CACH,EAAC;AACF,IAAI,8BAA8B,+BAA+B;CAC/D,aAAa;CACb,gBAAgB,CAAC,SAAS,KAAK,MAAM;AACtC,EAAC;AAGF,SAAS,oBAAoB,EAC3B,IACA,OACA,SACD,EAAE;AACD,QAAO;EACL,IAAI,MAAM,OAAO,UAAU;EAC3B,SAAS,SAAS,OAAO,aAAa;EACtC,WAAW,WAAW,uBAAO,IAAI,KAAK,UAAU,YAAY;CAC7D;AACF;AAMD,SAAS,aAAa,EACpB,MACA,2BAA2B,OAC3B,mBACD,EAAE;CACD,IAAI;CACJ,MAAM,UAAU,KAAK,KAAK,UAAU,YAAY,IAAI,GAAG,UAAU,KAAK,aAAa;CACnF,MAAM,eAAe,CAAE;AACvB,KAAI,SAAS,KACX,QAAO;EAAE,YAAY;EAAG,kBAAkB;EAAG;CAAc;CAE7D,MAAM,aAAa,KAAK;AACxB,KAAI,0BAA0B;EAC5B,MAAM,kBAAkB,CAAE;AAC1B,OAAK,MAAM,QAAQ,MACjB,KAAI,KAAK,SAAS,mBAChB,cAAa,KAAK;GAAE,MAAM;GAAoB;EAAM,EAAC;MAErD,iBAAgB,KAAK;GACnB,MAAM,KAAK;GACX,aAAa,KAAK;GAClB,YAAY,KAAK;EAClB,EAAC;AAGN,MAAI,cAAc,KAChB,QAAO;GACL,WAAW;GACX,oBAAoB;GACpB;EACD;EAEH,MAAM,QAAQ,WAAW;AACzB,UAAQ,OAAR;GACE,KAAK;GACL,KAAK;GACL,UAAU,EACR,QAAO;IACL,WAAW;IACX,oBAAoB;IACpB;GACD;GACH,KAAK,WACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,oDAChB;GACH,QACE,QAAO;IACL,WAAW;IACX,eAAe,EAAE,MAAM,WAAW,SAAU;IAC5C;GACD;EACJ;CACF;CACD,MAAM,eAAe,CAAE;AACvB,MAAK,MAAM,QAAQ,MACjB,KAAI,KAAK,SAAS,mBAChB,cAAa,KAAK;EAAE,MAAM;EAAoB;CAAM,EAAC;KAErD,cAAa,KAAK;EAChB,MAAM;EACN,UAAU;GACR,MAAM,KAAK;GACX,aAAa,KAAK;GAClB,YAAY,KAAK;GACjB,QAAQ,oBAAoB,YAAY;EACzC;CACF,EAAC;AAGN,KAAI,cAAc,KAChB,QAAO;EAAE,OAAO;EAAc,kBAAkB;EAAG;CAAc;CAEnE,MAAM,OAAO,WAAW;AACxB,SAAQ,MAAR;EACE,KAAK;EACL,KAAK;EACL,KAAK,WACH,QAAO;GAAE,OAAO;GAAc,aAAa;GAAM;EAAc;EACjE,KAAK,OACH,QAAO;GACL,OAAO;GACP,aAAa;IACX,MAAM;IACN,UAAU,EACR,MAAM,WAAW,SAClB;GACF;GACD;EACD;EACH,SAAS;GACP,MAAM,mBAAmB;AACzB,SAAM,IAAIA,8BAA+B,EACvC,gBAAgB,gCAAgC,mBACjD;EACF;CACF;AACF;AAGD,IAAI,0BAA0B,MAAM;CAClC,YAAY,SAAS,UAAU,QAAQ;AACrC,OAAK,uBAAuB;AAC5B,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,SAAS;CACf;CACD,IAAI,4BAA4B;EAC9B,IAAI;AACJ,UAAQ,KAAK,KAAK,SAAS,sBAAsB,OAAO,KAAK,iBAAiB,KAAK,QAAQ;CAC5F;CACD,IAAI,8BAA8B;AAChC,MAAI,aAAa,KAAK,QAAQ,CAC5B,QAAO;AAET,SAAO,KAAK,4BAA4B,SAAS;CAClD;CACD,IAAI,WAAW;AACb,SAAO,KAAK,OAAO;CACpB;CACD,IAAI,oBAAoB;AACtB,UAAQ,KAAK,SAAS;CACvB;CACD,QAAQ,EACN,MACA,QACA,WACA,aACA,MACA,MACA,kBACA,iBACA,eACA,gBACA,MACA,kBACD,EAAE;EACD,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;EAChC,MAAM,OAAO,KAAK;EAClB,MAAM,WAAW,CAAE;AACnB,MAAI,QAAQ,KACV,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;EACV,EAAC;AAEJ,OAAK,kBAAkB,YAAY,IAAI,eAAe,UAAU,UAAU,eAAe,UAAU,SAAS,KAAK,0BAC/G,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;GACT,SAAS;EACV,EAAC;EAEJ,MAAM,2BAA2B,KAAK,SAAS;AAC/C,MAAI,4BAA4B,KAAK,SAAS,sBAAsB,KAClE,OAAM,IAAIC,8BAA+B,EACvC,eAAe,kDAChB;AAEH,MAAI,4BAA4B,KAAK,0BACnC,OAAM,IAAIA,8BAA+B,EACvC,eAAe,kDAChB;EAEH,MAAM,EAAE,UAAU,UAAU,iBAAiB,GAAG,4BAC9C;GACE;GACA;GACA,mBAAmB,qBAAqB,KAAK,QAAQ;EACtD,EACF;AACD,WAAS,KAAK,GAAG,gBAAgB;EACjC,MAAM,WAAW;GAEf,OAAO,KAAK;GAEZ,YAAY,KAAK,SAAS;GAC1B,UAAU,KAAK,SAAS,aAAa,eAAe,KAAK,SAAS,aAAa,WAAW,YAAY;GACtG,qBAAqB,KAAK,SAAS,aAAa,WAAW,KAAK,SAAS,kBAAkB,KAAK,SAAS,aAAa,YAAY,KAAK,SAAS,WAAW,SAAS,SAAS;GAC7K,MAAM,KAAK,SAAS;GACpB,qBAAqB,KAAK,SAAS;GAEnC,YAAY;GACZ;GACA,OAAO;GACP,mBAAmB;GACnB,kBAAkB;GAClB,kBAAkB,kBAAkB,YAAY,IAAI,eAAe,UAAU,SAAS,KAAK,6BAA6B,eAAe,UAAU,OAAO;IACtJ,MAAM;IACN,aAAa;KACX,QAAQ,eAAe;KACvB,QAAQ;KACR,OAAO,KAAK,eAAe,SAAS,OAAO,KAAK;KAChD,aAAa,eAAe;IAC7B;GACF,IAAG,EAAE,MAAM,cAAe,SAAQ;GACnC,MAAM;GACN;GAGA,wBAAwB,KAAK,oBAAoB,YAAY,IAAI,iBAAiB,WAAW,YAAY,IAAI,GAAG;GAChH,QAAQ,KAAK,oBAAoB,YAAY,IAAI,iBAAiB,WAAW,YAAY,IAAI,GAAG;GAChG,WAAW,KAAK,oBAAoB,YAAY,IAAI,iBAAiB,WAAW,YAAY,IAAI,GAAG;GACnG,aAAa,KAAK,oBAAoB,YAAY,IAAI,iBAAiB,WAAW,YAAY,IAAI,GAAG;GACrG,mBAAmB,MAAM,KAAK,oBAAoB,YAAY,IAAI,iBAAiB,WAAW,YAAY,IAAI,GAAG,oBAAoB,OAAO,KAAK,KAAK,SAAS;GAE/J;EACD;AACD,MAAI,iBAAiB,KAAK,QAAQ,EAAE;AAClC,OAAI,SAAS,eAAe,MAAM;AAChC,aAAS,mBAAmB;AAC5B,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;KACT,SAAS;IACV,EAAC;GACH;AACD,OAAI,SAAS,SAAS,MAAM;AAC1B,aAAS,aAAa;AACtB,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;KACT,SAAS;IACV,EAAC;GACH;AACD,OAAI,SAAS,qBAAqB,MAAM;AACtC,aAAS,yBAAyB;AAClC,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;KACT,SAAS;IACV,EAAC;GACH;AACD,OAAI,SAAS,oBAAoB,MAAM;AACrC,aAAS,wBAAwB;AACjC,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;KACT,SAAS;IACV,EAAC;GACH;AACD,OAAI,SAAS,cAAc,MAAM;AAC/B,aAAS,kBAAkB;AAC3B,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;IACV,EAAC;GACH;AACD,OAAI,SAAS,YAAY,MAAM;AAC7B,aAAS,gBAAgB;AACzB,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;IACV,EAAC;GACH;AACD,OAAI,SAAS,gBAAgB,MAAM;AACjC,aAAS,oBAAoB;AAC7B,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;IACV,EAAC;GACH;AACD,OAAI,SAAS,cAAc,MAAM;AAC/B,QAAI,SAAS,yBAAyB,KACpC,UAAS,wBAAwB,SAAS;AAE5C,aAAS,kBAAkB;GAC5B;EACF,WAAU,KAAK,QAAQ,WAAW,wBAAwB,IAAI,KAAK,QAAQ,WAAW,6BAA6B,EAClH;OAAI,SAAS,eAAe,MAAM;AAChC,aAAS,mBAAmB;AAC5B,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;KACT,SAAS;IACV,EAAC;GACH;;AAEH,UAAQ,MAAR;GACE,KAAK,WAAW;IACd,MAAM,EAAE,OAAO,aAAa,WAAW,eAAe,cAAc,GAAG,aAAa;KAClF;KACA;KACA,mBAAmB,KAAK;IACzB,EAAC;AACF,WAAO;KACL,MAAM;MACJ,GAAG;MACH;MACA;MACA;MACA;KACD;KACD,UAAU,CAAC,GAAG,UAAU,GAAG,YAAa;IACzC;GACF;GACD,KAAK,cACH,QAAO;IACL,MAAM;KACJ,GAAG;KACH,iBAAiB,KAAK,6BAA6B,KAAK,UAAU,OAAO;MACvE,MAAM;MACN,aAAa;OACX,QAAQ,KAAK;OACb,QAAQ;OACR,OAAO,KAAK,KAAK,SAAS,OAAO,KAAK;OACtC,aAAa,KAAK;MACnB;KACF,IAAG,EAAE,MAAM,cAAe;IAC5B;IACD;GACD;GAEH,KAAK,cACH,QAAO;IACL,MAAM,2BAA2B;KAC/B,GAAG;KACH,eAAe,EACb,MAAM,KAAK,KAAK,KACjB;KACD,WAAW,CACT;MACE,MAAM,KAAK,KAAK;MAChB,aAAa,KAAK,KAAK;MACvB,YAAY,KAAK,KAAK;KACvB,CACF;IACF,IAAG;KACF,GAAG;KACH,aAAa;MACX,MAAM;MACN,UAAU,EAAE,MAAM,KAAK,KAAK,KAAM;KACnC;KACD,OAAO,CACL;MACE,MAAM;MACN,UAAU;OACR,MAAM,KAAK,KAAK;OAChB,aAAa,KAAK,KAAK;OACvB,YAAY,KAAK,KAAK;OACtB,QAAQ,KAAK,4BAA4B,YAAY;MACtD;KACF,CACF;IACF;IACD;GACD;GAEH,SAAS;IACP,MAAM,mBAAmB;AACzB,UAAM,IAAI,OAAO,oBAAoB;GACtC;EACF;CACF;CACD,MAAM,WAAW,SAAS;EACxB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;EAChC,MAAM,EAAE,MAAM,MAAM,UAAU,GAAG,KAAK,QAAQ,QAAQ;EACtD,MAAM,EACJ,iBACA,OAAO,UACP,UAAU,aACX,GAAG,MAAM,cAAc;GACtB,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAe,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAC/D;GACA,uBAAuB;GACvB,2BAA2B,0BACzB,yBACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,EAAE,UAAU,UAAW,GAAG,aAAa,GAAG;EAChD,MAAM,SAAS,SAAS,QAAQ;EAChC,MAAM,0BAA0B,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG;EAC3E,MAAM,sBAAsB,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG;EACvE,MAAM,mBAAmB,EAAE,QAAQ,CAAE,EAAE;AACvC,OAAK,0BAA0B,YAAY,IAAI,uBAAuB,qBAAqB,KACzF,kBAAiB,OAAO,kBAAkB,0BAA0B,YAAY,IAAI,uBAAuB;AAE7G,OAAK,0BAA0B,YAAY,IAAI,uBAAuB,+BAA+B,KACnG,kBAAiB,OAAO,2BAA2B,0BAA0B,YAAY,IAAI,uBAAuB;AAEtH,OAAK,0BAA0B,YAAY,IAAI,uBAAuB,+BAA+B,KACnG,kBAAiB,OAAO,2BAA2B,0BAA0B,YAAY,IAAI,uBAAuB;AAEtH,OAAK,sBAAsB,YAAY,IAAI,mBAAmB,kBAAkB,KAC9E,kBAAiB,OAAO,qBAAqB,sBAAsB,YAAY,IAAI,mBAAmB;AAExG,SAAO;GACL,OAAO,KAAK,OAAO,QAAQ,YAAY,OAAO,UAAU;GACxD,WAAW,KAAK,SAAS,4BAA4B,OAAO,QAAQ,gBAAgB,CAClF;IACE,cAAc;IACd,YAAY,YAAY;IACxB,UAAU,OAAO,QAAQ,cAAc;IACvC,MAAM,OAAO,QAAQ,cAAc;GACpC,CACF,KAAI,KAAK,OAAO,QAAQ,eAAe,YAAY,IAAI,GAAG,IAAI,CAAC,aAAa;IAC3E,IAAI;AACJ,WAAO;KACL,cAAc;KACd,aAAa,MAAM,SAAS,OAAO,OAAO,MAAM,YAAY;KAC5D,UAAU,SAAS,SAAS;KAC5B,MAAM,SAAS,SAAS;IACzB;GACF,EAAC;GACF,cAAc,sBAAsB,OAAO,cAAc;GACzD,OAAO;IACL,eAAe,MAAM,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG,kBAAkB,OAAO,KAAK;IAC9F,mBAAmB,MAAM,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG,sBAAsB,OAAO,KAAK;GACvG;GACD,SAAS;IAAE;IAAW;GAAa;GACnC,aAAa;IAAE,SAAS;IAAiB,MAAM;GAAa;GAC5D,SAAS,EAAE,MAAM,KAAK,UAAU,KAAK,CAAE;GACvC,UAAU,oBAAoB,SAAS;GACvC;GACA,UAAU,4BAA4B,OAAO,SAAS;GACtD;EACD;CACF;CACD,MAAM,SAAS,SAAS;AACtB,MAAI,KAAK,SAAS,mBAAmB;GACnC,MAAM,SAAS,MAAM,KAAK,WAAW,QAAQ;GAC7C,MAAM,kBAAkB,IAAI,eAAe,EACzC,MAAM,YAAY;AAChB,eAAW,QAAQ;KAAE,MAAM;KAAqB,GAAG,OAAO;IAAU,EAAC;AACrE,QAAI,OAAO,KACT,YAAW,QAAQ;KACjB,MAAM;KACN,WAAW,OAAO;IACnB,EAAC;AAEJ,QAAI,OAAO,UACT,MAAK,MAAM,YAAY,OAAO,WAAW;AACvC,gBAAW,QAAQ;MACjB,MAAM;MACN,cAAc;MACd,YAAY,SAAS;MACrB,UAAU,SAAS;MACnB,eAAe,SAAS;KACzB,EAAC;AACF,gBAAW,QAAQ;MACjB,MAAM;MACN,GAAG;KACJ,EAAC;IACH;AAEH,eAAW,QAAQ;KACjB,MAAM;KACN,cAAc,OAAO;KACrB,OAAO,OAAO;KACd,UAAU,OAAO;KACjB,kBAAkB,OAAO;IAC1B,EAAC;AACF,eAAW,OAAO;GACnB,EACF;AACD,UAAO;IACL,QAAQ;IACR,SAAS,OAAO;IAChB,aAAa,OAAO;IACpB,UAAU,OAAO;GAClB;EACF;EACD,MAAM,EAAE,MAAM,UAAU,GAAG,KAAK,QAAQ,QAAQ;EAChD,MAAM,OAAO;GACX,GAAG;GACH,QAAQ;GAER,gBAAgB,KAAK,OAAO,kBAAkB,WAAW,EAAE,eAAe,KAAM,SAAQ;EACzF;EACD,MAAM,EAAE,iBAAiB,OAAO,UAAU,GAAG,MAAM,cAAc;GAC/D,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAe,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAC/D;GACA,uBAAuB;GACvB,2BAA2B,iCACzB,sBACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,EAAE,UAAU,UAAW,GAAG,aAAa,GAAG;EAChD,MAAM,YAAY,CAAE;EACpB,IAAI,eAAe;EACnB,IAAI,QAAQ;GACV,mBAAmB;GACnB,uBAAuB;EACxB;EACD,IAAI;EACJ,IAAI,eAAe;EACnB,MAAM,EAAE,0BAA0B,GAAG,KAAK;EAC1C,MAAM,mBAAmB,EAAE,QAAQ,CAAE,EAAE;AACvC,SAAO;GACL,QAAQ,SAAS,YACf,IAAI,gBAAgB;IAClB,UAAU,OAAO,YAAY;KAC3B,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChD,UAAK,MAAM,SAAS;AAClB,qBAAe;AACf,iBAAW,QAAQ;OAAE,MAAM;OAAS,OAAO,MAAM;MAAO,EAAC;AACzD;KACD;KACD,MAAM,QAAQ,MAAM;AACpB,SAAI,WAAW,OAAO;AACpB,qBAAe;AACf,iBAAW,QAAQ;OAAE,MAAM;OAAS,OAAO,MAAM;MAAO,EAAC;AACzD;KACD;AACD,SAAI,cAAc;AAChB,qBAAe;AACf,iBAAW,QAAQ;OACjB,MAAM;OACN,GAAG,oBAAoB,MAAM;MAC9B,EAAC;KACH;AACD,SAAI,MAAM,SAAS,MAAM;MACvB,MAAM,EACJ,eACA,mBACA,uBACA,2BACD,GAAG,MAAM;AACV,cAAQ;OACN,cAAc,iBAAiB,OAAO,qBAAqB;OAC3D,kBAAkB,qBAAqB,OAAO,yBAAyB;MACxE;AACD,WAAK,6BAA6B,YAAY,IAAI,0BAA0B,qBAAqB,KAC/F,kBAAiB,OAAO,kBAAkB,6BAA6B,YAAY,IAAI,0BAA0B;AAEnH,WAAK,6BAA6B,YAAY,IAAI,0BAA0B,+BAA+B,KACzG,kBAAiB,OAAO,2BAA2B,6BAA6B,YAAY,IAAI,0BAA0B;AAE5H,WAAK,6BAA6B,YAAY,IAAI,0BAA0B,+BAA+B,KACzG,kBAAiB,OAAO,2BAA2B,6BAA6B,YAAY,IAAI,0BAA0B;AAE5H,WAAK,yBAAyB,YAAY,IAAI,sBAAsB,kBAAkB,KACpF,kBAAiB,OAAO,qBAAqB,yBAAyB,YAAY,IAAI,sBAAsB;KAE/G;KACD,MAAM,SAAS,MAAM,QAAQ;AAC7B,UAAK,UAAU,YAAY,IAAI,OAAO,kBAAkB,KACtD,gBAAe,sBAAsB,OAAO,cAAc;AAE5D,UAAK,UAAU,YAAY,IAAI,OAAO,UAAU,KAC9C;KAEF,MAAM,QAAQ,OAAO;AACrB,SAAI,MAAM,WAAW,KACnB,YAAW,QAAQ;MACjB,MAAM;MACN,WAAW,MAAM;KAClB,EAAC;KAEJ,MAAM,iBAAiB,4BACrB,UAAU,YAAY,IAAI,OAAO,SAClC;AACD,SAAI,kBAAkB,YAAY,IAAI,eAAe,QAAQ;AAC3D,UAAI,kBAAkB,EAAG,YAAW,CAAE;AACtC,eAAS,KAAK,GAAG,eAAe;KACjC;KACD,MAAM,kBAAkB,4BAA4B,MAAM,iBAAiB,OAAO,CAChF;MACE,MAAM;MACN,IAAI,YAAY;MAChB,UAAU,MAAM;MAChB,OAAO;KACR,CACF,IAAG,MAAM;AACV,SAAI,mBAAmB,KACrB,MAAK,MAAM,iBAAiB,iBAAiB;MAC3C,MAAM,QAAQ,cAAc;AAC5B,UAAI,UAAU,UAAU,MAAM;AAC5B,WAAI,cAAc,SAAS,WACzB,OAAM,IAAI,yBAAyB;QACjC,MAAM;QACN,UAAU;OACX;AAEH,WAAI,cAAc,MAAM,KACtB,OAAM,IAAI,yBAAyB;QACjC,MAAM;QACN,UAAU;OACX;AAEH,aAAM,KAAK,cAAc,aAAa,YAAY,IAAI,GAAG,SAAS,KAChE,OAAM,IAAI,yBAAyB;QACjC,MAAM;QACN,UAAU;OACX;AAEH,iBAAU,SAAS;QACjB,IAAI,cAAc;QAClB,MAAM;QACN,UAAU;SACR,MAAM,cAAc,SAAS;SAC7B,YAAY,KAAK,cAAc,SAAS,cAAc,OAAO,KAAK;QACnE;QACD,aAAa;OACd;OACD,MAAM,YAAY,UAAU;AAC5B,aAAM,KAAK,UAAU,aAAa,YAAY,IAAI,GAAG,SAAS,UAAU,KAAK,UAAU,aAAa,YAAY,IAAI,GAAG,cAAc,MAAM;AACzI,YAAI,UAAU,SAAS,UAAU,SAAS,EACxC,YAAW,QAAQ;SACjB,MAAM;SACN,cAAc;SACd,YAAY,UAAU;SACtB,UAAU,UAAU,SAAS;SAC7B,eAAe,UAAU,SAAS;QACnC,EAAC;AAEJ,YAAI,eAAe,UAAU,SAAS,UAAU,EAAE;AAChD,oBAAW,QAAQ;UACjB,MAAM;UACN,cAAc;UACd,aAAa,KAAK,UAAU,OAAO,OAAO,KAAK,YAAY;UAC3D,UAAU,UAAU,SAAS;UAC7B,MAAM,UAAU,SAAS;SAC1B,EAAC;AACF,mBAAU,cAAc;QACzB;OACF;AACD;MACD;MACD,MAAM,WAAW,UAAU;AAC3B,UAAI,SAAS,YACX;AAEF,YAAM,KAAK,cAAc,aAAa,YAAY,IAAI,GAAG,cAAc,KACrE,UAAS,SAAS,cAAc,MAAM,KAAK,cAAc,aAAa,YAAY,IAAI,GAAG,cAAc,OAAO,KAAK;AAErH,iBAAW,QAAQ;OACjB,MAAM;OACN,cAAc;OACd,YAAY,SAAS;OACrB,UAAU,SAAS,SAAS;OAC5B,gBAAgB,KAAK,cAAc,SAAS,cAAc,OAAO,KAAK;MACvE,EAAC;AACF,YAAM,KAAK,SAAS,aAAa,YAAY,IAAI,GAAG,SAAS,UAAU,KAAK,SAAS,aAAa,YAAY,IAAI,GAAG,cAAc,QAAQ,eAAe,SAAS,SAAS,UAAU,EAAE;AACtL,kBAAW,QAAQ;QACjB,MAAM;QACN,cAAc;QACd,aAAa,KAAK,SAAS,OAAO,OAAO,KAAK,YAAY;QAC1D,UAAU,SAAS,SAAS;QAC5B,MAAM,SAAS,SAAS;OACzB,EAAC;AACF,gBAAS,cAAc;MACxB;KACF;IAEJ;IACD,MAAM,YAAY;KAChB,IAAI,IAAI;AACR,gBAAW,QAAQ;MACjB,MAAM;MACN;MACA;MACA,OAAO;OACL,eAAe,KAAK,MAAM,iBAAiB,OAAO,KAAK;OACvD,mBAAmB,KAAK,MAAM,qBAAqB,OAAO,KAAK;MAChE;MACD,GAAG,oBAAoB,OAAO,EAAE,iBAAkB,IAAG,CAAE;KACxD,EAAC;IACH;GACF,GACF;GACD,SAAS;IAAE;IAAW;GAAa;GACnC,aAAa,EAAE,SAAS,gBAAiB;GACzC,SAAS,EAAE,MAAM,KAAK,UAAU,KAAK,CAAE;GACvC;EACD;CACF;AACF;AACD,IAAI,yBAAyB,WAAU;CACrC,eAAe,YAAW,CAAC,SAAS;CACpC,mBAAmB,YAAW,CAAC,SAAS;CACxC,uBAAuB,WAAU,EAC/B,eAAe,YAAW,CAAC,SAAS,CACrC,EAAC,CAAC,SAAS;CACZ,2BAA2B,WAAU;EACnC,kBAAkB,YAAW,CAAC,SAAS;EACvC,4BAA4B,YAAW,CAAC,SAAS;EACjD,4BAA4B,YAAW,CAAC,SAAS;CAClD,EAAC,CAAC,SAAS;AACb,EAAC,CAAC,SAAS;AACZ,IAAI,2BAA2B,WAAU;CACvC,IAAI,YAAW,CAAC,SAAS;CACzB,SAAS,YAAW,CAAC,SAAS;CAC9B,OAAO,YAAW,CAAC,SAAS;CAC5B,SAAS,UACP,WAAU;EACR,SAAS,WAAU;GACjB,MAAM,YAAW,YAAY,CAAC,SAAS;GACvC,SAAS,YAAW,CAAC,SAAS;GAC9B,eAAe,WAAU;IACvB,WAAW,YAAW;IACtB,MAAM,YAAW;GAClB,EAAC,CAAC,SAAS;GACZ,YAAY,UACV,WAAU;IACR,IAAI,YAAW,CAAC,SAAS;IACzB,MAAM,YAAW,WAAW;IAC5B,UAAU,WAAU;KAClB,MAAM,YAAW;KACjB,WAAW,YAAW;IACvB,EAAC;GACH,EAAC,CACH,CAAC,SAAS;EACZ,EAAC;EACF,OAAO,YAAW;EAClB,UAAU,WAAU,EAClB,SAAS,UACP,WAAU;GACR,OAAO,YAAW;GAClB,SAAS,YAAW;GACpB,cAAc,UACZ,WAAU;IACR,OAAO,YAAW;IAClB,SAAS,YAAW;GACrB,EAAC,CACH;EACF,EAAC,CACH,CAAC,UAAU,CACb,EAAC,CAAC,SAAS;EACZ,eAAe,YAAW,CAAC,SAAS;CACrC,EAAC,CACH;CACD,OAAO;AACR,EAAC;AACF,IAAI,wBAAwB,UAAS,CACnC,WAAU;CACR,IAAI,YAAW,CAAC,SAAS;CACzB,SAAS,YAAW,CAAC,SAAS;CAC9B,OAAO,YAAW,CAAC,SAAS;CAC5B,SAAS,UACP,WAAU;EACR,OAAO,WAAU;GACf,MAAM,SAAQ,CAAC,WAAY,EAAC,CAAC,SAAS;GACtC,SAAS,YAAW,CAAC,SAAS;GAC9B,eAAe,WAAU;IACvB,MAAM,YAAW,CAAC,UAAU;IAC5B,WAAW,YAAW,CAAC,UAAU;GAClC,EAAC,CAAC,SAAS;GACZ,YAAY,UACV,WAAU;IACR,OAAO,YAAW;IAClB,IAAI,YAAW,CAAC,SAAS;IACzB,MAAM,YAAW,WAAW,CAAC,SAAS;IACtC,UAAU,WAAU;KAClB,MAAM,YAAW,CAAC,SAAS;KAC3B,WAAW,YAAW,CAAC,SAAS;IACjC,EAAC;GACH,EAAC,CACH,CAAC,SAAS;EACZ,EAAC,CAAC,SAAS;EACZ,UAAU,WAAU,EAClB,SAAS,UACP,WAAU;GACR,OAAO,YAAW;GAClB,SAAS,YAAW;GACpB,cAAc,UACZ,WAAU;IACR,OAAO,YAAW;IAClB,SAAS,YAAW;GACrB,EAAC,CACH;EACF,EAAC,CACH,CAAC,UAAU,CACb,EAAC,CAAC,SAAS;EACZ,eAAe,YAAW,CAAC,SAAS;EACpC,OAAO,YAAW;CACnB,EAAC,CACH;CACD,OAAO;AACR,EAAC,EACF,qBACD,EAAC;AACF,SAAS,iBAAiB,SAAS;AACjC,QAAO,QAAQ,WAAW,IAAI;AAC/B;AACD,SAAS,aAAa,SAAS;AAC7B,QAAO,QAAQ,WAAW,uBAAuB;AAClD;AACD,SAAS,qBAAqB,SAAS;CACrC,IAAI,IAAI;AACR,MAAK,iBAAiB,QAAQ,CAC5B,QAAO;AAET,SAAQ,MAAM,KAAK,gBAAgB,aAAa,YAAY,IAAI,GAAG,sBAAsB,OAAO,KAAK;AACtG;AACD,IAAI,kBAAkB;CACpB,WAAW,EACT,mBAAmB,SACpB;CACD,sBAAsB,EACpB,mBAAmB,SACpB;CACD,cAAc,EACZ,mBAAmB,SACpB;CACD,yBAAyB,EACvB,mBAAmB,SACpB;CACD,IAAI,EACF,mBAAmB,YACpB;CACD,iBAAiB,EACf,mBAAmB,YACpB;CACD,WAAW,EACT,mBAAmB,YACpB;CACD,sBAAsB,EACpB,mBAAmB,YACpB;CACD,WAAW,EACT,mBAAmB,YACpB;CACD,sBAAsB,EACpB,mBAAmB,YACpB;AACF;AAmBD,SAAS,gCAAgC,EACvC,QACA,aACA,OAAO,QACP,YAAY,aACb,EAAE;AACD,KAAI,gBAAgB,YAAY,OAAO,WAAW,KAAK,OAAO,GAAG,SAAS,UAAU,OAAO,GAAG,QAAQ,WAAW,KAAK,OAAO,GAAG,QAAQ,GAAG,SAAS,OAClJ,QAAO,EAAE,QAAQ,OAAO,GAAG,QAAQ,GAAG,KAAM;CAE9C,IAAI,OAAO;AACX,KAAI,OAAO,GAAG,SAAS,UAAU;AAC/B,aAAW,OAAO,GAAG,QAAQ;;;AAG7B,WAAS,OAAO,MAAM,EAAE;CACzB;AACD,MAAK,MAAM,EAAE,MAAM,SAAS,IAAI,OAC9B,SAAQ,MAAR;EACE,KAAK,SACH,OAAM,IAAI,mBAAmB;GAC3B,SAAS;GACT;EACD;EAEH,KAAK,QAAQ;GACX,MAAM,cAAc,QAAQ,IAAI,CAAC,SAAS;AACxC,YAAQ,KAAK,MAAb;KACE,KAAK,OACH,QAAO,KAAK;KAEd,KAAK,QACH,OAAM,IAAIC,8BAA+B,EACvC,eAAe,SAChB;IAEJ;GACF,EAAC,CAAC,KAAK,GAAG;AACX,cAAW,KAAK;EACtB,YAAY;;;AAGN;EACD;EACD,KAAK,aAAa;GAChB,MAAM,mBAAmB,QAAQ,IAAI,CAAC,SAAS;AAC7C,YAAQ,KAAK,MAAb;KACE,KAAK,OACH,QAAO,KAAK;KAEd,KAAK,YACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,qBAChB;IAEJ;GACF,EAAC,CAAC,KAAK,GAAG;AACX,cAAW,UAAU;EAC3B,iBAAiB;;;AAGX;EACD;EACD,KAAK,OACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,gBAChB;EAEH,SAAS;GACP,MAAM,mBAAmB;AACzB,SAAM,IAAI,OAAO,oBAAoB;EACtC;CACF;AAEH,YAAW,UAAU;;AAErB,QAAO;EACL,QAAQ;EACR,eAAe,EAAE;EACnB,KAAK,EAAG;CACP;AACF;AAGD,SAAS,4BAA4B,UAAU;AAC7C,QAAO,YAAY,YAAY,IAAI,SAAS,OAAO,IAAI,CAAC,OAAO,WAAW;EACxE;EACA,SAAS,SAAS,eAAe;EACjC,aAAa,SAAS,eAAe,OAAO,QAAQ,SAAS,aAAa,OAAO,CAAC,IAChF,CAAC,CAAC,QAAQ,QAAQ,MAAM;GACtB,OAAO;GACP;EACD,GACF,GAAG,CAAE;CACP,GAAE;AACJ;AAGD,IAAI,gCAAgC,MAAM;CACxC,YAAY,SAAS,UAAU,QAAQ;AACrC,OAAK,uBAAuB;AAC5B,OAAK,mCAAmC;AACxC,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,SAAS;CACf;CACD,IAAI,WAAW;AACb,SAAO,KAAK,OAAO;CACpB;CACD,QAAQ,EACN,MACA,aACA,QACA,WACA,aACA,MACA,MACA,kBACA,iBACA,eAAe,mBACf,gBACA,MACD,EAAE;EACD,IAAI;EACJ,MAAM,OAAO,KAAK;EAClB,MAAM,WAAW,CAAE;AACnB,MAAI,QAAQ,KACV,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;EACV,EAAC;AAEJ,MAAI,kBAAkB,QAAQ,eAAe,SAAS,OACpD,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;GACT,SAAS;EACV,EAAC;EAEJ,MAAM,EAAE,QAAQ,kBAAkB,eAAe,GAAG,gCAAgC;GAAE;GAAQ;EAAa,EAAC;EAC5G,MAAM,OAAO,CAAC,GAAG,iBAAiB,OAAO,gBAAgB,CAAE,GAAE,GAAG,qBAAqB,OAAO,oBAAoB,CAAE,CAAC;EACnH,MAAM,WAAW;GAEf,OAAO,KAAK;GAEZ,MAAM,KAAK,SAAS;GACpB,YAAY,KAAK,SAAS;GAC1B,iBAAiB,KAAK,SAAS,aAAa,WAAW,KAAK,SAAS,kBAAkB,KAAK,SAAS,aAAa,YAAY,KAAK,SAAS,WAAW,SAAS,SAAS;GACzK,QAAQ,KAAK,SAAS;GACtB,MAAM,KAAK,SAAS;GAEpB,YAAY;GACZ;GACA,OAAO;GACP,mBAAmB;GACnB,kBAAkB;GAClB;GAEA,QAAQ;GAER,MAAM,KAAK,SAAS,IAAI,YAAY;EACrC;AACD,UAAQ,MAAR;GACE,KAAK,WAAW;AACd,SAAK,KAAK,KAAK,UAAU,YAAY,IAAI,GAAG,OAC1C,OAAM,IAAIC,8BAA+B,EACvC,eAAe,QAChB;AAEH,QAAI,KAAK,WACP,OAAM,IAAIA,8BAA+B,EACvC,eAAe,aAChB;AAEH,WAAO;KAAE,MAAM;KAAU;IAAU;GACpC;GACD,KAAK,cACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,mBAChB;GAEH,KAAK,cACH,OAAM,IAAIA,8BAA+B,EACvC,eAAe,mBAChB;GAEH,SAAS;IACP,MAAM,mBAAmB;AACzB,UAAM,IAAI,OAAO,oBAAoB;GACtC;EACF;CACF;CACD,MAAM,WAAW,SAAS;EACxB,MAAM,EAAE,MAAM,UAAU,GAAG,KAAK,QAAQ,QAAQ;EAChD,MAAM,EACJ,iBACA,OAAO,UACP,UAAU,aACX,GAAG,MAAM,cAAe;GACvB,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAChE,MAAM;GACN,uBAAuB;GACvB,2BAA2B,0BACzB,+BACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,EAAE,QAAQ,UAAW,GAAG,aAAa,GAAG;EAC9C,MAAM,SAAS,SAAS,QAAQ;AAChC,SAAO;GACL,MAAM,OAAO;GACb,OAAO;IACL,cAAc,SAAS,MAAM;IAC7B,kBAAkB,SAAS,MAAM;GAClC;GACD,cAAc,sBAAsB,OAAO,cAAc;GACzD,UAAU,4BAA4B,OAAO,SAAS;GACtD,SAAS;IAAE;IAAW;GAAa;GACnC,aAAa;IAAE,SAAS;IAAiB,MAAM;GAAa;GAC5D,UAAU,oBAAoB,SAAS;GACvC;GACA,SAAS,EAAE,MAAM,KAAK,UAAU,KAAK,CAAE;EACxC;CACF;CACD,MAAM,SAAS,SAAS;EACtB,MAAM,EAAE,MAAM,UAAU,GAAG,KAAK,QAAQ,QAAQ;EAChD,MAAM,OAAO;GACX,GAAG;GACH,QAAQ;GAER,gBAAgB,KAAK,OAAO,kBAAkB,WAAW,EAAE,eAAe,KAAM,SAAQ;EACzF;EACD,MAAM,EAAE,iBAAiB,OAAO,UAAU,GAAG,MAAM,cAAe;GAChE,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAChE;GACA,uBAAuB;GACvB,2BAA2B,iCACzB,4BACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,EAAE,QAAQ,UAAW,GAAG,aAAa,GAAG;EAC9C,IAAI,eAAe;EACnB,IAAI,QAAQ;GACV,cAAc;GACd,kBAAkB;EACnB;EACD,IAAI;EACJ,IAAI,eAAe;AACnB,SAAO;GACL,QAAQ,SAAS,YACf,IAAI,gBAAgB;IAClB,UAAU,OAAO,YAAY;AAC3B,UAAK,MAAM,SAAS;AAClB,qBAAe;AACf,iBAAW,QAAQ;OAAE,MAAM;OAAS,OAAO,MAAM;MAAO,EAAC;AACzD;KACD;KACD,MAAM,QAAQ,MAAM;AACpB,SAAI,WAAW,OAAO;AACpB,qBAAe;AACf,iBAAW,QAAQ;OAAE,MAAM;OAAS,OAAO,MAAM;MAAO,EAAC;AACzD;KACD;AACD,SAAI,cAAc;AAChB,qBAAe;AACf,iBAAW,QAAQ;OACjB,MAAM;OACN,GAAG,oBAAoB,MAAM;MAC9B,EAAC;KACH;AACD,SAAI,MAAM,SAAS,KACjB,SAAQ;MACN,cAAc,MAAM,MAAM;MAC1B,kBAAkB,MAAM,MAAM;KAC/B;KAEH,MAAM,SAAS,MAAM,QAAQ;AAC7B,UAAK,UAAU,YAAY,IAAI,OAAO,kBAAkB,KACtD,gBAAe,sBAAsB,OAAO,cAAc;AAE5D,UAAK,UAAU,YAAY,IAAI,OAAO,SAAS,KAC7C,YAAW,QAAQ;MACjB,MAAM;MACN,WAAW,OAAO;KACnB,EAAC;KAEJ,MAAM,iBAAiB,4BACrB,UAAU,YAAY,IAAI,OAAO,SAClC;AACD,SAAI,kBAAkB,YAAY,IAAI,eAAe,QAAQ;AAC3D,UAAI,kBAAkB,EAAG,YAAW,CAAE;AACtC,eAAS,KAAK,GAAG,eAAe;KACjC;IACF;IACD,MAAM,YAAY;AAChB,gBAAW,QAAQ;MACjB,MAAM;MACN;MACA;MACA;KACD,EAAC;IACH;GACF,GACF;GACD,SAAS;IAAE;IAAW;GAAa;GACnC,aAAa,EAAE,SAAS,gBAAiB;GACzC;GACA,SAAS,EAAE,MAAM,KAAK,UAAU,KAAK,CAAE;EACxC;CACF;AACF;AACD,IAAI,iCAAiC,WAAU;CAC7C,IAAI,YAAW,CAAC,SAAS;CACzB,SAAS,YAAW,CAAC,SAAS;CAC9B,OAAO,YAAW,CAAC,SAAS;CAC5B,SAAS,UACP,WAAU;EACR,MAAM,YAAW;EACjB,eAAe,YAAW;EAC1B,UAAU,WAAU;GAClB,QAAQ,UAAS,YAAW,CAAC;GAC7B,gBAAgB,UAAS,YAAW,CAAC;GACrC,cAAc,UAAS,WAAU,YAAW,EAAE,YAAW,CAAC,CAAC,CAAC,UAAU;EACvE,EAAC,CAAC,SAAS;CACb,EAAC,CACH;CACD,OAAO,WAAU;EACf,eAAe,YAAW;EAC1B,mBAAmB,YAAW;CAC/B,EAAC;AACH,EAAC;AACF,IAAI,8BAA8B,UAAS,CACzC,WAAU;CACR,IAAI,YAAW,CAAC,SAAS;CACzB,SAAS,YAAW,CAAC,SAAS;CAC9B,OAAO,YAAW,CAAC,SAAS;CAC5B,SAAS,UACP,WAAU;EACR,MAAM,YAAW;EACjB,eAAe,YAAW,CAAC,SAAS;EACpC,OAAO,YAAW;EAClB,UAAU,WAAU;GAClB,QAAQ,UAAS,YAAW,CAAC;GAC7B,gBAAgB,UAAS,YAAW,CAAC;GACrC,cAAc,UAAS,WAAU,YAAW,EAAE,YAAW,CAAC,CAAC,CAAC,UAAU;EACvE,EAAC,CAAC,SAAS;CACb,EAAC,CACH;CACD,OAAO,WAAU;EACf,eAAe,YAAW;EAC1B,mBAAmB,YAAW;CAC/B,EAAC,CAAC,SAAS;AACb,EAAC,EACF,qBACD,EAAC;AAYF,IAAI,uBAAuB,MAAM;CAC/B,YAAY,SAAS,UAAU,QAAQ;AACrC,OAAK,uBAAuB;AAC5B,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,SAAS;CACf;CACD,IAAI,WAAW;AACb,SAAO,KAAK,OAAO;CACpB;CACD,IAAI,uBAAuB;EACzB,IAAI;AACJ,UAAQ,KAAK,KAAK,SAAS,yBAAyB,OAAO,KAAK;CACjE;CACD,IAAI,wBAAwB;EAC1B,IAAI;AACJ,UAAQ,KAAK,KAAK,SAAS,0BAA0B,OAAO,KAAK;CAClE;CACD,MAAM,QAAQ,EACZ,QACA,SACA,aACD,EAAE;AACD,MAAI,OAAO,SAAS,KAAK,qBACvB,OAAM,IAAI,mCAAmC;GAC3C,UAAU,KAAK;GACf,SAAS,KAAK;GACd,sBAAsB,KAAK;GAC3B;EACD;EAEH,MAAM,EAAE,iBAAiB,OAAO,UAAU,GAAG,MAAM,cAAe;GAChE,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ;GACxD,MAAM;IACJ,OAAO,KAAK;IACZ,OAAO;IACP,iBAAiB;IACjB,YAAY,KAAK,SAAS;IAC1B,MAAM,KAAK,SAAS;GACrB;GACD,uBAAuB;GACvB,2BAA2B,0BACzB,kCACD;GACD;GACA,OAAO,KAAK,OAAO;EACpB,EAAC;AACF,SAAO;GACL,YAAY,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,UAAU;GACvD,OAAO,SAAS,QAAQ,EAAE,QAAQ,SAAS,MAAM,cAAe,SAAQ;GACxE,aAAa,EAAE,SAAS,gBAAiB;EAC1C;CACF;AACF;AACD,IAAI,oCAAoC,WAAU;CAChD,MAAM,UAAS,WAAU,EAAE,WAAW,UAAS,YAAW,CAAC,CAAE,EAAC,CAAC;CAC/D,OAAO,WAAU,EAAE,eAAe,YAAW,CAAE,EAAC,CAAC,SAAS;AAC3D,EAAC;AAWF,IAAI,wBAAwB;CAC1B,YAAY;CACZ,YAAY;CACZ,eAAe;AAChB;AACD,IAAI,2CAA2C,IAAI,IAAI,CAAC,aAAc;AAGtE,IAAI,mBAAmB,MAAM;CAC3B,YAAY,SAAS,UAAU,QAAQ;AACrC,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,SAAS;AACd,OAAK,uBAAuB;CAC7B;CACD,IAAI,mBAAmB;EACrB,IAAI,IAAI;AACR,UAAQ,MAAM,KAAK,KAAK,SAAS,qBAAqB,OAAO,KAAK,sBAAsB,KAAK,aAAa,OAAO,KAAK;CACvH;CACD,IAAI,WAAW;AACb,SAAO,KAAK,OAAO;CACpB;CACD,MAAM,WAAW,EACf,QACA,GACA,MACA,aACA,MACA,iBACA,SACA,aACD,EAAE;EACD,IAAI,IAAI,IAAI,IAAI;EAChB,MAAM,WAAW,CAAE;AACnB,MAAI,eAAe,KACjB,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;GACT,SAAS;EACV,EAAC;AAEJ,MAAI,QAAQ,KACV,UAAS,KAAK;GAAE,MAAM;GAAuB,SAAS;EAAQ,EAAC;EAEjE,MAAM,eAAe,MAAM,MAAM,KAAK,KAAK,OAAO,cAAc,YAAY,IAAI,GAAG,gBAAgB,YAAY,IAAI,GAAG,KAAK,GAAG,KAAK,OAAO,qBAAqB,IAAI;EACnK,MAAM,EAAE,OAAO,UAAU,iBAAiB,GAAG,MAAM,cAAe;GAChE,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ;GACxD,MAAM;IACJ,OAAO,KAAK;IACZ;IACA;IACA;IACA,IAAI,KAAK,gBAAgB,WAAW,OAAO,KAAK,CAAE;IAClD,IAAI,yBAAyB,IAAI,KAAK,QAAQ,GAAG,EAAE,iBAAiB,WAAY,IAAG,CAAE;GACtF;GACD,uBAAuB;GACvB,2BAA2B,0BACzB,0BACD;GACD;GACA,OAAO,KAAK,OAAO;EACpB,EAAC;AACF,SAAO;GACL,QAAQ,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,SAAS;GAClD;GACA,UAAU;IACR,WAAW;IACX,SAAS,KAAK;IACd,SAAS;GACV;EACF;CACF;AACF;AACD,IAAI,4BAA4B,WAAU,EACxC,MAAM,UAAS,WAAU,EAAE,UAAU,YAAW,CAAE,EAAC,CAAC,CACrD,EAAC;AAWF,IAAI,8BAA8B,WAAU;CAC1C,SAAS,UAAS,YAAW,CAAC,CAAC,SAAS;CACxC,UAAU,YAAW,CAAC,SAAS;CAC/B,QAAQ,YAAW,CAAC,SAAS;CAC7B,aAAa,YAAW,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE;CAC3D,wBAAwB,UAAS,SAAQ,CAAC,QAAQ,SAAU,EAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAU,EAAC;AAC9F,EAAC;AACF,IAAI,cAAc;CAChB,WAAW;CACX,QAAQ;CACR,UAAU;CACV,aAAa;CACb,YAAY;CACZ,SAAS;CACT,WAAW;CACX,SAAS;CACT,SAAS;CACT,UAAU;CACV,OAAO;CACP,QAAQ;CACR,OAAO;CACP,SAAS;CACT,UAAU;CACV,SAAS;CACT,QAAQ;CACR,UAAU;CACV,QAAQ;CACR,OAAO;CACP,QAAQ;CACR,OAAO;CACP,WAAW;CACX,WAAW;CACX,YAAY;CACZ,SAAS;CACT,UAAU;CACV,SAAS;CACT,QAAQ;CACR,QAAQ;CACR,SAAS;CACT,YAAY;CACZ,YAAY;CACZ,OAAO;CACP,SAAS;CACT,OAAO;CACP,QAAQ;CACR,WAAW;CACX,SAAS;CACT,QAAQ;CACR,YAAY;CACZ,UAAU;CACV,SAAS;CACT,SAAS;CACT,QAAQ;CACR,WAAW;CACX,SAAS;CACT,SAAS;CACT,SAAS;CACT,SAAS;CACT,OAAO;CACP,MAAM;CACN,SAAS;CACT,WAAW;CACX,MAAM;CACN,YAAY;CACZ,OAAO;AACR;AACD,IAAI,2BAA2B,MAAM;CACnC,YAAY,SAAS,QAAQ;AAC3B,OAAK,UAAU;AACf,OAAK,SAAS;AACd,OAAK,uBAAuB;CAC7B;CACD,IAAI,WAAW;AACb,SAAO,KAAK,OAAO;CACpB;CACD,QAAQ,EACN,OACA,WACA,iBACD,EAAE;EACD,IAAI,IAAI,IAAI,IAAI,IAAI;EACpB,MAAM,WAAW,CAAE;EACnB,MAAM,gBAAgB,qBAAqB;GACzC,UAAU;GACV;GACA,QAAQ;EACT,EAAC;EACF,MAAM,WAAW,IAAI;EACrB,MAAM,OAAO,iBAAiB,aAAa,IAAI,KAAK,CAAC,KAAM,KAAI,IAAI,KAAK,CAAC,0BAA0B,MAAM,AAAC;AAC1G,WAAS,OAAO,SAAS,KAAK,QAAQ;AACtC,WAAS,OAAO,QAAQ,IAAI,KAAK,CAAC,IAAK,GAAE,SAAS,EAAE,MAAM,UAAW,GAAE;AACvE,MAAI,eAAe;GACjB,MAAM,4BAA4B;IAChC,UAAU,KAAK,cAAc,YAAY,OAAO,UAAU;IAC1D,WAAW,KAAK,cAAc,aAAa,OAAO,UAAU;IAC5D,SAAS,KAAK,cAAc,WAAW,OAAO,UAAU;IACxD,cAAc,KAAK,cAAc,gBAAgB,OAAO,UAAU;IAClE,0BAA0B,KAAK,cAAc,2BAA2B,OAAO,UAAU;GAC1F;AACD,QAAK,MAAM,OAAO,2BAA2B;IAC3C,MAAM,QAAQ,0BAA0B;AACxC,QAAI,eAAe,EACjB,UAAS,OAAO,KAAK,OAAO,MAAM,CAAC;GAEtC;EACF;AACD,SAAO;GACL;GACA;EACD;CACF;CACD,MAAM,WAAW,SAAS;EACxB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;EACxB,MAAM,eAAe,MAAM,MAAM,KAAK,KAAK,OAAO,cAAc,YAAY,IAAI,GAAG,gBAAgB,YAAY,IAAI,GAAG,KAAK,GAAG,KAAK,OAAO,qBAAqB,IAAI;EACnK,MAAM,EAAE,UAAU,UAAU,GAAG,KAAK,QAAQ,QAAQ;EACpD,MAAM,EACJ,OAAO,UACP,iBACA,UAAU,aACX,GAAG,MAAM,kBAAkB;GAC1B,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAChE;GACA,uBAAuB;GACvB,2BAA2B,0BACzB,kCACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,WAAW,SAAS,YAAY,QAAQ,SAAS,YAAY,cAAc,YAAY,SAAS,iBAAiB;AACvH,SAAO;GACL,MAAM,SAAS;GACf,WAAW,MAAM,KAAK,SAAS,UAAU,YAAY,IAAI,GAAG,IAAI,CAAC,UAAU;IACzE,MAAM,KAAK;IACX,aAAa,KAAK;IAClB,WAAW,KAAK;GACjB,GAAE,KAAK,OAAO,KAAK,CAAE;GACtB;GACA,oBAAoB,KAAK,SAAS,aAAa,OAAO,UAAU;GAChE;GACA,UAAU;IACR,WAAW;IACX,SAAS,KAAK;IACd,SAAS;IACT,MAAM;GACP;EACF;CACF;AACF;AACD,IAAI,oCAAoC,WAAU;CAChD,MAAM,YAAW;CACjB,UAAU,YAAW,CAAC,SAAS;CAC/B,UAAU,YAAW,CAAC,SAAS;CAC/B,OAAO,UACL,WAAU;EACR,MAAM,YAAW;EACjB,OAAO,YAAW;EAClB,KAAK,YAAW;CACjB,EAAC,CACH,CAAC,SAAS;AACZ,EAAC;AAkBF,SAAS,iCAAiC,EACxC,QACA,mBACD,EAAE;CACD,MAAM,WAAW,CAAE;CACnB,MAAM,WAAW,CAAE;AACnB,MAAK,MAAM,EAAE,MAAM,SAAS,IAAI,OAC9B,SAAQ,MAAR;EACE,KAAK,UAAU;AACb,WAAQ,mBAAR;IACE,KAAK,UAAU;AACb,cAAS,KAAK;MAAE,MAAM;MAAU;KAAS,EAAC;AAC1C;IACD;IACD,KAAK,aAAa;AAChB,cAAS,KAAK;MAAE,MAAM;MAAa;KAAS,EAAC;AAC7C;IACD;IACD,KAAK,UAAU;AACb,cAAS,KAAK;MACZ,MAAM;MACN,SAAS;KACV,EAAC;AACF;IACD;IACD,SAAS;KACP,MAAM,mBAAmB;AACzB,WAAM,IAAI,OACP,mCAAmC;IAEvC;GACF;AACD;EACD;EACD,KAAK,QAAQ;AACX,YAAS,KAAK;IACZ,MAAM;IACN,SAAS,QAAQ,IAAI,CAAC,MAAM,UAAU;KACpC,IAAI,IAAI,IAAI,IAAI;AAChB,aAAQ,KAAK,MAAb;MACE,KAAK,OACH,QAAO;OAAE,MAAM;OAAc,MAAM,KAAK;MAAM;MAEhD,KAAK,QACH,QAAO;OACL,MAAM;OACN,WAAW,KAAK,iBAAiB,MAAM,KAAK,MAAM,UAAU,IAAI,QAAQ,KAAK,KAAK,aAAa,OAAO,KAAK,aAAa,UAAU,0BAA2B,KAAK,MAAM;OAExK,SAAS,MAAM,KAAK,KAAK,qBAAqB,YAAY,IAAI,GAAG,WAAW,YAAY,IAAI,GAAG;MAChG;MAEH,KAAK,QAAQ;AACX,WAAI,KAAK,gBAAgB,IACvB,OAAM,IAAIC,8BAA+B,EACvC,eAAe,6BAChB;AAEH,eAAQ,KAAK,UAAb;QACE,KAAK,kBACH,QAAO;SACL,MAAM;SACN,WAAW,KAAK,KAAK,aAAa,OAAO,MAAM,OAAO,MAAM;SAC5D,YAAY,8BAA8B,KAAK;QAChD;QAEH,QACE,OAAM,IAAIA,8BAA+B,EACvC,eAAe,gDAChB;OAEJ;MACF;KACF;IACF,EAAC;GACH,EAAC;AACF;EACD;EACD,KAAK,aAAa;AAChB,QAAK,MAAM,QAAQ,QACjB,SAAQ,KAAK,MAAb;IACE,KAAK,QAAQ;AACX,cAAS,KAAK;MACZ,MAAM;MACN,SAAS,CAAC;OAAE,MAAM;OAAe,MAAM,KAAK;MAAM,CAAC;KACpD,EAAC;AACF;IACD;IACD,KAAK,aAAa;AAChB,cAAS,KAAK;MACZ,MAAM;MACN,SAAS,KAAK;MACd,MAAM,KAAK;MACX,WAAW,KAAK,UAAU,KAAK,KAAK;KACrC,EAAC;AACF;IACD;GACF;AAEH;EACD;EACD,KAAK,QAAQ;AACX,QAAK,MAAM,QAAQ,QACjB,UAAS,KAAK;IACZ,MAAM;IACN,SAAS,KAAK;IACd,QAAQ,KAAK,UAAU,KAAK,OAAO;GACpC,EAAC;AAEJ;EACD;EACD,SAAS;GACP,MAAM,mBAAmB;AACzB,SAAM,IAAI,OAAO,oBAAoB;EACtC;CACF;AAEH,QAAO;EAAE;EAAU;CAAU;AAC9B;AAGD,SAAS,8BAA8B,EACrC,cACA,cACD,EAAE;AACD,SAAQ,cAAR;EACE,UAAU;EACV,KAAK,KACH,QAAO,eAAe,eAAe;EACvC,KAAK,oBACH,QAAO;EACT,KAAK,iBACH,QAAO;EACT,QACE,QAAO,eAAe,eAAe;CACxC;AACF;AAMD,SAAS,sBAAsB,EAC7B,MACA,QACD,EAAE;CACD,IAAI;CACJ,MAAM,UAAU,KAAK,KAAK,UAAU,YAAY,IAAI,GAAG,UAAU,KAAK,aAAa;CACnF,MAAM,eAAe,CAAE;AACvB,KAAI,SAAS,KACX,QAAO;EAAE,YAAY;EAAG,kBAAkB;EAAG;CAAc;CAE7D,MAAM,aAAa,KAAK;CACxB,MAAM,eAAe,CAAE;AACvB,MAAK,MAAM,QAAQ,MACjB,SAAQ,KAAK,MAAb;EACE,KAAK;AACH,gBAAa,KAAK;IAChB,MAAM;IACN,MAAM,KAAK;IACX,aAAa,KAAK;IAClB,YAAY,KAAK;IACjB,QAAQ,SAAS,YAAY;GAC9B,EAAC;AACF;EACF,KAAK;AACH,WAAQ,KAAK,IAAb;IACE,KAAK;AACH,kBAAa,KAAK;MAChB,MAAM;MACN,qBAAqB,KAAK,KAAK;MAC/B,eAAe,KAAK,KAAK;KAC1B,EAAC;AACF;IACF;AACE,kBAAa,KAAK;MAAE,MAAM;MAAoB;KAAM,EAAC;AACrD;GACH;AACD;EACF;AACE,gBAAa,KAAK;IAAE,MAAM;IAAoB;GAAM,EAAC;AACrD;CACH;AAEH,KAAI,cAAc,KAChB,QAAO;EAAE,OAAO;EAAc,kBAAkB;EAAG;CAAc;CAEnE,MAAM,OAAO,WAAW;AACxB,SAAQ,MAAR;EACE,KAAK;EACL,KAAK;EACL,KAAK,WACH,QAAO;GAAE,OAAO;GAAc,aAAa;GAAM;EAAc;EACjE,KAAK,QAAQ;AACX,OAAI,WAAW,aAAa,qBAC1B,QAAO;IACL,OAAO;IACP,aAAa,EACX,MAAM,qBACP;IACD;GACD;AAEH,UAAO;IACL,OAAO;IACP,aAAa;KACX,MAAM;KACN,MAAM,WAAW;IAClB;IACD;GACD;EACF;EACD,SAAS;GACP,MAAM,mBAAmB;AACzB,SAAM,IAAIC,8BAA+B,EACvC,gBAAgB,gCAAgC,mBACjD;EACF;CACF;AACF;AAGD,IAAI,+BAA+B,MAAM;CACvC,YAAY,SAAS,QAAQ;AAC3B,OAAK,uBAAuB;AAC5B,OAAK,8BAA8B;AACnC,OAAK,4BAA4B;AACjC,OAAK,UAAU;AACf,OAAK,SAAS;CACf;CACD,IAAI,WAAW;AACb,SAAO,KAAK,OAAO;CACpB;CACD,QAAQ,EACN,MACA,WACA,aACA,eACA,MACA,MACA,iBACA,kBACA,MACA,QACA,kBACA,gBACD,EAAE;EACD,IAAI,IAAI,IAAI;EACZ,MAAM,WAAW,CAAE;EACnB,MAAM,cAAc,wBAAwB,KAAK,QAAQ;EACzD,MAAM,OAAO,KAAK;AAClB,MAAI,QAAQ,KACV,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;EACV,EAAC;AAEJ,MAAI,QAAQ,KACV,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;EACV,EAAC;AAEJ,MAAI,mBAAmB,KACrB,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;EACV,EAAC;AAEJ,MAAI,oBAAoB,KACtB,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;EACV,EAAC;AAEJ,MAAI,iBAAiB,KACnB,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;EACV,EAAC;EAEJ,MAAM,EAAE,UAAU,UAAU,iBAAiB,GAAG,iCAAiC;GAC/E;GACA,mBAAmB,YAAY;EAChC,EAAC;AACF,WAAS,KAAK,GAAG,gBAAgB;EACjC,MAAM,gBAAgB,qBAAsB;GAC1C,UAAU;GACV,iBAAiB;GACjB,QAAQ;EACT,EAAC;EACF,MAAM,YAAY,KAAK,iBAAiB,YAAY,IAAI,cAAc,kBAAkB,OAAO,KAAK;EACpG,MAAM,WAAW;GACf,OAAO,KAAK;GACZ,OAAO;GACP;GACA,OAAO;GACP,mBAAmB;GACnB,IAAI,kBAAkB,YAAY,IAAI,eAAe,UAAU,UAAU,EACvE,MAAM,EACJ,QAAQ,eAAe,UAAU,OAAO;IACtC,MAAM;IACN,QAAQ;IACR,OAAO,KAAK,eAAe,SAAS,OAAO,KAAK;IAChD,aAAa,eAAe;IAC5B,QAAQ,eAAe;GACxB,IAAG,EAAE,MAAM,cAAe,EAC5B,EACF;GAED,UAAU,iBAAiB,YAAY,IAAI,cAAc;GACzD,qBAAqB,iBAAiB,YAAY,IAAI,cAAc;GACpE,sBAAsB,iBAAiB,YAAY,IAAI,cAAc;GACrE,OAAO,iBAAiB,YAAY,IAAI,cAAc;GACtD,MAAM,iBAAiB,YAAY,IAAI,cAAc;GACrD,cAAc,iBAAiB,YAAY,IAAI,cAAc;GAE7D,GAAG,YAAY,sBAAsB,iBAAiB,YAAY,IAAI,cAAc,oBAAoB,SAAS,iBAAiB,YAAY,IAAI,cAAc,qBAAqB,SAAS,EAC5L,WAAW;IACT,IAAI,iBAAiB,YAAY,IAAI,cAAc,oBAAoB,QAAQ,EAC7E,QAAQ,cAAc,gBACvB;IACD,IAAI,iBAAiB,YAAY,IAAI,cAAc,qBAAqB,QAAQ,EAC9E,SAAS,cAAc,iBACxB;GACF,EACF;GACD,GAAG,YAAY,0BAA0B,EACvC,YAAY,OACb;EACF;AACD,MAAI,YAAY,kBAAkB;AAChC,OAAI,SAAS,eAAe,MAAM;AAChC,aAAS,mBAAmB;AAC5B,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;KACT,SAAS;IACV,EAAC;GACH;AACD,OAAI,SAAS,SAAS,MAAM;AAC1B,aAAS,aAAa;AACtB,aAAS,KAAK;KACZ,MAAM;KACN,SAAS;KACT,SAAS;IACV,EAAC;GACH;EACF;AACD,UAAQ,MAAR;GACE,KAAK,WAAW;IACd,MAAM,EAAE,OAAO,aAAa,cAAc,GAAG,sBAAsB;KACjE;KACA,QAAQ;IAET,EAAC;AACF,WAAO;KACL,MAAM;MACJ,GAAG;MACH;MACA;KACD;KACD,UAAU,CAAC,GAAG,UAAU,GAAG,YAAa;IACzC;GACF;GACD,KAAK,cACH,QAAO;IACL,MAAM;KACJ,GAAG;KACH,MAAM,EACJ,QAAQ,KAAK,UAAU,OAAO;MAC5B,MAAM;MACN,QAAQ;MACR,OAAO,KAAK,KAAK,SAAS,OAAO,KAAK;MACtC,aAAa,KAAK;MAClB,QAAQ,KAAK;KACd,IAAG,EAAE,MAAM,cAAe,EAC5B;IACF;IACD;GACD;GAEH,KAAK,cACH,QAAO;IACL,MAAM;KACJ,GAAG;KACH,aAAa;MAAE,MAAM;MAAY,MAAM,KAAK,KAAK;KAAM;KACvD,OAAO,CACL;MACE,MAAM;MACN,MAAM,KAAK,KAAK;MAChB,aAAa,KAAK,KAAK;MACvB,YAAY,KAAK,KAAK;MACtB,QAAQ;KACT,CACF;IACF;IACD;GACD;GAEH,SAAS;IACP,MAAM,mBAAmB;AACzB,UAAM,IAAI,OAAO,oBAAoB;GACtC;EACF;CACF;CACD,MAAM,WAAW,SAAS;EACxB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;EAC5B,MAAM,EAAE,MAAM,MAAM,UAAU,GAAG,KAAK,QAAQ,QAAQ;EACtD,MAAM,EACJ,iBACA,OAAO,UACP,UAAU,aACX,GAAG,MAAM,cAAe;GACvB,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAChE;GACA,uBAAuB;GACvB,2BAA2B,0BACzB,WAAU;IACR,IAAI,YAAW;IACf,YAAY,YAAW;IACvB,OAAO,YAAW;IAClB,QAAQ,UACN,uBAAsB,QAAQ;KAC5B,WAAU;MACR,MAAM,YAAW,UAAU;MAC3B,MAAM,YAAW,YAAY;MAC7B,SAAS,UACP,WAAU;OACR,MAAM,YAAW,cAAc;OAC/B,MAAM,YAAW;OACjB,aAAa,UACX,WAAU;QACR,MAAM,YAAW,eAAe;QAChC,aAAa,YAAW;QACxB,WAAW,YAAW;QACtB,KAAK,YAAW;QAChB,OAAO,YAAW;OACnB,EAAC,CACH;MACF,EAAC,CACH;KACF,EAAC;KACF,WAAU;MACR,MAAM,YAAW,gBAAgB;MACjC,SAAS,YAAW;MACpB,MAAM,YAAW;MACjB,WAAW,YAAW;KACvB,EAAC;KACF,WAAU,EACR,MAAM,YAAW,kBAAkB,CACpC,EAAC;KACF,WAAU,EACR,MAAM,YAAW,gBAAgB,CAClC,EAAC;KACF,WAAU;MACR,MAAM,YAAW,YAAY;MAC7B,SAAS,UACP,WAAU;OACR,MAAM,YAAW,eAAe;OAChC,MAAM,YAAW;MAClB,EAAC,CACH;KACF,EAAC;IACH,EAAC,CACH;IACD,oBAAoB,WAAU,EAAE,QAAQ,YAAW,CAAE,EAAC,CAAC,UAAU;IACjE,OAAO;GACR,EAAC,CACH;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,qBAAqB,SAAS,OAAO,OAAO,CAAC,WAAW,OAAO,SAAS,UAAU,CAAC,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC,OAAO,CAAC,YAAY,QAAQ,SAAS,cAAc;EAChL,MAAM,YAAY,SAAS,OAAO,OAAO,CAAC,WAAW,OAAO,SAAS,gBAAgB,CAAC,IAAI,CAAC,YAAY;GACrG,cAAc;GACd,YAAY,OAAO;GACnB,UAAU,OAAO;GACjB,MAAM,OAAO;EACd,GAAE;EACH,MAAM,oBAAoB,MAAM,KAAK,SAAS,OAAO,KAAK,CAAC,SAAS,KAAK,SAAS,YAAY,KAAK,YAAY,IAAI,GAAG,YAAY,OAAO,KAAK;AAC9I,SAAO;GACL,MAAM,mBAAmB,IAAI,CAAC,YAAY,QAAQ,KAAK,CAAC,KAAK,KAAK;GAClE,SAAS,mBAAmB,QAC1B,CAAC,YAAY,QAAQ,YAAY,IAAI,CAAC,eAAe;IACnD,IAAI,KAAK,KAAK;AACd,WAAO;KACL,YAAY;KACZ,KAAK,OAAO,OAAO,MAAM,KAAK,QAAQ,eAAe,YAAY,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,MAAM,YAAa;KACjH,KAAK,WAAW;KAChB,OAAO,WAAW;IACnB;GACF,EAAC,CACH;GACD,cAAc,8BAA8B;IAC1C,eAAe,KAAK,SAAS,uBAAuB,YAAY,IAAI,GAAG;IACvE,cAAc,UAAU,SAAS;GAClC,EAAC;GACF,WAAW,UAAU,SAAS,IAAI,iBAAiB;GACnD,WAAW,mBAAmB,iBAAiB,IAAI,CAAC,aAAa;IAC/D,MAAM;IACN,MAAM,QAAQ;GACf,GAAE,QAAQ;GACX,OAAO;IACL,cAAc,SAAS,MAAM;IAC7B,kBAAkB,SAAS,MAAM;GAClC;GACD,SAAS;IACP,gBAAgB;IAChB,aAAa,CAAE;GAChB;GACD,aAAa;IACX,SAAS;IACT,MAAM;GACP;GACD,SAAS,EACP,MAAM,KAAK,UAAU,KAAK,CAC3B;GACD,UAAU;IACR,IAAI,SAAS;IACb,2BAAW,IAAI,KAAK,SAAS,aAAa;IAC1C,SAAS,SAAS;GACnB;GACD,kBAAkB,EAChB,QAAQ;IACN,YAAY,SAAS;IACrB,qBAAqB,MAAM,KAAK,SAAS,MAAM,yBAAyB,YAAY,IAAI,GAAG,kBAAkB,OAAO,KAAK;IACzH,kBAAkB,MAAM,KAAK,SAAS,MAAM,0BAA0B,YAAY,IAAI,GAAG,qBAAqB,OAAO,KAAK;GAC3H,EACF;GACD;EACD;CACF;CACD,MAAM,SAAS,SAAS;EACtB,MAAM,EAAE,MAAM,MAAM,UAAU,GAAG,KAAK,QAAQ,QAAQ;EACtD,MAAM,EAAE,iBAAiB,OAAO,UAAU,GAAG,MAAM,cAAe;GAChE,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAChE,MAAM;IACJ,GAAG;IACH,QAAQ;GACT;GACD,uBAAuB;GACvB,2BAA2B,iCACzB,2BACD;GACD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;EACF,MAAM,OAAO;EACb,IAAI,eAAe;EACnB,IAAI,eAAe;EACnB,IAAI,mBAAmB;EACvB,IAAI,qBAAqB;EACzB,IAAI,kBAAkB;EACtB,IAAI,aAAa;EACjB,MAAM,mBAAmB,CAAE;EAC3B,IAAI,eAAe;AACnB,SAAO;GACL,QAAQ,SAAS,YACf,IAAI,gBAAgB;IAClB,UAAU,OAAO,YAAY;KAC3B,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChC,UAAK,MAAM,SAAS;AAClB,qBAAe;AACf,iBAAW,QAAQ;OAAE,MAAM;OAAS,OAAO,MAAM;MAAO,EAAC;AACzD;KACD;KACD,MAAM,QAAQ,MAAM;AACpB,SAAI,+BAA+B,MAAM,EACvC;UAAI,MAAM,KAAK,SAAS,iBAAiB;AACvC,wBAAiB,MAAM,gBAAgB;QACrC,UAAU,MAAM,KAAK;QACrB,YAAY,MAAM,KAAK;OACxB;AACD,kBAAW,QAAQ;QACjB,MAAM;QACN,cAAc;QACd,YAAY,MAAM,KAAK;QACvB,UAAU,MAAM,KAAK;QACrB,eAAe,MAAM,KAAK;OAC3B,EAAC;MACH;gBACQ,0CAA0C,MAAM,EAAE;MAC3D,MAAM,WAAW,iBAAiB,MAAM;AACxC,UAAI,YAAY,KACd,YAAW,QAAQ;OACjB,MAAM;OACN,cAAc;OACd,YAAY,SAAS;OACrB,UAAU,SAAS;OACnB,eAAe,MAAM;MACtB,EAAC;KAEL,WAAU,uBAAuB,MAAM,EAAE;AACxC,mBAAa,MAAM,SAAS;AAC5B,iBAAW,QAAQ;OACjB,MAAM;OACN,IAAI,MAAM,SAAS;OACnB,2BAAW,IAAI,KAAK,MAAM,SAAS,aAAa;OAChD,SAAS,MAAM,SAAS;MACzB,EAAC;KACH,WAAU,iBAAiB,MAAM,CAChC,YAAW,QAAQ;MACjB,MAAM;MACN,WAAW,MAAM;KAClB,EAAC;cACO,yCAAyC,MAAM,CACxD,YAAW,QAAQ;MACjB,MAAM;MACN,WAAW,MAAM;KAClB,EAAC;cACO,8BAA8B,MAAM,IAAI,MAAM,KAAK,SAAS,iBAAiB;AACtF,uBAAiB,MAAM,qBAAqB;AAC5C,qBAAe;AACf,iBAAW,QAAQ;OACjB,MAAM;OACN,cAAc;OACd,YAAY,MAAM,KAAK;OACvB,UAAU,MAAM,KAAK;OACrB,MAAM,MAAM,KAAK;MAClB,EAAC;KACH,WAAU,wBAAwB,MAAM,EAAE;AACzC,qBAAe,8BAA8B;OAC3C,eAAe,KAAK,MAAM,SAAS,uBAAuB,YAAY,IAAI,GAAG;OAC7E;MACD,EAAC;AACF,qBAAe,MAAM,SAAS,MAAM;AACpC,yBAAmB,MAAM,SAAS,MAAM;AACxC,4BAAsB,MAAM,KAAK,MAAM,SAAS,MAAM,yBAAyB,YAAY,IAAI,GAAG,kBAAkB,OAAO,KAAK;AAChI,yBAAmB,MAAM,KAAK,MAAM,SAAS,MAAM,0BAA0B,YAAY,IAAI,GAAG,qBAAqB,OAAO,KAAK;KAClI,WAAU,+BAA+B,MAAM,CAC9C,YAAW,QAAQ;MACjB,MAAM;MACN,QAAQ;OACN,YAAY;OACZ,KAAK,MAAM,MAAM,KAAK,KAAK,QAAQ,eAAe,YAAY,IAAI,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,YAAa;OAC3G,KAAK,MAAM,WAAW;OACtB,OAAO,MAAM,WAAW;MACzB;KACF,EAAC;IAEL;IACD,MAAM,YAAY;AAChB,gBAAW,QAAQ;MACjB,MAAM;MACN;MACA,OAAO;OAAE;OAAc;MAAkB;MACzC,IAAI,sBAAsB,QAAQ,mBAAmB,SAAS,EAC5D,kBAAkB,EAChB,QAAQ;OACN;OACA;OACA;MACD,EACF,EACF;KACF,EAAC;IACH;GACF,GACF;GACD,SAAS;IACP,gBAAgB;IAChB,aAAa,CAAE;GAChB;GACD,aAAa,EAAE,SAAS,gBAAiB;GACzC,SAAS,EAAE,MAAM,KAAK,UAAU,KAAK,CAAE;GACvC;EACD;CACF;AACF;AACD,IAAI,cAAc,WAAU;CAC1B,cAAc,YAAW;CACzB,sBAAsB,WAAU,EAAE,eAAe,YAAW,CAAC,SAAS,CAAE,EAAC,CAAC,SAAS;CACnF,eAAe,YAAW;CAC1B,uBAAuB,WAAU,EAAE,kBAAkB,YAAW,CAAC,SAAS,CAAE,EAAC,CAAC,SAAS;AACxF,EAAC;AACF,IAAI,uBAAuB,WAAU;CACnC,MAAM,YAAW,6BAA6B;CAC9C,OAAO,YAAW;AACnB,EAAC;AACF,IAAI,8BAA8B,WAAU;CAC1C,MAAM,SAAQ,CAAC,sBAAsB,qBAAsB,EAAC;CAC5D,UAAU,WAAU;EAClB,oBAAoB,WAAU,EAAE,QAAQ,YAAW,CAAE,EAAC,CAAC,SAAS;EAChE,OAAO;CACR,EAAC;AACH,EAAC;AACF,IAAI,6BAA6B,WAAU;CACzC,MAAM,YAAW,mBAAmB;CACpC,UAAU,WAAU;EAClB,IAAI,YAAW;EACf,YAAY,YAAW;EACvB,OAAO,YAAW;CACnB,EAAC;AACH,EAAC;AACF,IAAI,+BAA+B,WAAU;CAC3C,MAAM,YAAW,4BAA4B;CAC7C,cAAc,YAAW;CACzB,MAAM,uBAAsB,QAAQ,CAClC,WAAU,EACR,MAAM,YAAW,UAAU,CAC5B,EAAC,EACF,WAAU;EACR,MAAM,YAAW,gBAAgB;EACjC,IAAI,YAAW;EACf,SAAS,YAAW;EACpB,MAAM,YAAW;EACjB,WAAW,YAAW;EACtB,QAAQ,YAAW,YAAY;CAChC,EAAC,AACH,EAAC;AACH,EAAC;AACF,IAAI,2CAA2C,WAAU;CACvD,MAAM,YAAW,yCAAyC;CAC1D,SAAS,YAAW;CACpB,cAAc,YAAW;CACzB,OAAO,YAAW;AACnB,EAAC;AACF,IAAI,gCAAgC,WAAU;CAC5C,MAAM,YAAW,6BAA6B;CAC9C,cAAc,YAAW;CACzB,MAAM,uBAAsB,QAAQ,CAClC,WAAU,EACR,MAAM,YAAW,UAAU,CAC5B,EAAC,EACF,WAAU;EACR,MAAM,YAAW,gBAAgB;EACjC,IAAI,YAAW;EACf,SAAS,YAAW;EACpB,MAAM,YAAW;EACjB,WAAW,YAAW;CACvB,EAAC,AACH,EAAC;AACH,EAAC;AACF,IAAI,gCAAgC,WAAU;CAC5C,MAAM,YAAW,wCAAwC;CACzD,YAAY,WAAU;EACpB,MAAM,YAAW,eAAe;EAChC,KAAK,YAAW;EAChB,OAAO,YAAW;CACnB,EAAC;AACH,EAAC;AACF,IAAI,0CAA0C,WAAU;CACtD,MAAM,YAAW,wCAAwC;CACzD,SAAS,YAAW;CACpB,cAAc,YAAW;CACzB,eAAe,YAAW;CAC1B,OAAO,YAAW;AACnB,EAAC;AACF,IAAI,6BAA6B,UAAS;CACxC;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,WAAU,EAAE,MAAM,YAAW,CAAE,EAAC,CAAC,aAAa;AAE/C,EAAC;AACF,SAAS,iBAAiB,OAAO;AAC/B,QAAO,MAAM,SAAS;AACvB;AACD,SAAS,8BAA8B,OAAO;AAC5C,QAAO,MAAM,SAAS;AACvB;AACD,SAAS,wBAAwB,OAAO;AACtC,QAAO,MAAM,SAAS,wBAAwB,MAAM,SAAS;AAC9D;AACD,SAAS,uBAAuB,OAAO;AACrC,QAAO,MAAM,SAAS;AACvB;AACD,SAAS,0CAA0C,OAAO;AACxD,QAAO,MAAM,SAAS;AACvB;AACD,SAAS,+BAA+B,OAAO;AAC7C,QAAO,MAAM,SAAS;AACvB;AACD,SAAS,+BAA+B,OAAO;AAC7C,QAAO,MAAM,SAAS;AACvB;AACD,SAAS,yCAAyC,OAAO;AACvD,QAAO,MAAM,SAAS;AACvB;AACD,SAAS,wBAAwB,SAAS;AACxC,KAAI,QAAQ,WAAW,IAAI,EAAE;AAC3B,MAAI,QAAQ,WAAW,UAAU,IAAI,QAAQ,WAAW,aAAa,CACnE,QAAO;GACL,kBAAkB;GAClB,mBAAmB;GACnB,wBAAwB;EACzB;AAEH,SAAO;GACL,kBAAkB;GAClB,mBAAmB;GACnB,wBAAwB;EACzB;CACF;AACD,QAAO;EACL,kBAAkB;EAClB,mBAAmB;EACnB,wBAAwB;CACzB;AACF;AACD,IAAI,uCAAuC,WAAU;CACnD,UAAU,SAAQ,CAAC,SAAS;CAC5B,mBAAmB,aAAY,CAAC,SAAS;CACzC,oBAAoB,YAAW,CAAC,SAAS;CACzC,OAAO,aAAY,CAAC,SAAS;CAC7B,MAAM,YAAW,CAAC,SAAS;CAC3B,iBAAiB,YAAW,CAAC,SAAS;CACtC,eAAe,aAAY,CAAC,SAAS;CACrC,cAAc,YAAW,CAAC,SAAS;CACnC,kBAAkB,YAAW,CAAC,SAAS;AACxC,EAAC;AAIF,IAAI,6BAA6B,WAAU,CAAE,EAAC;AAC9C,SAAS,qBAAqB,EAC5B,mBACA,cACD,GAAG,CAAE,GAAE;AACN,QAAO;EACL,MAAM;EACN,IAAI;EACJ,MAAM;GACJ;GACA;EACD;EACD,YAAY;CACb;AACF;AACD,IAAI,cAAc,EAChB,kBAAkB,qBACnB;AAUD,IAAI,8BAA8B,WAAU;CAC1C,cAAc,YAAW,CAAC,SAAS;CACnC,OAAO,YAAW,CAAC,IAAI,IAAK,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS;AACzD,EAAC;AACF,IAAI,oBAAoB,MAAM;CAC5B,YAAY,SAAS,QAAQ;AAC3B,OAAK,UAAU;AACf,OAAK,SAAS;AACd,OAAK,uBAAuB;CAC7B;CACD,IAAI,WAAW;AACb,SAAO,KAAK,OAAO;CACpB;CACD,QAAQ,EACN,MACA,QAAQ,SACR,eAAe,OACf,OACA,cACA,iBACD,EAAE;EACD,MAAM,WAAW,CAAE;EACnB,MAAM,gBAAgB,qBAAsB;GAC1C,UAAU;GACV;GACA,QAAQ;EACT,EAAC;EACF,MAAM,cAAc;GAClB,OAAO,KAAK;GACZ,OAAO;GACP;GACA,iBAAiB;GACjB;GACA;EACD;AACD,MAAI,aACF,KAAI;GAAC;GAAO;GAAQ;GAAO;GAAQ;GAAO;EAAM,EAAC,SAAS,aAAa,CACrE,aAAY,kBAAkB;MAE9B,UAAS,KAAK;GACZ,MAAM;GACN,SAAS;GACT,UAAU,6BAA6B,aAAa;EACrD,EAAC;AAGN,MAAI,eAAe;GACjB,MAAM,qBAAqB,CAAE;AAC7B,QAAK,MAAM,OAAO,oBAAoB;IACpC,MAAM,QAAQ,mBAAmB;AACjC,QAAI,eAAe,EACjB,aAAY,OAAO;GAEtB;EACF;AACD,SAAO;GACL;GACA;EACD;CACF;CACD,MAAM,WAAW,SAAS;EACxB,IAAI,IAAI,IAAI;EACZ,MAAM,eAAe,MAAM,MAAM,KAAK,KAAK,OAAO,cAAc,YAAY,IAAI,GAAG,gBAAgB,YAAY,IAAI,GAAG,KAAK,GAAG,KAAK,OAAO,qBAAqB,IAAI;EACnK,MAAM,EAAE,aAAa,UAAU,GAAG,KAAK,QAAQ,QAAQ;EACvD,MAAM,EACJ,OAAO,OACP,iBACA,UAAU,aACX,GAAG,MAAM,cAAe;GACvB,KAAK,KAAK,OAAO,IAAI;IACnB,MAAM;IACN,SAAS,KAAK;GACf,EAAC;GACF,SAAS,eAAgB,KAAK,OAAO,SAAS,EAAE,QAAQ,QAAQ;GAChE,MAAM;GACN,uBAAuB;GACvB,2BAA2B,6BAA6B;GACxD,aAAa,QAAQ;GACrB,OAAO,KAAK,OAAO;EACpB,EAAC;AACF,SAAO;GACL;GACA;GACA,SAAS,EACP,MAAM,KAAK,UAAU,YAAY,CAClC;GACD,UAAU;IACR,WAAW;IACX,SAAS,KAAK;IACd,SAAS;IACT,MAAM;GACP;EACF;CACF;AACF;AAGD,SAAS,aAAa,UAAU,CAAE,GAAE;CAClC,IAAI,IAAI,IAAI;CACZ,MAAM,WAAW,KAAK,qBAAqB,QAAQ,QAAQ,KAAK,OAAO,KAAK;CAC5E,MAAM,iBAAiB,KAAK,QAAQ,kBAAkB,OAAO,KAAK;CAClE,MAAM,gBAAgB,KAAK,QAAQ,SAAS,OAAO,KAAK;CACxD,MAAM,aAAa,OAAO;EACxB,gBAAgB,SAAS,WAAW;GAClC,QAAQ,QAAQ;GAChB,yBAAyB;GACzB,aAAa;EACd,EAAC;EACF,uBAAuB,QAAQ;EAC/B,kBAAkB,QAAQ;EAC1B,GAAG,QAAQ;CACZ;CACD,MAAM,kBAAkB,CAAC,SAAS,WAAW,CAAE,MAAK,IAAI,wBAAwB,SAAS,UAAU;EACjG,aAAa,aAAa;EAC1B,KAAK,CAAC,EAAE,MAAM,QAAQ,UAAU;EAChC,SAAS;EACT;EACA,OAAO,QAAQ;CAChB;CACD,MAAM,wBAAwB,CAAC,SAAS,WAAW,CAAE,MAAK,IAAI,8BAA8B,SAAS,UAAU;EAC7G,aAAa,aAAa;EAC1B,KAAK,CAAC,EAAE,MAAM,QAAQ,UAAU;EAChC,SAAS;EACT;EACA,OAAO,QAAQ;CAChB;CACD,MAAM,uBAAuB,CAAC,SAAS,WAAW,CAAE,MAAK,IAAI,qBAAqB,SAAS,UAAU;EACnG,aAAa,aAAa;EAC1B,KAAK,CAAC,EAAE,MAAM,QAAQ,UAAU;EAChC,SAAS;EACT,OAAO,QAAQ;CAChB;CACD,MAAM,mBAAmB,CAAC,SAAS,WAAW,CAAE,MAAK,IAAI,iBAAiB,SAAS,UAAU;EAC3F,aAAa,aAAa;EAC1B,KAAK,CAAC,EAAE,MAAM,QAAQ,UAAU;EAChC,SAAS;EACT,OAAO,QAAQ;CAChB;CACD,MAAM,2BAA2B,CAAC,YAAY,IAAI,yBAAyB,SAAS;EAClF,aAAa,aAAa;EAC1B,KAAK,CAAC,EAAE,MAAM,QAAQ,UAAU;EAChC,SAAS;EACT,OAAO,QAAQ;CAChB;CACD,MAAM,oBAAoB,CAAC,YAAY,IAAI,kBAAkB,SAAS;EACpE,aAAa,aAAa;EAC1B,KAAK,CAAC,EAAE,MAAM,QAAQ,UAAU;EAChC,SAAS;EACT,OAAO,QAAQ;CAChB;CACD,MAAM,sBAAsB,CAAC,SAAS,aAAa;AACjD,MAAI,IAAI,OACN,OAAM,IAAI,MACR;AAGJ,MAAI,YAAY,yBACd,QAAO,sBACL,SACA,SACD;AAEH,SAAO,gBAAgB,SAAS,SAAS;CAC1C;CACD,MAAM,uBAAuB,CAAC,YAAY;AACxC,SAAO,IAAI,6BAA6B,SAAS;GAC/C,aAAa,aAAa;GAC1B,KAAK,CAAC,EAAE,MAAM,QAAQ,UAAU;GAChC,SAAS;GACT,OAAO,QAAQ;EAChB;CACF;CACD,MAAM,WAAW,SAAS,SAAS,UAAU;AAC3C,SAAO,oBAAoB,SAAS,SAAS;CAC9C;AACD,UAAS,gBAAgB;AACzB,UAAS,OAAO;AAChB,UAAS,aAAa;AACtB,UAAS,YAAY;AACrB,UAAS,YAAY;AACrB,UAAS,gBAAgB;AACzB,UAAS,qBAAqB;AAC9B,UAAS,QAAQ;AACjB,UAAS,aAAa;AACtB,UAAS,gBAAgB;AACzB,UAAS,qBAAqB;AAC9B,UAAS,SAAS;AAClB,UAAS,cAAc;AACvB,UAAS,QAAQ;AACjB,QAAO;AACR;AACD,IAAI,SAAS,aAAa,EACxB,eAAe,SAEhB,EAAC"}