{"hash": "f975c7e1", "configHash": "e59bf3ad", "lockfileHash": "b7f736cb", "browserHash": "9d74ca1d", "optimized": {"@ai-sdk/openai": {"src": "../../@ai-sdk/openai/dist/index.mjs", "file": "@ai-sdk_openai.js", "fileHash": "84f117cf", "needsInterop": false}, "@openrouter/ai-sdk-provider": {"src": "../../@openrouter/ai-sdk-provider/dist/index.mjs", "file": "@openrouter_ai-sdk-provider.js", "fileHash": "62afad14", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "f4c4ddbf", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "cf5ee89a", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "e4c6a987", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "fb29dd65", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "5240336b", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "9fe7730f", "needsInterop": false}, "ai": {"src": "../../ai/dist/index.mjs", "file": "ai.js", "fileHash": "643b7d42", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "fc347210", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "1be58ab8", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "deb76448", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "069beece", "needsInterop": false}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "8bd09f45", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b34a210d", "needsInterop": true}, "react-dropzone": {"src": "../../react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "8e23726b", "needsInterop": false}, "react-router": {"src": "../../react-router/dist/development/index.mjs", "file": "react-router.js", "fileHash": "47f568a7", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "944680cf", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "2c6ad669", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "e75ff9e3", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "12487ea2", "needsInterop": false}}, "chunks": {"chunk-CSDACtG1": {"file": "chunk-CSDACtG1.js", "isDynamicEntry": false}, "clsx-dO0s959M": {"file": "clsx-dO0s959M.js", "isDynamicEntry": false}, "dist-CzHXh4Qo": {"file": "dist-CzHXh4Qo.js", "isDynamicEntry": false}, "dist-t3wzf1ro": {"file": "dist-t3wzf1ro.js", "isDynamicEntry": false}, "jsx-runtime-BaUrS6dR": {"file": "jsx-runtime-BaUrS6dR.js", "isDynamicEntry": false}, "react-dom-yX6PEKSL": {"file": "react-dom-yX6PEKSL.js", "isDynamicEntry": false}, "react-jwkZF-4H": {"file": "react-jwkZF-4H.js", "isDynamicEntry": false}, "tslib.es6-DjTMuTw1": {"file": "tslib.es6-DjTMuTw1.js", "isDynamicEntry": false}, "types-Bq5IzgDU": {"file": "types-Bq5IzgDU.js", "isDynamicEntry": false}}}