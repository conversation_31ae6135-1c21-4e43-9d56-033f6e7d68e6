{"version": 3, "file": "@radix-ui_react-tabs.js", "names": ["setRef", "composeRefs", "useComposedRefs", "createCollectionScope", "useCollection", "useLayoutEffect2", "React", "React", "Node", "useLayoutEffect2", "React", "React", "useLayoutEffect", "React"], "sources": ["../../@radix-ui/react-tabs/node_modules/@radix-ui/primitive/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-context/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-roving-focus/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-roving-focus/node_modules/@radix-ui/react-collection/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-id/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-id/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-primitive/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-roving-focus/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-use-controllable-state/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-direction/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-roving-focus/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-presence/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-presence/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../@radix-ui/react-tabs/node_modules/@radix-ui/react-presence/dist/index.mjs", "../../@radix-ui/react-tabs/dist/index.mjs"], "sourcesContent": ["// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/collection-legacy.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createCollection(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: /* @__PURE__ */ new Map() }\n  );\n  const CollectionProvider = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef(null);\n    const itemMap = React.useRef(/* @__PURE__ */ new Map()).current;\n    return /* @__PURE__ */ jsx(CollectionProviderImpl, { scope, itemMap, collectionRef: ref, children });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...itemData });\n        return () => void context.itemMap.delete(ref);\n      });\n      return /* @__PURE__ */ jsx(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useCollection(scope) {\n    const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope\n  ];\n}\n\n// src/collection.tsx\nimport React2 from \"react\";\nimport { createContextScope as createContextScope2 } from \"@radix-ui/react-context\";\nimport { useComposedRefs as useComposedRefs2 } from \"@radix-ui/react-compose-refs\";\nimport { createSlot as createSlot2 } from \"@radix-ui/react-slot\";\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n  #keys;\n  constructor(entries) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n  set(key, value) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n  insert(index, key, value) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n    if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n    const size = this.size + (has ? 0 : 1);\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n    const keys = [...this.#keys];\n    let nextValue;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i];\n        if (keys[i] === key) {\n          nextKey = keys[i + 1];\n        }\n        if (has) {\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1];\n        const currentValue = nextValue;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n  with(index, key, value) {\n    const copy = new _OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n  before(key) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n  after(key) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n  first() {\n    return this.entryAt(0);\n  }\n  last() {\n    return this.entryAt(-1);\n  }\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n  delete(key) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n  deleteAt(index) {\n    const key = this.keyAt(index);\n    if (key !== void 0) {\n      return this.delete(key);\n    }\n    return false;\n  }\n  at(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return this.get(key);\n    }\n  }\n  entryAt(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return [key, this.get(key)];\n    }\n  }\n  indexOf(key) {\n    return this.#keys.indexOf(key);\n  }\n  keyAt(index) {\n    return at(this.#keys, index);\n  }\n  from(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n  keyFrom(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n  find(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return void 0;\n  }\n  findIndex(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n  filter(predicate, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  map(callbackfn, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  reduce(...args) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0);\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n  reduceRight(...args) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1);\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index);\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n  toSorted(compareFn) {\n    const entries = [...this.entries()].sort(compareFn);\n    return new _OrderedDict(entries);\n  }\n  toReversed() {\n    const reversed = new _OrderedDict();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n  toSpliced(...args) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new _OrderedDict(entries);\n  }\n  slice(start, end) {\n    const result = new _OrderedDict();\n    let stop = this.size - 1;\n    if (start === void 0) {\n      return result;\n    }\n    if (start < 0) {\n      start = start + this.size;\n    }\n    if (end !== void 0 && end > 0) {\n      stop = end - 1;\n    }\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      result.set(key, element);\n    }\n    return result;\n  }\n  every(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n  some(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n};\nfunction at(array, index) {\n  if (\"at\" in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n\n// src/collection.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction createCollection2(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope2(PROVIDER_NAME);\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0\n    }\n  );\n  const CollectionProvider = ({ state, ...props }) => {\n    return state ? /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state }) : /* @__PURE__ */ jsx2(CollectionInit, { ...props });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const CollectionInit = (props) => {\n    const state = useInitCollection();\n    return /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state });\n  };\n  CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n  const CollectionProviderImpl = (props) => {\n    const { scope, children, state } = props;\n    const ref = React2.useRef(null);\n    const [collectionElement, setCollectionElement] = React2.useState(\n      null\n    );\n    const composeRefs = useComposedRefs2(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n    React2.useEffect(() => {\n      if (!collectionElement) return;\n      const observer = getChildListObserver(() => {\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n    return /* @__PURE__ */ jsx2(\n      CollectionContextProvider,\n      {\n        scope,\n        itemMap,\n        setItemMap,\n        collectionRef: composeRefs,\n        collectionRefObject: ref,\n        collectionElement,\n        children\n      }\n    );\n  };\n  CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot2(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs2(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx2(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot2(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React2.useRef(null);\n      const [element, setElement] = React2.useState(null);\n      const composedRefs = useComposedRefs2(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      const { setItemMap } = context;\n      const itemDataRef = React2.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n      React2.useEffect(() => {\n        const itemData2 = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n          if (!map.has(element)) {\n            map.set(element, { ...itemData2, element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n          return map.set(element, { ...itemData2, element }).toSorted(sortByDocumentPosition);\n        });\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n      return /* @__PURE__ */ jsx2(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useInitCollection() {\n    return React2.useState(new OrderedDict());\n  }\n  function useCollection(scope) {\n    const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n    return itemMap;\n  }\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection\n  };\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions\n  ];\n}\nfunction shallowEqual(a, b) {\n  if (a === b) return true;\n  if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\nfunction isElementPreceding(a, b) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n  return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === \"childList\") {\n        callback();\n        return;\n      }\n    }\n  });\n  return observer;\n}\nexport {\n  createCollection,\n  createCollection2 as unstable_createCollection\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/roving-focus-group.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = { bubbles: false, cancelable: true };\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(Collection.Provider, { scope: props.__scopeRovingFocusGroup, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeRovingFocusGroup, children: /* @__PURE__ */ jsx(RovingFocusGroupImpl, { ...props, ref: forwardedRef }) }) });\n  }\n);\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n  return /* @__PURE__ */ jsx(\n    RovingFocusProvider,\n    {\n      scope: __scopeRovingFocusGroup,\n      orientation,\n      dir: direction,\n      loop,\n      currentTabStopId,\n      onItemFocus: React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      ),\n      onItemShiftTab: React.useCallback(() => setIsTabbingBackOut(true), []),\n      onFocusableItemAdd: React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      ),\n      onFocusableItemRemove: React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      ),\n      children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n          \"data-orientation\": orientation,\n          ...groupProps,\n          ref: composedRefs,\n          style: { outline: \"none\", ...props.style },\n          onMouseDown: composeEventHandlers(props.onMouseDown, () => {\n            isClickFocusRef.current = true;\n          }),\n          onFocus: composeEventHandlers(props.onFocus, (event) => {\n            const isKeyboardFocus = !isClickFocusRef.current;\n            if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n              const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n              event.currentTarget.dispatchEvent(entryFocusEvent);\n              if (!entryFocusEvent.defaultPrevented) {\n                const items = getItems().filter((item) => item.focusable);\n                const activeItem = items.find((item) => item.active);\n                const currentItem = items.find((item) => item.id === currentTabStopId);\n                const candidateItems = [activeItem, currentItem, ...items].filter(\n                  Boolean\n                );\n                const candidateNodes = candidateItems.map((item) => item.ref.current);\n                focusFirst(candidateNodes, preventScrollOnEntryFocus);\n              }\n            }\n            isClickFocusRef.current = false;\n          }),\n          onBlur: composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))\n        }\n      )\n    }\n  );\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n    return /* @__PURE__ */ jsx(\n      Collection.ItemSlot,\n      {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ jsx(\n          Primitive.span,\n          {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: composeEventHandlers(props.onMouseDown, (event) => {\n              if (!focusable) event.preventDefault();\n              else context.onItemFocus(id);\n            }),\n            onFocus: composeEventHandlers(props.onFocus, () => context.onItemFocus(id)),\n            onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n              if (event.key === \"Tab\" && event.shiftKey) {\n                context.onItemShiftTab();\n                return;\n              }\n              if (event.target !== event.currentTarget) return;\n              const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n              if (focusIntent !== void 0) {\n                if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                event.preventDefault();\n                const items = getItems().filter((item) => item.focusable);\n                let candidateNodes = items.map((item) => item.ref.current);\n                if (focusIntent === \"last\") candidateNodes.reverse();\n                else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                  if (focusIntent === \"prev\") candidateNodes.reverse();\n                  const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                  candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                }\n                setTimeout(() => focusFirst(candidateNodes));\n              }\n            }),\n            children: typeof children === \"function\" ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null }) : children\n          }\n        )\n      }\n    );\n  }\n);\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n  ArrowLeft: \"prev\",\n  ArrowUp: \"prev\",\n  ArrowRight: \"next\",\n  ArrowDown: \"next\",\n  PageUp: \"first\",\n  Home: \"first\",\n  PageDown: \"last\",\n  End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n  if (dir !== \"rtl\") return key;\n  return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === \"vertical\" && [\"ArrowLeft\", \"ArrowRight\"].includes(key)) return void 0;\n  if (orientation === \"horizontal\" && [\"ArrowUp\", \"ArrowDown\"].includes(key)) return void 0;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\nexport {\n  Item,\n  Root,\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  createRovingFocusGroupScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/presence.tsx\nimport * as React2 from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\n\n// src/use-state-machine.tsx\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : React2.Children.only(children);\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? React2.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = React2.useState();\n  const stylesRef = React2.useRef(null);\n  const prevPresentRef = React2.useRef(present);\n  const prevAnimationNameRef = React2.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  React2.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: React2.useCallback((node2) => {\n      stylesRef.current = node2 ? getComputedStyle(node2) : null;\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Presence;\nexport {\n  Presence,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/tabs.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { createRovingFocusGroupScope } from \"@radix-ui/react-roving-focus\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport * as RovingFocusGroup from \"@radix-ui/react-roving-focus\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { jsx } from \"react/jsx-runtime\";\nvar TABS_NAME = \"Tabs\";\nvar [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope\n]);\nvar useRovingFocusGroupScope = createRovingFocusGroupScope();\nvar [TabsProvider, useTabsContext] = createTabsContext(TABS_NAME);\nvar Tabs = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = \"horizontal\",\n      dir,\n      activationMode = \"automatic\",\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? \"\",\n      caller: TABS_NAME\n    });\n    return /* @__PURE__ */ jsx(\n      TabsProvider,\n      {\n        scope: __scopeTabs,\n        baseId: useId(),\n        value,\n        onValueChange: setValue,\n        orientation,\n        dir: direction,\n        activationMode,\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            dir: direction,\n            \"data-orientation\": orientation,\n            ...tabsProps,\n            ref: forwardedRef\n          }\n        )\n      }\n    );\n  }\n);\nTabs.displayName = TABS_NAME;\nvar TAB_LIST_NAME = \"TabsList\";\nvar TabsList = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return /* @__PURE__ */ jsx(\n      RovingFocusGroup.Root,\n      {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        orientation: context.orientation,\n        dir: context.dir,\n        loop,\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            role: \"tablist\",\n            \"aria-orientation\": context.orientation,\n            ...listProps,\n            ref: forwardedRef\n          }\n        )\n      }\n    );\n  }\n);\nTabsList.displayName = TAB_LIST_NAME;\nvar TRIGGER_NAME = \"TabsTrigger\";\nvar TabsTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return /* @__PURE__ */ jsx(\n      RovingFocusGroup.Item,\n      {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !disabled,\n        active: isSelected,\n        children: /* @__PURE__ */ jsx(\n          Primitive.button,\n          {\n            type: \"button\",\n            role: \"tab\",\n            \"aria-selected\": isSelected,\n            \"aria-controls\": contentId,\n            \"data-state\": isSelected ? \"active\" : \"inactive\",\n            \"data-disabled\": disabled ? \"\" : void 0,\n            disabled,\n            id: triggerId,\n            ...triggerProps,\n            ref: forwardedRef,\n            onMouseDown: composeEventHandlers(props.onMouseDown, (event) => {\n              if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                context.onValueChange(value);\n              } else {\n                event.preventDefault();\n              }\n            }),\n            onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n              if ([\" \", \"Enter\"].includes(event.key)) context.onValueChange(value);\n            }),\n            onFocus: composeEventHandlers(props.onFocus, () => {\n              const isAutomaticActivation = context.activationMode !== \"manual\";\n              if (!isSelected && !disabled && isAutomaticActivation) {\n                context.onValueChange(value);\n              }\n            })\n          }\n        )\n      }\n    );\n  }\n);\nTabsTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"TabsContent\";\nvar TabsContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => isMountAnimationPreventedRef.current = false);\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || isSelected, children: ({ present }) => /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"data-state\": isSelected ? \"active\" : \"inactive\",\n        \"data-orientation\": context.orientation,\n        role: \"tabpanel\",\n        \"aria-labelledby\": triggerId,\n        hidden: !present,\n        id: contentId,\n        tabIndex: 0,\n        ...contentProps,\n        ref: forwardedRef,\n        style: {\n          ...props.style,\n          animationDuration: isMountAnimationPreventedRef.current ? \"0s\" : void 0\n        },\n        children: present && children\n      }\n    ) });\n  }\n);\nTabsContent.displayName = CONTENT_NAME;\nfunction makeTriggerId(baseId, value) {\n  return `${baseId}-trigger-${value}`;\n}\nfunction makeContentId(baseId, value) {\n  return `${baseId}-content-${value}`;\n}\nvar Root2 = Tabs;\nvar List = TabsList;\nvar Trigger = TabsTrigger;\nvar Content = TabsContent;\nexport {\n  Content,\n  List,\n  Root2 as Root,\n  Tabs,\n  TabsContent,\n  TabsList,\n  TabsTrigger,\n  Trigger,\n  createTabsScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "mappings": ";;;;;;;;;;;AACA,SAAS,qBAAqB,sBAAsB,iBAAiB,EAAE,2BAA2B,MAAM,GAAG,CAAE,GAAE;AAC7G,QAAO,SAAS,YAAY,OAAO;AACjC,yBAAuB,MAAM;AAC7B,MAAI,6BAA6B,UAAU,MAAM,iBAC/C,QAAO,kBAAkB,MAAM;CAElC;AACF;;;;;;ACWD,SAAS,mBAAmB,WAAW,yBAAyB,CAAE,GAAE;CAClE,IAAI,kBAAkB,CAAE;CACxB,SAAS,eAAe,mBAAmB,gBAAgB;EACzD,MAAM,cAAc,aAAM,cAAc,eAAe;EACvD,MAAM,QAAQ,gBAAgB;AAC9B,oBAAkB,CAAC,GAAG,iBAAiB,cAAe;EACtD,MAAM,WAAW,CAAC,UAAU;GAC1B,MAAM,EAAE,OAAO,SAAU,GAAG,SAAS,GAAG;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,QAAQ,aAAM,QAAQ,MAAM,SAAS,OAAO,OAAO,QAAQ,CAAC;AAClE,0BAAuB,4BAAI,QAAQ,UAAU;IAAE;IAAO;GAAU,EAAC;EAClE;AACD,WAAS,cAAc,oBAAoB;EAC3C,SAAS,YAAY,cAAc,OAAO;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,UAAU,aAAM,WAAW,QAAQ;AACzC,OAAI,QAAS,QAAO;AACpB,OAAI,wBAAwB,EAAG,QAAO;AACtC,SAAM,IAAI,OAAO,IAAI,aAAa,2BAA2B,kBAAkB;EAChF;AACD,SAAO,CAAC,UAAU,WAAY;CAC/B;CACD,MAAM,cAAc,MAAM;EACxB,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,UAAO,aAAM,cAAc,eAAe;EAC3C,EAAC;AACF,SAAO,SAAS,SAAS,OAAO;GAC9B,MAAM,WAAW,QAAQ,cAAc;AACvC,UAAO,aAAM,QACX,OAAO,IAAI,SAAS,cAAc;IAAE,GAAG;KAAQ,YAAY;GAAU,EAAE,IACvE,CAAC,OAAO,QAAS,EAClB;EACF;CACF;AACD,aAAY,YAAY;AACxB,QAAO,CAAC,gBAAgB,qBAAqB,aAAa,GAAG,uBAAuB,AAAC;AACtF;AACD,SAAS,qBAAqB,GAAG,QAAQ;CACvC,MAAM,YAAY,OAAO;AACzB,KAAI,OAAO,WAAW,EAAG,QAAO;CAChC,MAAM,cAAc,MAAM;EACxB,MAAM,aAAa,OAAO,IAAI,CAAC,kBAAkB;GAC/C,UAAU,cAAc;GACxB,WAAW,aAAa;EACzB,GAAE;AACH,SAAO,SAAS,kBAAkB,gBAAgB;GAChD,MAAM,aAAa,WAAW,OAAO,CAAC,aAAa,EAAE,UAAU,WAAW,KAAK;IAC7E,MAAM,aAAa,SAAS,eAAe;IAC3C,MAAM,eAAe,YAAY,SAAS;AAC1C,WAAO;KAAE,GAAG;KAAa,GAAG;IAAc;GAC3C,GAAE,CAAE,EAAC;AACN,UAAO,aAAM,QAAQ,OAAO,IAAI,SAAS,UAAU,cAAc,WAAY,IAAG,CAAC,UAAW,EAAC;EAC9F;CACF;AACD,aAAY,YAAY,UAAU;AAClC,QAAO;AACR;;;;ACzED,SAASA,SAAO,KAAK,OAAO;AAC1B,YAAW,QAAQ,WACjB,QAAO,IAAI,MAAM;UACR,QAAQ,QAAQ,aAAa,EACtC,KAAI,UAAU;AAEjB;AACD,SAASC,cAAY,GAAG,MAAM;AAC5B,QAAO,CAAC,SAAS;EACf,IAAI,aAAa;EACjB,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;GACjC,MAAM,UAAU,SAAO,KAAK,KAAK;AACjC,QAAK,qBAAqB,WAAW,WACnC,cAAa;AAEf,UAAO;EACR,EAAC;AACF,MAAI,WACF,QAAO,MAAM;AACX,QAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;IACxC,MAAM,UAAU,SAAS;AACzB,eAAW,WAAW,WACpB,UAAS;QAET,UAAO,KAAK,IAAI,KAAK;GAExB;EACF;CAEJ;AACF;AACD,SAASC,kBAAgB,GAAG,MAAM;AAChC,QAAO,aAAM,YAAY,cAAY,GAAG,KAAK,EAAE,KAAK;AACrD;;;;AC3BD,SAAS,iBAAiB,MAAM;CAC9B,MAAM,gBAAgB,OAAO;CAC7B,MAAM,CAAC,yBAAyBC,wBAAsB,GAAG,mBAAmB,cAAc;CAC1F,MAAM,CAAC,wBAAwB,qBAAqB,GAAG,wBACrD,eACA;EAAE,eAAe,EAAE,SAAS,KAAM;EAAE,yBAAyB,IAAI;CAAO,EACzE;CACD,MAAM,qBAAqB,CAAC,UAAU;EACpC,MAAM,EAAE,OAAO,UAAU,GAAG;EAC5B,MAAM,MAAM,qBAAM,OAAO,KAAK;EAC9B,MAAM,UAAU,qBAAM,uBAAuB,IAAI,MAAM,CAAC;AACxD,yBAAuB,4BAAI,wBAAwB;GAAE;GAAO;GAAS,eAAe;GAAK;EAAU,EAAC;CACrG;AACD,oBAAmB,cAAc;CACjC,MAAM,uBAAuB,OAAO;CACpC,MAAM,qBAAqB,WAAW,qBAAqB;CAC3D,MAAM,iBAAiB,qBAAM,WAC3B,CAAC,OAAO,iBAAiB;EACvB,MAAM,EAAE,OAAO,UAAU,GAAG;EAC5B,MAAM,UAAU,qBAAqB,sBAAsB,MAAM;EACjE,MAAM,eAAe,kBAAgB,cAAc,QAAQ,cAAc;AACzE,yBAAuB,4BAAI,oBAAoB;GAAE,KAAK;GAAc;EAAU,EAAC;CAChF,EACF;AACD,gBAAe,cAAc;CAC7B,MAAM,iBAAiB,OAAO;CAC9B,MAAM,iBAAiB;CACvB,MAAM,yBAAyB,WAAW,eAAe;CACzD,MAAM,qBAAqB,qBAAM,WAC/B,CAAC,OAAO,iBAAiB;EACvB,MAAM,EAAE,OAAO,SAAU,GAAG,UAAU,GAAG;EACzC,MAAM,MAAM,qBAAM,OAAO,KAAK;EAC9B,MAAM,eAAe,kBAAgB,cAAc,IAAI;EACvD,MAAM,UAAU,qBAAqB,gBAAgB,MAAM;AAC3D,uBAAM,UAAU,MAAM;AACpB,WAAQ,QAAQ,IAAI,KAAK;IAAE;IAAK,GAAG;GAAU,EAAC;AAC9C,UAAO,WAAW,QAAQ,QAAQ,OAAO,IAAI;EAC9C,EAAC;AACF,yBAAuB,4BAAI,wBAAwB;IAAQ,iBAAiB;GAAM,KAAK;GAAc;EAAU,EAAC;CACjH,EACF;AACD,oBAAmB,cAAc;CACjC,SAASC,gBAAc,OAAO;EAC5B,MAAM,UAAU,qBAAqB,OAAO,sBAAsB,MAAM;EACxE,MAAM,WAAW,qBAAM,YAAY,MAAM;GACvC,MAAM,iBAAiB,QAAQ,cAAc;AAC7C,QAAK,eAAgB,QAAO,CAAE;GAC9B,MAAM,eAAe,MAAM,KAAK,eAAe,kBAAkB,GAAG,eAAe,GAAG,CAAC;GACvF,MAAM,QAAQ,MAAM,KAAK,QAAQ,QAAQ,QAAQ,CAAC;GAClD,MAAM,eAAe,MAAM,KACzB,CAAC,GAAG,MAAM,aAAa,QAAQ,EAAE,IAAI,QAAQ,GAAG,aAAa,QAAQ,EAAE,IAAI,QAAQ,CACpF;AACD,UAAO;EACR,GAAE,CAAC,QAAQ,eAAe,QAAQ,OAAQ,EAAC;AAC5C,SAAO;CACR;AACD,QAAO;EACL;GAAE,UAAU;GAAoB,MAAM;GAAgB,UAAU;EAAoB;EACpFA;EACAD;CACD;AACF;;;;ACnED,IAAIE,qBAAmB,YAAY,WAAWC,aAAM,kBAAkB,MAAM,CAC3E;;;;ACAD,IAAI,aAAaC,aAAM,UAAU,MAAM,CAAC,UAAU,MAAM,WAAW;AACnE,IAAI,QAAQ;AACZ,SAAS,MAAM,iBAAiB;CAC9B,MAAM,CAAC,IAAI,MAAM,GAAG,aAAM,SAAS,YAAY,CAAC;AAChD,oBAAgB,MAAM;AACpB,OAAK,gBAAiB,OAAM,CAAC,YAAY,WAAW,OAAO,QAAQ,CAAC;CACrE,GAAE,CAAC,eAAgB,EAAC;AACrB,QAAO,oBAAoB,MAAM,QAAQ,OAAO;AACjD;;;;;ACND,IAAI,QAAQ;CACV;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACD;AACD,IAAI,YAAY,MAAM,OAAO,CAAC,WAAW,SAAS;CAChD,MAAM,OAAO,YAAY,YAAY,OAAO;CAC5C,MAAMC,SAAO,aAAM,WAAW,CAAC,OAAO,iBAAiB;EACrD,MAAM,EAAE,QAAS,GAAG,gBAAgB,GAAG;EACvC,MAAM,OAAO,UAAU,OAAO;AAC9B,aAAW,WAAW,YACpB,QAAO,OAAO,IAAI,WAAW,IAAI;AAEnC,yBAAuB,4BAAI,MAAM;GAAE,GAAG;GAAgB,KAAK;EAAc,EAAC;CAC3E,EAAC;AACF,QAAK,eAAe,YAAY;AAChC,QAAO;EAAE,GAAG;GAAY,OAAOA;CAAM;AACtC,GAAE,CAAE,EAAC;;;;AClCN,SAAS,eAAe,UAAU;CAChC,MAAM,cAAc,aAAM,OAAO,SAAS;AAC1C,cAAM,UAAU,MAAM;AACpB,cAAY,UAAU;CACvB,EAAC;AACF,QAAO,aAAM,QAAQ,MAAM,CAAC,GAAG,SAAS,YAAY,UAAU,GAAG,KAAK,EAAE,CAAE,EAAC;AAC5E;;;;ACND,IAAIC,qBAAmB,YAAY,WAAWC,aAAM,kBAAkB,MAAM,CAC3E;;;;ACAD,IAAI,qBAAqBC,aAAM,uBAAuB,MAAM,CAAC,UAAU,KAAKC;AAC5E,SAAS,qBAAqB,EAC5B,MACA,aACA,WAAW,MAAM,CAChB,GACD,QACD,EAAE;CACD,MAAM,CAAC,kBAAkB,qBAAqB,YAAY,GAAG,qBAAqB;EAChF;EACA;CACD,EAAC;CACF,MAAM,eAAe,cAAc;CACnC,MAAM,QAAQ,eAAe,OAAO;CAC1B;EACR,MAAM,kBAAkB,aAAM,OAAO,cAAc,EAAE;AACrD,eAAM,UAAU,MAAM;GACpB,MAAM,gBAAgB,gBAAgB;AACtC,OAAI,kBAAkB,cAAc;IAClC,MAAM,OAAO,gBAAgB,eAAe;IAC5C,MAAM,KAAK,eAAe,eAAe;AACzC,YAAQ,QACH,OAAO,oBAAoB,KAAK,MAAM,GAAG,4KAC7C;GACF;AACD,mBAAgB,UAAU;EAC3B,GAAE,CAAC,cAAc,MAAO,EAAC;CAC3B;CACD,MAAM,WAAW,aAAM,YACrB,CAAC,cAAc;AACb,MAAI,cAAc;GAChB,MAAM,SAAS,WAAW,UAAU,GAAG,UAAU,KAAK,GAAG;AACzD,OAAI,WAAW,KACb,aAAY,UAAU,OAAO;EAEhC,MACC,qBAAoB,UAAU;CAEjC,GACD;EAAC;EAAc;EAAM;EAAqB;CAAY,EACvD;AACD,QAAO,CAAC,OAAO,QAAS;AACzB;AACD,SAAS,qBAAqB,EAC5B,aACA,UACD,EAAE;CACD,MAAM,CAAC,OAAO,SAAS,GAAG,aAAM,SAAS,YAAY;CACrD,MAAM,eAAe,aAAM,OAAO,MAAM;CACxC,MAAM,cAAc,aAAM,OAAO,SAAS;AAC1C,oBAAmB,MAAM;AACvB,cAAY,UAAU;CACvB,GAAE,CAAC,QAAS,EAAC;AACd,cAAM,UAAU,MAAM;AACpB,MAAI,aAAa,YAAY,OAAO;AAClC,eAAY,UAAU,MAAM;AAC5B,gBAAa,UAAU;EACxB;CACF,GAAE,CAAC,OAAO,YAAa,EAAC;AACzB,QAAO;EAAC;EAAO;EAAU;CAAY;AACtC;AACD,SAAS,WAAW,OAAO;AACzB,eAAc,UAAU;AACzB;AAKD,IAAI,aAAa,OAAO,mBAAmB;;;;ACpE3C,IAAI,mBAAmB,aAAM,mBAAmB,EAAE;AAKlD,SAAS,aAAa,UAAU;CAC9B,MAAM,YAAY,aAAM,WAAW,iBAAiB;AACpD,QAAO,YAAY,aAAa;AACjC;;;;ACGD,IAAI,cAAc;AAClB,IAAI,gBAAgB;CAAE,SAAS;CAAO,YAAY;AAAM;AACxD,IAAI,aAAa;AACjB,IAAI,CAAC,YAAY,eAAe,sBAAsB,GAAG,iBAAiB,WAAW;AACrF,IAAI,CAAC,+BAA+B,4BAA4B,GAAG,mBACjE,YACA,CAAC,qBAAsB,EACxB;AACD,IAAI,CAAC,qBAAqB,sBAAsB,GAAG,8BAA8B,WAAW;AAC5F,IAAI,mBAAmB,aAAM,WAC3B,CAAC,OAAO,iBAAiB;AACvB,wBAAuB,4BAAI,WAAW,UAAU;EAAE,OAAO,MAAM;EAAyB,0BAA0B,4BAAI,WAAW,MAAM;GAAE,OAAO,MAAM;GAAyB,0BAA0B,4BAAI,sBAAsB;IAAE,GAAG;IAAO,KAAK;GAAc,EAAC;EAAE,EAAC;CAAE,EAAC;AAC1Q,EACF;AACD,iBAAiB,cAAc;AAC/B,IAAI,uBAAuB,aAAM,WAAW,CAAC,OAAO,iBAAiB;CACnE,MAAM,EACJ,yBACA,aACA,OAAO,OACP,KACA,kBAAkB,sBAClB,yBACA,0BACA,cACA,4BAA4B,MAC5B,GAAG,YACJ,GAAG;CACJ,MAAM,MAAM,aAAM,OAAO,KAAK;CAC9B,MAAM,eAAe,kBAAgB,cAAc,IAAI;CACvD,MAAM,YAAY,aAAa,IAAI;CACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,qBAAqB;EACnE,MAAM;EACN,aAAa,2BAA2B;EACxC,UAAU;EACV,QAAQ;CACT,EAAC;CACF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,aAAM,SAAS,MAAM;CACrE,MAAM,mBAAmB,eAAe,aAAa;CACrD,MAAM,WAAW,cAAc,wBAAwB;CACvD,MAAM,kBAAkB,aAAM,OAAO,MAAM;CAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,aAAM,SAAS,EAAE;AACvE,cAAM,UAAU,MAAM;EACpB,MAAM,OAAO,IAAI;AACjB,MAAI,MAAM;AACR,QAAK,iBAAiB,aAAa,iBAAiB;AACpD,UAAO,MAAM,KAAK,oBAAoB,aAAa,iBAAiB;EACrE;CACF,GAAE,CAAC,gBAAiB,EAAC;AACtB,wBAAuB,4BACrB,qBACA;EACE,OAAO;EACP;EACA,KAAK;EACL;EACA;EACA,aAAa,aAAM,YACjB,CAAC,cAAc,oBAAoB,UAAU,EAC7C,CAAC,mBAAoB,EACtB;EACD,gBAAgB,aAAM,YAAY,MAAM,oBAAoB,KAAK,EAAE,CAAE,EAAC;EACtE,oBAAoB,aAAM,YACxB,MAAM,uBAAuB,CAAC,cAAc,YAAY,EAAE,EAC1D,CAAE,EACH;EACD,uBAAuB,aAAM,YAC3B,MAAM,uBAAuB,CAAC,cAAc,YAAY,EAAE,EAC1D,CAAE,EACH;EACD,0BAA0B,4BACxB,UAAU,KACV;GACE,UAAU,oBAAoB,wBAAwB,IAAI,KAAK;GAC/D,oBAAoB;GACpB,GAAG;GACH,KAAK;GACL,OAAO;IAAE,SAAS;IAAQ,GAAG,MAAM;GAAO;GAC1C,aAAa,qBAAqB,MAAM,aAAa,MAAM;AACzD,oBAAgB,UAAU;GAC3B,EAAC;GACF,SAAS,qBAAqB,MAAM,SAAS,CAAC,UAAU;IACtD,MAAM,mBAAmB,gBAAgB;AACzC,QAAI,MAAM,WAAW,MAAM,iBAAiB,oBAAoB,kBAAkB;KAChF,MAAM,kBAAkB,IAAI,YAAY,aAAa;AACrD,WAAM,cAAc,cAAc,gBAAgB;AAClD,UAAK,gBAAgB,kBAAkB;MACrC,MAAM,QAAQ,UAAU,CAAC,OAAO,CAAC,SAAS,KAAK,UAAU;MACzD,MAAM,aAAa,MAAM,KAAK,CAAC,SAAS,KAAK,OAAO;MACpD,MAAM,cAAc,MAAM,KAAK,CAAC,SAAS,KAAK,OAAO,iBAAiB;MACtE,MAAM,iBAAiB;OAAC;OAAY;OAAa,GAAG;MAAM,EAAC,OACzD,QACD;MACD,MAAM,iBAAiB,eAAe,IAAI,CAAC,SAAS,KAAK,IAAI,QAAQ;AACrE,iBAAW,gBAAgB,0BAA0B;KACtD;IACF;AACD,oBAAgB,UAAU;GAC3B,EAAC;GACF,QAAQ,qBAAqB,MAAM,QAAQ,MAAM,oBAAoB,MAAM,CAAC;EAC7E,EACF;CACF,EACF;AACF,EAAC;AACF,IAAI,YAAY;AAChB,IAAI,uBAAuB,aAAM,WAC/B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,yBACA,YAAY,MACZ,SAAS,OACT,WACA,SACA,GAAG,WACJ,GAAG;CACJ,MAAM,SAAS,OAAO;CACtB,MAAM,KAAK,aAAa;CACxB,MAAM,UAAU,sBAAsB,WAAW,wBAAwB;CACzE,MAAM,mBAAmB,QAAQ,qBAAqB;CACtD,MAAM,WAAW,cAAc,wBAAwB;CACvD,MAAM,EAAE,oBAAoB,uBAAuB,kBAAkB,GAAG;AACxE,cAAM,UAAU,MAAM;AACpB,MAAI,WAAW;AACb,uBAAoB;AACpB,UAAO,MAAM,uBAAuB;EACrC;CACF,GAAE;EAAC;EAAW;EAAoB;CAAsB,EAAC;AAC1D,wBAAuB,4BACrB,WAAW,UACX;EACE,OAAO;EACP;EACA;EACA;EACA,0BAA0B,4BACxB,UAAU,MACV;GACE,UAAU,mBAAmB,IAAI;GACjC,oBAAoB,QAAQ;GAC5B,GAAG;GACH,KAAK;GACL,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,SAAK,UAAW,OAAM,gBAAgB;QACjC,SAAQ,YAAY,GAAG;GAC7B,EAAC;GACF,SAAS,qBAAqB,MAAM,SAAS,MAAM,QAAQ,YAAY,GAAG,CAAC;GAC3E,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,QAAI,MAAM,QAAQ,SAAS,MAAM,UAAU;AACzC,aAAQ,gBAAgB;AACxB;IACD;AACD,QAAI,MAAM,WAAW,MAAM,cAAe;IAC1C,MAAM,cAAc,eAAe,OAAO,QAAQ,aAAa,QAAQ,IAAI;AAC3E,QAAI,qBAAqB,GAAG;AAC1B,SAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU,MAAM,SAAU;AACtE,WAAM,gBAAgB;KACtB,MAAM,QAAQ,UAAU,CAAC,OAAO,CAAC,SAAS,KAAK,UAAU;KACzD,IAAI,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,QAAQ;AAC1D,SAAI,gBAAgB,OAAQ,gBAAe,SAAS;cAC3C,gBAAgB,UAAU,gBAAgB,QAAQ;AACzD,UAAI,gBAAgB,OAAQ,gBAAe,SAAS;MACpD,MAAM,eAAe,eAAe,QAAQ,MAAM,cAAc;AAChE,uBAAiB,QAAQ,OAAO,UAAU,gBAAgB,eAAe,EAAE,GAAG,eAAe,MAAM,eAAe,EAAE;KACrH;AACD,gBAAW,MAAM,WAAW,eAAe,CAAC;IAC7C;GACF,EAAC;GACF,iBAAiB,aAAa,aAAa,SAAS;IAAE;IAAkB,YAAY,oBAAoB;GAAM,EAAC,GAAG;EACnH,EACF;CACF,EACF;AACF,EACF;AACD,qBAAqB,cAAc;AACnC,IAAI,0BAA0B;CAC5B,WAAW;CACX,SAAS;CACT,YAAY;CACZ,WAAW;CACX,QAAQ;CACR,MAAM;CACN,UAAU;CACV,KAAK;AACN;AACD,SAAS,qBAAqB,KAAK,KAAK;AACtC,KAAI,QAAQ,MAAO,QAAO;AAC1B,QAAO,QAAQ,cAAc,eAAe,QAAQ,eAAe,cAAc;AAClF;AACD,SAAS,eAAe,OAAO,aAAa,KAAK;CAC/C,MAAM,MAAM,qBAAqB,MAAM,KAAK,IAAI;AAChD,KAAI,gBAAgB,cAAc,CAAC,aAAa,YAAa,EAAC,SAAS,IAAI,CAAE,aAAY;AACzF,KAAI,gBAAgB,gBAAgB,CAAC,WAAW,WAAY,EAAC,SAAS,IAAI,CAAE,aAAY;AACxF,QAAO,wBAAwB;AAChC;AACD,SAAS,WAAW,YAAY,gBAAgB,OAAO;CACrD,MAAM,6BAA6B,SAAS;AAC5C,MAAK,MAAM,aAAa,YAAY;AAClC,MAAI,cAAc,2BAA4B;AAC9C,YAAU,MAAM,EAAE,cAAe,EAAC;AAClC,MAAI,SAAS,kBAAkB,2BAA4B;CAC5D;AACF;AACD,SAAS,UAAU,OAAO,YAAY;AACpC,QAAO,MAAM,IAAI,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,QAAQ;AAC3E;AACD,IAAI,OAAO;AACX,IAAI,OAAO;;;;AC5NX,SAAS,OAAO,KAAK,OAAO;AAC1B,YAAW,QAAQ,WACjB,QAAO,IAAI,MAAM;UACR,QAAQ,QAAQ,aAAa,EACtC,KAAI,UAAU;AAEjB;AACD,SAAS,YAAY,GAAG,MAAM;AAC5B,QAAO,CAAC,SAAS;EACf,IAAI,aAAa;EACjB,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;GACjC,MAAM,UAAU,OAAO,KAAK,KAAK;AACjC,QAAK,qBAAqB,WAAW,WACnC,cAAa;AAEf,UAAO;EACR,EAAC;AACF,MAAI,WACF,QAAO,MAAM;AACX,QAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;IACxC,MAAM,UAAU,SAAS;AACzB,eAAW,WAAW,WACpB,UAAS;QAET,QAAO,KAAK,IAAI,KAAK;GAExB;EACF;CAEJ;AACF;AACD,SAAS,gBAAgB,GAAG,MAAM;AAChC,QAAO,aAAM,YAAY,YAAY,GAAG,KAAK,EAAE,KAAK;AACrD;;;;ACjCD,IAAI,mBAAmB,YAAY,WAAWC,aAAM,kBAAkB,MAAM,CAC3E;;;;ACMD,SAAS,gBAAgB,cAAc,SAAS;AAC9C,QAAO,aAAM,WAAW,CAAC,OAAO,UAAU;EACxC,MAAM,YAAY,QAAQ,OAAO;AACjC,SAAO,aAAa;CACrB,GAAE,aAAa;AACjB;AAGD,IAAI,WAAW,CAAC,UAAU;CACxB,MAAM,EAAE,SAAS,UAAU,GAAG;CAC9B,MAAM,WAAW,YAAY,QAAQ;CACrC,MAAM,eAAe,aAAa,aAAa,SAAS,EAAE,SAAS,SAAS,UAAW,EAAC,GAAG,aAAO,SAAS,KAAK,SAAS;CACzH,MAAM,MAAM,gBAAgB,SAAS,KAAK,cAAc,MAAM,CAAC;CAC/D,MAAM,oBAAoB,aAAa;AACvC,QAAO,cAAc,SAAS,YAAY,aAAO,aAAa,OAAO,EAAE,IAAK,EAAC,GAAG;AACjF;AACD,SAAS,cAAc;AACvB,SAAS,YAAY,SAAS;CAC5B,MAAM,CAAC,MAAM,QAAQ,GAAG,aAAO,UAAU;CACzC,MAAM,YAAY,aAAO,OAAO,KAAK;CACrC,MAAM,iBAAiB,aAAO,OAAO,QAAQ;CAC7C,MAAM,uBAAuB,aAAO,OAAO,OAAO;CAClD,MAAM,eAAe,UAAU,YAAY;CAC3C,MAAM,CAAC,OAAO,KAAK,GAAG,gBAAgB,cAAc;EAClD,SAAS;GACP,SAAS;GACT,eAAe;EAChB;EACD,kBAAkB;GAChB,OAAO;GACP,eAAe;EAChB;EACD,WAAW,EACT,OAAO,UACR;CACF,EAAC;AACF,cAAO,UAAU,MAAM;EACrB,MAAM,uBAAuB,iBAAiB,UAAU,QAAQ;AAChE,uBAAqB,UAAU,UAAU,YAAY,uBAAuB;CAC7E,GAAE,CAAC,KAAM,EAAC;AACX,kBAAgB,MAAM;EACpB,MAAM,SAAS,UAAU;EACzB,MAAM,aAAa,eAAe;EAClC,MAAM,oBAAoB,eAAe;AACzC,MAAI,mBAAmB;GACrB,MAAM,oBAAoB,qBAAqB;GAC/C,MAAM,uBAAuB,iBAAiB,OAAO;AACrD,OAAI,QACF,MAAK,QAAQ;YACJ,yBAAyB,UAAU,QAAQ,YAAY,OAChE,MAAK,UAAU;QACV;IACL,MAAM,cAAc,sBAAsB;AAC1C,QAAI,cAAc,YAChB,MAAK,gBAAgB;QAErB,MAAK,UAAU;GAElB;AACD,kBAAe,UAAU;EAC1B;CACF,GAAE,CAAC,SAAS,IAAK,EAAC;AACnB,kBAAgB,MAAM;AACpB,MAAI,MAAM;GACR,IAAI;GACJ,MAAM,cAAc,KAAK,cAAc,eAAe;GACtD,MAAM,qBAAqB,CAAC,UAAU;IACpC,MAAM,uBAAuB,iBAAiB,UAAU,QAAQ;IAChE,MAAM,qBAAqB,qBAAqB,SAAS,MAAM,cAAc;AAC7E,QAAI,MAAM,WAAW,QAAQ,oBAAoB;AAC/C,UAAK,gBAAgB;AACrB,UAAK,eAAe,SAAS;MAC3B,MAAM,kBAAkB,KAAK,MAAM;AACnC,WAAK,MAAM,oBAAoB;AAC/B,kBAAY,YAAY,WAAW,MAAM;AACvC,WAAI,KAAK,MAAM,sBAAsB,WACnC,MAAK,MAAM,oBAAoB;MAElC,EAAC;KACH;IACF;GACF;GACD,MAAM,uBAAuB,CAAC,UAAU;AACtC,QAAI,MAAM,WAAW,KACnB,sBAAqB,UAAU,iBAAiB,UAAU,QAAQ;GAErE;AACD,QAAK,iBAAiB,kBAAkB,qBAAqB;AAC7D,QAAK,iBAAiB,mBAAmB,mBAAmB;AAC5D,QAAK,iBAAiB,gBAAgB,mBAAmB;AACzD,UAAO,MAAM;AACX,gBAAY,aAAa,UAAU;AACnC,SAAK,oBAAoB,kBAAkB,qBAAqB;AAChE,SAAK,oBAAoB,mBAAmB,mBAAmB;AAC/D,SAAK,oBAAoB,gBAAgB,mBAAmB;GAC7D;EACF,MACC,MAAK,gBAAgB;CAExB,GAAE,CAAC,MAAM,IAAK,EAAC;AAChB,QAAO;EACL,WAAW,CAAC,WAAW,kBAAmB,EAAC,SAAS,MAAM;EAC1D,KAAK,aAAO,YAAY,CAAC,UAAU;AACjC,aAAU,UAAU,QAAQ,iBAAiB,MAAM,GAAG;AACtD,WAAQ,MAAM;EACf,GAAE,CAAE,EAAC;CACP;AACF;AACD,SAAS,iBAAiB,QAAQ;AAChC,QAAO,QAAQ,iBAAiB;AACjC;AACD,SAAS,cAAc,SAAS;CAC9B,IAAI,SAAS,OAAO,yBAAyB,QAAQ,OAAO,MAAM,EAAE;CACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,KAAI,QACF,QAAO,QAAQ;AAEjB,UAAS,OAAO,yBAAyB,SAAS,MAAM,EAAE;AAC1D,WAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,KAAI,QACF,QAAO,QAAQ,MAAM;AAEvB,QAAO,QAAQ,MAAM,OAAO,QAAQ;AACrC;;;;ACtHD,IAAI,YAAY;AAChB,IAAI,CAAC,mBAAmB,gBAAgB,GAAG,mBAAmB,WAAW,CACvE,2BACD,EAAC;AACF,IAAI,2BAA2B,6BAA6B;AAC5D,IAAI,CAAC,cAAc,eAAe,GAAG,kBAAkB,UAAU;AACjE,IAAI,OAAO,aAAM,WACf,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,aACA,OAAO,WACP,eACA,cACA,cAAc,cACd,KACA,iBAAiB,YACjB,GAAG,WACJ,GAAG;CACJ,MAAM,YAAY,aAAa,IAAI;CACnC,MAAM,CAAC,OAAO,SAAS,GAAG,qBAAqB;EAC7C,MAAM;EACN,UAAU;EACV,aAAa,gBAAgB;EAC7B,QAAQ;CACT,EAAC;AACF,wBAAuB,4BACrB,cACA;EACE,OAAO;EACP,QAAQ,OAAO;EACf;EACA,eAAe;EACf;EACA,KAAK;EACL;EACA,0BAA0B,4BACxB,UAAU,KACV;GACE,KAAK;GACL,oBAAoB;GACpB,GAAG;GACH,KAAK;EACN,EACF;CACF,EACF;AACF,EACF;AACD,KAAK,cAAc;AACnB,IAAI,gBAAgB;AACpB,IAAI,WAAW,aAAM,WACnB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,aAAa,OAAO,KAAM,GAAG,WAAW,GAAG;CACnD,MAAM,UAAU,eAAe,eAAe,YAAY;CAC1D,MAAM,wBAAwB,yBAAyB,YAAY;AACnE,wBAAuB,kCAErB;EACE,SAAS;EACT,GAAG;EACH,aAAa,QAAQ;EACrB,KAAK,QAAQ;EACb;EACA,0BAA0B,4BACxB,UAAU,KACV;GACE,MAAM;GACN,oBAAoB,QAAQ;GAC5B,GAAG;GACH,KAAK;EACN,EACF;CACF,EACF;AACF,EACF;AACD,SAAS,cAAc;AACvB,IAAI,eAAe;AACnB,IAAI,cAAc,aAAM,WACtB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,aAAa,OAAO,WAAW,MAAO,GAAG,cAAc,GAAG;CAClE,MAAM,UAAU,eAAe,cAAc,YAAY;CACzD,MAAM,wBAAwB,yBAAyB,YAAY;CACnE,MAAM,YAAY,cAAc,QAAQ,QAAQ,MAAM;CACtD,MAAM,YAAY,cAAc,QAAQ,QAAQ,MAAM;CACtD,MAAM,aAAa,UAAU,QAAQ;AACrC,wBAAuB,kCAErB;EACE,SAAS;EACT,GAAG;EACH,YAAY;EACZ,QAAQ;EACR,0BAA0B,4BACxB,UAAU,QACV;GACE,MAAM;GACN,MAAM;GACN,iBAAiB;GACjB,iBAAiB;GACjB,cAAc,aAAa,WAAW;GACtC,iBAAiB,WAAW,UAAU;GACtC;GACA,IAAI;GACJ,GAAG;GACH,KAAK;GACL,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,SAAK,YAAY,MAAM,WAAW,KAAK,MAAM,YAAY,MACvD,SAAQ,cAAc,MAAM;QAE5B,OAAM,gBAAgB;GAEzB,EAAC;GACF,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,QAAI,CAAC,KAAK,OAAQ,EAAC,SAAS,MAAM,IAAI,CAAE,SAAQ,cAAc,MAAM;GACrE,EAAC;GACF,SAAS,qBAAqB,MAAM,SAAS,MAAM;IACjD,MAAM,wBAAwB,QAAQ,mBAAmB;AACzD,SAAK,eAAe,YAAY,sBAC9B,SAAQ,cAAc,MAAM;GAE/B,EAAC;EACH,EACF;CACF,EACF;AACF,EACF;AACD,YAAY,cAAc;AAC1B,IAAI,eAAe;AACnB,IAAI,cAAc,aAAM,WACtB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,aAAa,OAAO,YAAY,SAAU,GAAG,cAAc,GAAG;CACtE,MAAM,UAAU,eAAe,cAAc,YAAY;CACzD,MAAM,YAAY,cAAc,QAAQ,QAAQ,MAAM;CACtD,MAAM,YAAY,cAAc,QAAQ,QAAQ,MAAM;CACtD,MAAM,aAAa,UAAU,QAAQ;CACrC,MAAM,+BAA+B,aAAM,OAAO,WAAW;AAC7D,cAAM,UAAU,MAAM;EACpB,MAAM,MAAM,sBAAsB,MAAM,6BAA6B,UAAU,MAAM;AACrF,SAAO,MAAM,qBAAqB,IAAI;CACvC,GAAE,CAAE,EAAC;AACN,wBAAuB,4BAAI,UAAU;EAAE,SAAS,cAAc;EAAY,UAAU,CAAC,EAAE,SAAS,qBAAqB,4BACnH,UAAU,KACV;GACE,cAAc,aAAa,WAAW;GACtC,oBAAoB,QAAQ;GAC5B,MAAM;GACN,mBAAmB;GACnB,SAAS;GACT,IAAI;GACJ,UAAU;GACV,GAAG;GACH,KAAK;GACL,OAAO;IACL,GAAG,MAAM;IACT,mBAAmB,6BAA6B,UAAU,YAAY;GACvE;GACD,UAAU,WAAW;EACtB,EACF;CAAE,EAAC;AACL,EACF;AACD,YAAY,cAAc;AAC1B,SAAS,cAAc,QAAQ,OAAO;AACpC,WAAU,OAAO,WAAW;AAC7B;AACD,SAAS,cAAc,QAAQ,OAAO;AACpC,WAAU,OAAO,WAAW;AAC7B;AACD,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,UAAU"}