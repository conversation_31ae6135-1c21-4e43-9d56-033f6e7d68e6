{"version": 3, "file": "@radix-ui_react-slider.js", "names": ["React", "React", "useLayoutEffect", "Node", "createCollectionScope", "useCollection"], "sources": ["../../@radix-ui/react-slider/node_modules/@radix-ui/number/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/primitive/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/react-context/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/react-direction/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/react-use-previous/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/react-use-size/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/react-primitive/dist/index.mjs", "../../@radix-ui/react-slider/node_modules/@radix-ui/react-collection/dist/index.mjs", "../../@radix-ui/react-slider/dist/index.mjs"], "sourcesContent": ["// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport {\n  clamp\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-size/src/use-size.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction useSize(element) {\n  const [size, setSize] = React.useState(void 0);\n  useLayoutEffect(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\nexport {\n  useSize\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/collection-legacy.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createCollection(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: /* @__PURE__ */ new Map() }\n  );\n  const CollectionProvider = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef(null);\n    const itemMap = React.useRef(/* @__PURE__ */ new Map()).current;\n    return /* @__PURE__ */ jsx(CollectionProviderImpl, { scope, itemMap, collectionRef: ref, children });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...itemData });\n        return () => void context.itemMap.delete(ref);\n      });\n      return /* @__PURE__ */ jsx(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useCollection(scope) {\n    const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope\n  ];\n}\n\n// src/collection.tsx\nimport React2 from \"react\";\nimport { createContextScope as createContextScope2 } from \"@radix-ui/react-context\";\nimport { useComposedRefs as useComposedRefs2 } from \"@radix-ui/react-compose-refs\";\nimport { createSlot as createSlot2 } from \"@radix-ui/react-slot\";\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n  #keys;\n  constructor(entries) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n  set(key, value) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n  insert(index, key, value) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n    if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n    const size = this.size + (has ? 0 : 1);\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n    const keys = [...this.#keys];\n    let nextValue;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i];\n        if (keys[i] === key) {\n          nextKey = keys[i + 1];\n        }\n        if (has) {\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1];\n        const currentValue = nextValue;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n  with(index, key, value) {\n    const copy = new _OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n  before(key) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n  after(key) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n  first() {\n    return this.entryAt(0);\n  }\n  last() {\n    return this.entryAt(-1);\n  }\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n  delete(key) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n  deleteAt(index) {\n    const key = this.keyAt(index);\n    if (key !== void 0) {\n      return this.delete(key);\n    }\n    return false;\n  }\n  at(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return this.get(key);\n    }\n  }\n  entryAt(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return [key, this.get(key)];\n    }\n  }\n  indexOf(key) {\n    return this.#keys.indexOf(key);\n  }\n  keyAt(index) {\n    return at(this.#keys, index);\n  }\n  from(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n  keyFrom(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n  find(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return void 0;\n  }\n  findIndex(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n  filter(predicate, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  map(callbackfn, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  reduce(...args) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0);\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n  reduceRight(...args) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1);\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index);\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n  toSorted(compareFn) {\n    const entries = [...this.entries()].sort(compareFn);\n    return new _OrderedDict(entries);\n  }\n  toReversed() {\n    const reversed = new _OrderedDict();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n  toSpliced(...args) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new _OrderedDict(entries);\n  }\n  slice(start, end) {\n    const result = new _OrderedDict();\n    let stop = this.size - 1;\n    if (start === void 0) {\n      return result;\n    }\n    if (start < 0) {\n      start = start + this.size;\n    }\n    if (end !== void 0 && end > 0) {\n      stop = end - 1;\n    }\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      result.set(key, element);\n    }\n    return result;\n  }\n  every(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n  some(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n};\nfunction at(array, index) {\n  if (\"at\" in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n\n// src/collection.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction createCollection2(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope2(PROVIDER_NAME);\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0\n    }\n  );\n  const CollectionProvider = ({ state, ...props }) => {\n    return state ? /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state }) : /* @__PURE__ */ jsx2(CollectionInit, { ...props });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const CollectionInit = (props) => {\n    const state = useInitCollection();\n    return /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state });\n  };\n  CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n  const CollectionProviderImpl = (props) => {\n    const { scope, children, state } = props;\n    const ref = React2.useRef(null);\n    const [collectionElement, setCollectionElement] = React2.useState(\n      null\n    );\n    const composeRefs = useComposedRefs2(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n    React2.useEffect(() => {\n      if (!collectionElement) return;\n      const observer = getChildListObserver(() => {\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n    return /* @__PURE__ */ jsx2(\n      CollectionContextProvider,\n      {\n        scope,\n        itemMap,\n        setItemMap,\n        collectionRef: composeRefs,\n        collectionRefObject: ref,\n        collectionElement,\n        children\n      }\n    );\n  };\n  CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot2(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs2(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx2(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot2(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React2.useRef(null);\n      const [element, setElement] = React2.useState(null);\n      const composedRefs = useComposedRefs2(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      const { setItemMap } = context;\n      const itemDataRef = React2.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n      React2.useEffect(() => {\n        const itemData2 = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n          if (!map.has(element)) {\n            map.set(element, { ...itemData2, element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n          return map.set(element, { ...itemData2, element }).toSorted(sortByDocumentPosition);\n        });\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n      return /* @__PURE__ */ jsx2(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useInitCollection() {\n    return React2.useState(new OrderedDict());\n  }\n  function useCollection(scope) {\n    const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n    return itemMap;\n  }\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection\n  };\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions\n  ];\n}\nfunction shallowEqual(a, b) {\n  if (a === b) return true;\n  if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\nfunction isElementPreceding(a, b) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n  return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === \"childList\") {\n        callback();\n        return;\n      }\n    }\n  });\n  return observer;\n}\nexport {\n  createCollection,\n  createCollection2 as unstable_createCollection\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/slider.tsx\nimport * as React from \"react\";\nimport { clamp } from \"@radix-ui/number\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_KEYS = [\"PageUp\", \"PageDown\"];\nvar ARROW_KEYS = [\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"];\nvar BACK_KEYS = {\n  \"from-left\": [\"Home\", \"PageDown\", \"ArrowDown\", \"ArrowLeft\"],\n  \"from-right\": [\"Home\", \"PageDown\", \"ArrowDown\", \"ArrowRight\"],\n  \"from-bottom\": [\"Home\", \"PageDown\", \"ArrowDown\", \"ArrowLeft\"],\n  \"from-top\": [\"Home\", \"PageDown\", \"ArrowUp\", \"ArrowLeft\"]\n};\nvar SLIDER_NAME = \"Slider\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(SLIDER_NAME);\nvar [createSliderContext, createSliderScope] = createContextScope(SLIDER_NAME, [\n  createCollectionScope\n]);\nvar [SliderProvider, useSliderContext] = createSliderContext(SLIDER_NAME);\nvar Slider = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      name,\n      min = 0,\n      max = 100,\n      step = 1,\n      orientation = \"horizontal\",\n      disabled = false,\n      minStepsBetweenThumbs = 0,\n      defaultValue = [min],\n      value,\n      onValueChange = () => {\n      },\n      onValueCommit = () => {\n      },\n      inverted = false,\n      form,\n      ...sliderProps\n    } = props;\n    const thumbRefs = React.useRef(/* @__PURE__ */ new Set());\n    const valueIndexToChangeRef = React.useRef(0);\n    const isHorizontal = orientation === \"horizontal\";\n    const SliderOrientation = isHorizontal ? SliderHorizontal : SliderVertical;\n    const [values = [], setValues] = useControllableState({\n      prop: value,\n      defaultProp: defaultValue,\n      onChange: (value2) => {\n        const thumbs = [...thumbRefs.current];\n        thumbs[valueIndexToChangeRef.current]?.focus();\n        onValueChange(value2);\n      }\n    });\n    const valuesBeforeSlideStartRef = React.useRef(values);\n    function handleSlideStart(value2) {\n      const closestIndex = getClosestValueIndex(values, value2);\n      updateValues(value2, closestIndex);\n    }\n    function handleSlideMove(value2) {\n      updateValues(value2, valueIndexToChangeRef.current);\n    }\n    function handleSlideEnd() {\n      const prevValue = valuesBeforeSlideStartRef.current[valueIndexToChangeRef.current];\n      const nextValue = values[valueIndexToChangeRef.current];\n      const hasChanged = nextValue !== prevValue;\n      if (hasChanged) onValueCommit(values);\n    }\n    function updateValues(value2, atIndex, { commit } = { commit: false }) {\n      const decimalCount = getDecimalCount(step);\n      const snapToStep = roundValue(Math.round((value2 - min) / step) * step + min, decimalCount);\n      const nextValue = clamp(snapToStep, [min, max]);\n      setValues((prevValues = []) => {\n        const nextValues = getNextSortedValues(prevValues, nextValue, atIndex);\n        if (hasMinStepsBetweenValues(nextValues, minStepsBetweenThumbs * step)) {\n          valueIndexToChangeRef.current = nextValues.indexOf(nextValue);\n          const hasChanged = String(nextValues) !== String(prevValues);\n          if (hasChanged && commit) onValueCommit(nextValues);\n          return hasChanged ? nextValues : prevValues;\n        } else {\n          return prevValues;\n        }\n      });\n    }\n    return /* @__PURE__ */ jsx(\n      SliderProvider,\n      {\n        scope: props.__scopeSlider,\n        name,\n        disabled,\n        min,\n        max,\n        valueIndexToChangeRef,\n        thumbs: thumbRefs.current,\n        values,\n        orientation,\n        form,\n        children: /* @__PURE__ */ jsx(Collection.Provider, { scope: props.__scopeSlider, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeSlider, children: /* @__PURE__ */ jsx(\n          SliderOrientation,\n          {\n            \"aria-disabled\": disabled,\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...sliderProps,\n            ref: forwardedRef,\n            onPointerDown: composeEventHandlers(sliderProps.onPointerDown, () => {\n              if (!disabled) valuesBeforeSlideStartRef.current = values;\n            }),\n            min,\n            max,\n            inverted,\n            onSlideStart: disabled ? void 0 : handleSlideStart,\n            onSlideMove: disabled ? void 0 : handleSlideMove,\n            onSlideEnd: disabled ? void 0 : handleSlideEnd,\n            onHomeKeyDown: () => !disabled && updateValues(min, 0, { commit: true }),\n            onEndKeyDown: () => !disabled && updateValues(max, values.length - 1, { commit: true }),\n            onStepKeyDown: ({ event, direction: stepDirection }) => {\n              if (!disabled) {\n                const isPageKey = PAGE_KEYS.includes(event.key);\n                const isSkipKey = isPageKey || event.shiftKey && ARROW_KEYS.includes(event.key);\n                const multiplier = isSkipKey ? 10 : 1;\n                const atIndex = valueIndexToChangeRef.current;\n                const value2 = values[atIndex];\n                const stepInDirection = step * multiplier * stepDirection;\n                updateValues(value2 + stepInDirection, atIndex, { commit: true });\n              }\n            }\n          }\n        ) }) })\n      }\n    );\n  }\n);\nSlider.displayName = SLIDER_NAME;\nvar [SliderOrientationProvider, useSliderOrientationContext] = createSliderContext(SLIDER_NAME, {\n  startEdge: \"left\",\n  endEdge: \"right\",\n  size: \"width\",\n  direction: 1\n});\nvar SliderHorizontal = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      min,\n      max,\n      dir,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const [slider, setSlider] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setSlider(node));\n    const rectRef = React.useRef(void 0);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const isSlidingFromLeft = isDirectionLTR && !inverted || !isDirectionLTR && inverted;\n    function getValueFromPointer(pointerPosition) {\n      const rect = rectRef.current || slider.getBoundingClientRect();\n      const input = [0, rect.width];\n      const output = isSlidingFromLeft ? [min, max] : [max, min];\n      const value = linearScale(input, output);\n      rectRef.current = rect;\n      return value(pointerPosition - rect.left);\n    }\n    return /* @__PURE__ */ jsx(\n      SliderOrientationProvider,\n      {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromLeft ? \"left\" : \"right\",\n        endEdge: isSlidingFromLeft ? \"right\" : \"left\",\n        direction: isSlidingFromLeft ? 1 : -1,\n        size: \"width\",\n        children: /* @__PURE__ */ jsx(\n          SliderImpl,\n          {\n            dir: direction,\n            \"data-orientation\": \"horizontal\",\n            ...sliderProps,\n            ref: composedRefs,\n            style: {\n              ...sliderProps.style,\n              [\"--radix-slider-thumb-transform\"]: \"translateX(-50%)\"\n            },\n            onSlideStart: (event) => {\n              const value = getValueFromPointer(event.clientX);\n              onSlideStart?.(value);\n            },\n            onSlideMove: (event) => {\n              const value = getValueFromPointer(event.clientX);\n              onSlideMove?.(value);\n            },\n            onSlideEnd: () => {\n              rectRef.current = void 0;\n              onSlideEnd?.();\n            },\n            onStepKeyDown: (event) => {\n              const slideDirection = isSlidingFromLeft ? \"from-left\" : \"from-right\";\n              const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n              onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n            }\n          }\n        )\n      }\n    );\n  }\n);\nvar SliderVertical = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      min,\n      max,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const sliderRef = React.useRef(null);\n    const ref = useComposedRefs(forwardedRef, sliderRef);\n    const rectRef = React.useRef(void 0);\n    const isSlidingFromBottom = !inverted;\n    function getValueFromPointer(pointerPosition) {\n      const rect = rectRef.current || sliderRef.current.getBoundingClientRect();\n      const input = [0, rect.height];\n      const output = isSlidingFromBottom ? [max, min] : [min, max];\n      const value = linearScale(input, output);\n      rectRef.current = rect;\n      return value(pointerPosition - rect.top);\n    }\n    return /* @__PURE__ */ jsx(\n      SliderOrientationProvider,\n      {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromBottom ? \"bottom\" : \"top\",\n        endEdge: isSlidingFromBottom ? \"top\" : \"bottom\",\n        size: \"height\",\n        direction: isSlidingFromBottom ? 1 : -1,\n        children: /* @__PURE__ */ jsx(\n          SliderImpl,\n          {\n            \"data-orientation\": \"vertical\",\n            ...sliderProps,\n            ref,\n            style: {\n              ...sliderProps.style,\n              [\"--radix-slider-thumb-transform\"]: \"translateY(50%)\"\n            },\n            onSlideStart: (event) => {\n              const value = getValueFromPointer(event.clientY);\n              onSlideStart?.(value);\n            },\n            onSlideMove: (event) => {\n              const value = getValueFromPointer(event.clientY);\n              onSlideMove?.(value);\n            },\n            onSlideEnd: () => {\n              rectRef.current = void 0;\n              onSlideEnd?.();\n            },\n            onStepKeyDown: (event) => {\n              const slideDirection = isSlidingFromBottom ? \"from-bottom\" : \"from-top\";\n              const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n              onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n            }\n          }\n        )\n      }\n    );\n  }\n);\nvar SliderImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSlider,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onHomeKeyDown,\n      onEndKeyDown,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const context = useSliderContext(SLIDER_NAME, __scopeSlider);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...sliderProps,\n        ref: forwardedRef,\n        onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n          if (event.key === \"Home\") {\n            onHomeKeyDown(event);\n            event.preventDefault();\n          } else if (event.key === \"End\") {\n            onEndKeyDown(event);\n            event.preventDefault();\n          } else if (PAGE_KEYS.concat(ARROW_KEYS).includes(event.key)) {\n            onStepKeyDown(event);\n            event.preventDefault();\n          }\n        }),\n        onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n          const target = event.target;\n          target.setPointerCapture(event.pointerId);\n          event.preventDefault();\n          if (context.thumbs.has(target)) {\n            target.focus();\n          } else {\n            onSlideStart(event);\n          }\n        }),\n        onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {\n          const target = event.target;\n          if (target.hasPointerCapture(event.pointerId)) onSlideMove(event);\n        }),\n        onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n          const target = event.target;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n            onSlideEnd(event);\n          }\n        })\n      }\n    );\n  }\n);\nvar TRACK_NAME = \"SliderTrack\";\nvar SliderTrack = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSlider, ...trackProps } = props;\n    const context = useSliderContext(TRACK_NAME, __scopeSlider);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        \"data-orientation\": context.orientation,\n        ...trackProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nSliderTrack.displayName = TRACK_NAME;\nvar RANGE_NAME = \"SliderRange\";\nvar SliderRange = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSlider, ...rangeProps } = props;\n    const context = useSliderContext(RANGE_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(RANGE_NAME, __scopeSlider);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const valuesCount = context.values.length;\n    const percentages = context.values.map(\n      (value) => convertValueToPercentage(value, context.min, context.max)\n    );\n    const offsetStart = valuesCount > 1 ? Math.min(...percentages) : 0;\n    const offsetEnd = 100 - Math.max(...percentages);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        \"data-orientation\": context.orientation,\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...rangeProps,\n        ref: composedRefs,\n        style: {\n          ...props.style,\n          [orientation.startEdge]: offsetStart + \"%\",\n          [orientation.endEdge]: offsetEnd + \"%\"\n        }\n      }\n    );\n  }\n);\nSliderRange.displayName = RANGE_NAME;\nvar THUMB_NAME = \"SliderThumb\";\nvar SliderThumb = React.forwardRef(\n  (props, forwardedRef) => {\n    const getItems = useCollection(props.__scopeSlider);\n    const [thumb, setThumb] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    const index = React.useMemo(\n      () => thumb ? getItems().findIndex((item) => item.ref.current === thumb) : -1,\n      [getItems, thumb]\n    );\n    return /* @__PURE__ */ jsx(SliderThumbImpl, { ...props, ref: composedRefs, index });\n  }\n);\nvar SliderThumbImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSlider, index, name, ...thumbProps } = props;\n    const context = useSliderContext(THUMB_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(THUMB_NAME, __scopeSlider);\n    const [thumb, setThumb] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    const isFormControl = thumb ? context.form || !!thumb.closest(\"form\") : true;\n    const size = useSize(thumb);\n    const value = context.values[index];\n    const percent = value === void 0 ? 0 : convertValueToPercentage(value, context.min, context.max);\n    const label = getLabel(index, context.values.length);\n    const orientationSize = size?.[orientation.size];\n    const thumbInBoundsOffset = orientationSize ? getThumbInBoundsOffset(orientationSize, percent, orientation.direction) : 0;\n    React.useEffect(() => {\n      if (thumb) {\n        context.thumbs.add(thumb);\n        return () => {\n          context.thumbs.delete(thumb);\n        };\n      }\n    }, [thumb, context.thumbs]);\n    return /* @__PURE__ */ jsxs(\n      \"span\",\n      {\n        style: {\n          transform: \"var(--radix-slider-thumb-transform)\",\n          position: \"absolute\",\n          [orientation.startEdge]: `calc(${percent}% + ${thumbInBoundsOffset}px)`\n        },\n        children: [\n          /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: props.__scopeSlider, children: /* @__PURE__ */ jsx(\n            Primitive.span,\n            {\n              role: \"slider\",\n              \"aria-label\": props[\"aria-label\"] || label,\n              \"aria-valuemin\": context.min,\n              \"aria-valuenow\": value,\n              \"aria-valuemax\": context.max,\n              \"aria-orientation\": context.orientation,\n              \"data-orientation\": context.orientation,\n              \"data-disabled\": context.disabled ? \"\" : void 0,\n              tabIndex: context.disabled ? void 0 : 0,\n              ...thumbProps,\n              ref: composedRefs,\n              style: value === void 0 ? { display: \"none\" } : props.style,\n              onFocus: composeEventHandlers(props.onFocus, () => {\n                context.valueIndexToChangeRef.current = index;\n              })\n            }\n          ) }),\n          isFormControl && /* @__PURE__ */ jsx(\n            SliderBubbleInput,\n            {\n              name: name ?? (context.name ? context.name + (context.values.length > 1 ? \"[]\" : \"\") : void 0),\n              form: context.form,\n              value\n            },\n            index\n          )\n        ]\n      }\n    );\n  }\n);\nSliderThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"RadioBubbleInput\";\nvar SliderBubbleInput = React.forwardRef(\n  ({ __scopeSlider, value, ...props }, forwardedRef) => {\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevValue = usePrevious(value);\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"value\");\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event(\"input\", { bubbles: true });\n        setValue.call(input, value);\n        input.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n    return /* @__PURE__ */ jsx(\n      Primitive.input,\n      {\n        style: { display: \"none\" },\n        ...props,\n        ref: composedRefs,\n        defaultValue: value\n      }\n    );\n  }\n);\nSliderBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getNextSortedValues(prevValues = [], nextValue, atIndex) {\n  const nextValues = [...prevValues];\n  nextValues[atIndex] = nextValue;\n  return nextValues.sort((a, b) => a - b);\n}\nfunction convertValueToPercentage(value, min, max) {\n  const maxSteps = max - min;\n  const percentPerStep = 100 / maxSteps;\n  const percentage = percentPerStep * (value - min);\n  return clamp(percentage, [0, 100]);\n}\nfunction getLabel(index, totalValues) {\n  if (totalValues > 2) {\n    return `Value ${index + 1} of ${totalValues}`;\n  } else if (totalValues === 2) {\n    return [\"Minimum\", \"Maximum\"][index];\n  } else {\n    return void 0;\n  }\n}\nfunction getClosestValueIndex(values, nextValue) {\n  if (values.length === 1) return 0;\n  const distances = values.map((value) => Math.abs(value - nextValue));\n  const closestDistance = Math.min(...distances);\n  return distances.indexOf(closestDistance);\n}\nfunction getThumbInBoundsOffset(width, left, direction) {\n  const halfWidth = width / 2;\n  const halfPercent = 50;\n  const offset = linearScale([0, halfPercent], [0, halfWidth]);\n  return (halfWidth - offset(left) * direction) * direction;\n}\nfunction getStepsBetweenValues(values) {\n  return values.slice(0, -1).map((value, index) => values[index + 1] - value);\n}\nfunction hasMinStepsBetweenValues(values, minStepsBetweenValues) {\n  if (minStepsBetweenValues > 0) {\n    const stepsBetweenValues = getStepsBetweenValues(values);\n    const actualMinStepsBetweenValues = Math.min(...stepsBetweenValues);\n    return actualMinStepsBetweenValues >= minStepsBetweenValues;\n  }\n  return true;\n}\nfunction linearScale(input, output) {\n  return (value) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\nfunction getDecimalCount(value) {\n  return (String(value).split(\".\")[1] || \"\").length;\n}\nfunction roundValue(value, decimalCount) {\n  const rounder = Math.pow(10, decimalCount);\n  return Math.round(value * rounder) / rounder;\n}\nvar Root = Slider;\nvar Track = SliderTrack;\nvar Range = SliderRange;\nvar Thumb = SliderThumb;\nexport {\n  Range,\n  Root,\n  Slider,\n  SliderRange,\n  SliderThumb,\n  SliderTrack,\n  Thumb,\n  Track,\n  createSliderScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "mappings": ";;;;;;;;;;;AACA,SAAS,MAAM,OAAO,CAAC,KAAK,IAAI,EAAE;AAChC,QAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM,CAAC;AAC3C;;;;ACFD,SAAS,qBAAqB,sBAAsB,iBAAiB,EAAE,2BAA2B,MAAM,GAAG,CAAE,GAAE;AAC7G,QAAO,SAAS,YAAY,OAAO;AACjC,yBAAuB,MAAM;AAC7B,MAAI,6BAA6B,UAAU,MAAM,iBAC/C,QAAO,kBAAkB,MAAM;CAElC;AACF;;;;;ACND,SAAS,OAAO,KAAK,OAAO;AAC1B,YAAW,QAAQ,WACjB,QAAO,IAAI,MAAM;UACR,QAAQ,QAAQ,aAAa,EACtC,KAAI,UAAU;AAEjB;AACD,SAAS,YAAY,GAAG,MAAM;AAC5B,QAAO,CAAC,SAAS;EACf,IAAI,aAAa;EACjB,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;GACjC,MAAM,UAAU,OAAO,KAAK,KAAK;AACjC,QAAK,qBAAqB,WAAW,WACnC,cAAa;AAEf,UAAO;EACR,EAAC;AACF,MAAI,WACF,QAAO,MAAM;AACX,QAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;IACxC,MAAM,UAAU,SAAS;AACzB,eAAW,WAAW,WACpB,UAAS;QAET,QAAO,KAAK,IAAI,KAAK;GAExB;EACF;CAEJ;AACF;AACD,SAAS,gBAAgB,GAAG,MAAM;AAChC,QAAO,aAAM,YAAY,YAAY,GAAG,KAAK,EAAE,KAAK;AACrD;;;;;AChBD,SAAS,mBAAmB,WAAW,yBAAyB,CAAE,GAAE;CAClE,IAAI,kBAAkB,CAAE;CACxB,SAAS,eAAe,mBAAmB,gBAAgB;EACzD,MAAM,cAAc,aAAM,cAAc,eAAe;EACvD,MAAM,QAAQ,gBAAgB;AAC9B,oBAAkB,CAAC,GAAG,iBAAiB,cAAe;EACtD,MAAM,WAAW,CAAC,UAAU;GAC1B,MAAM,EAAE,OAAO,SAAU,GAAG,SAAS,GAAG;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,QAAQ,aAAM,QAAQ,MAAM,SAAS,OAAO,OAAO,QAAQ,CAAC;AAClE,0BAAuB,4BAAI,QAAQ,UAAU;IAAE;IAAO;GAAU,EAAC;EAClE;AACD,WAAS,cAAc,oBAAoB;EAC3C,SAAS,YAAY,cAAc,OAAO;GACxC,MAAM,UAAU,QAAQ,aAAa,UAAU;GAC/C,MAAM,UAAU,aAAM,WAAW,QAAQ;AACzC,OAAI,QAAS,QAAO;AACpB,OAAI,wBAAwB,EAAG,QAAO;AACtC,SAAM,IAAI,OAAO,IAAI,aAAa,2BAA2B,kBAAkB;EAChF;AACD,SAAO,CAAC,UAAU,WAAY;CAC/B;CACD,MAAM,cAAc,MAAM;EACxB,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,UAAO,aAAM,cAAc,eAAe;EAC3C,EAAC;AACF,SAAO,SAAS,SAAS,OAAO;GAC9B,MAAM,WAAW,QAAQ,cAAc;AACvC,UAAO,aAAM,QACX,OAAO,IAAI,SAAS,cAAc;IAAE,GAAG;KAAQ,YAAY;GAAU,EAAE,IACvE,CAAC,OAAO,QAAS,EAClB;EACF;CACF;AACD,aAAY,YAAY;AACxB,QAAO,CAAC,gBAAgB,qBAAqB,aAAa,GAAG,uBAAuB,AAAC;AACtF;AACD,SAAS,qBAAqB,GAAG,QAAQ;CACvC,MAAM,YAAY,OAAO;AACzB,KAAI,OAAO,WAAW,EAAG,QAAO;CAChC,MAAM,cAAc,MAAM;EACxB,MAAM,aAAa,OAAO,IAAI,CAAC,kBAAkB;GAC/C,UAAU,cAAc;GACxB,WAAW,aAAa;EACzB,GAAE;AACH,SAAO,SAAS,kBAAkB,gBAAgB;GAChD,MAAM,aAAa,WAAW,OAAO,CAAC,aAAa,EAAE,UAAU,WAAW,KAAK;IAC7E,MAAM,aAAa,SAAS,eAAe;IAC3C,MAAM,eAAe,YAAY,SAAS;AAC1C,WAAO;KAAE,GAAG;KAAa,GAAG;IAAc;GAC3C,GAAE,CAAE,EAAC;AACN,UAAO,aAAM,QAAQ,OAAO,IAAI,SAAS,UAAU,cAAc,WAAY,IAAG,CAAC,UAAW,EAAC;EAC9F;CACF;AACD,aAAY,YAAY,UAAU;AAClC,QAAO;AACR;;;;ACzED,IAAI,mBAAmB,YAAY,WAAWA,aAAM,kBAAkB,MAAM,CAC3E;;;;ACAD,IAAI,qBAAqBC,aAAM,uBAAuB,MAAM,CAAC,UAAU,KAAKC;AAC5E,SAAS,qBAAqB,EAC5B,MACA,aACA,WAAW,MAAM,CAChB,GACD,QACD,EAAE;CACD,MAAM,CAAC,kBAAkB,qBAAqB,YAAY,GAAG,qBAAqB;EAChF;EACA;CACD,EAAC;CACF,MAAM,eAAe,cAAc;CACnC,MAAM,QAAQ,eAAe,OAAO;CAC1B;EACR,MAAM,kBAAkB,aAAM,OAAO,cAAc,EAAE;AACrD,eAAM,UAAU,MAAM;GACpB,MAAM,gBAAgB,gBAAgB;AACtC,OAAI,kBAAkB,cAAc;IAClC,MAAM,OAAO,gBAAgB,eAAe;IAC5C,MAAM,KAAK,eAAe,eAAe;AACzC,YAAQ,QACH,OAAO,oBAAoB,KAAK,MAAM,GAAG,4KAC7C;GACF;AACD,mBAAgB,UAAU;EAC3B,GAAE,CAAC,cAAc,MAAO,EAAC;CAC3B;CACD,MAAM,WAAW,aAAM,YACrB,CAAC,cAAc;AACb,MAAI,cAAc;GAChB,MAAM,SAAS,WAAW,UAAU,GAAG,UAAU,KAAK,GAAG;AACzD,OAAI,WAAW,KACb,aAAY,UAAU,OAAO;EAEhC,MACC,qBAAoB,UAAU;CAEjC,GACD;EAAC;EAAc;EAAM;EAAqB;CAAY,EACvD;AACD,QAAO,CAAC,OAAO,QAAS;AACzB;AACD,SAAS,qBAAqB,EAC5B,aACA,UACD,EAAE;CACD,MAAM,CAAC,OAAO,SAAS,GAAG,aAAM,SAAS,YAAY;CACrD,MAAM,eAAe,aAAM,OAAO,MAAM;CACxC,MAAM,cAAc,aAAM,OAAO,SAAS;AAC1C,oBAAmB,MAAM;AACvB,cAAY,UAAU;CACvB,GAAE,CAAC,QAAS,EAAC;AACd,cAAM,UAAU,MAAM;AACpB,MAAI,aAAa,YAAY,OAAO;AAClC,eAAY,UAAU,MAAM;AAC5B,gBAAa,UAAU;EACxB;CACF,GAAE,CAAC,OAAO,YAAa,EAAC;AACzB,QAAO;EAAC;EAAO;EAAU;CAAY;AACtC;AACD,SAAS,WAAW,OAAO;AACzB,eAAc,UAAU;AACzB;AAKD,IAAI,aAAa,OAAO,mBAAmB;;;;ACpE3C,IAAI,mBAAmB,aAAM,mBAAmB,EAAE;AAKlD,SAAS,aAAa,UAAU;CAC9B,MAAM,YAAY,aAAM,WAAW,iBAAiB;AACpD,QAAO,YAAY,aAAa;AACjC;;;;ACTD,SAAS,YAAY,OAAO;CAC1B,MAAM,MAAM,aAAM,OAAO;EAAE;EAAO,UAAU;CAAO,EAAC;AACpD,QAAO,aAAM,QAAQ,MAAM;AACzB,MAAI,IAAI,QAAQ,UAAU,OAAO;AAC/B,OAAI,QAAQ,WAAW,IAAI,QAAQ;AACnC,OAAI,QAAQ,QAAQ;EACrB;AACD,SAAO,IAAI,QAAQ;CACpB,GAAE,CAAC,KAAM,EAAC;AACZ;;;;ACRD,SAAS,QAAQ,SAAS;CACxB,MAAM,CAAC,MAAM,QAAQ,GAAG,aAAM,cAAc,EAAE;AAC9C,kBAAgB,MAAM;AACpB,MAAI,SAAS;AACX,WAAQ;IAAE,OAAO,QAAQ;IAAa,QAAQ,QAAQ;GAAc,EAAC;GACrE,MAAM,iBAAiB,IAAI,eAAe,CAAC,YAAY;AACrD,SAAK,MAAM,QAAQ,QAAQ,CACzB;AAEF,SAAK,QAAQ,OACX;IAEF,MAAM,QAAQ,QAAQ;IACtB,IAAI;IACJ,IAAI;AACJ,QAAI,mBAAmB,OAAO;KAC5B,MAAM,kBAAkB,MAAM;KAC9B,MAAM,aAAa,MAAM,QAAQ,gBAAgB,GAAG,gBAAgB,KAAK;AACzE,aAAQ,WAAW;AACnB,cAAS,WAAW;IACrB,OAAM;AACL,aAAQ,QAAQ;AAChB,cAAS,QAAQ;IAClB;AACD,YAAQ;KAAE;KAAO;IAAQ,EAAC;GAC3B;AACD,kBAAe,QAAQ,SAAS,EAAE,KAAK,aAAc,EAAC;AACtD,UAAO,MAAM,eAAe,UAAU,QAAQ;EAC/C,MACC,cAAa,EAAE;CAElB,GAAE,CAAC,OAAQ,EAAC;AACb,QAAO;AACR;;;;;AC/BD,IAAI,QAAQ;CACV;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACD;AACD,IAAI,YAAY,MAAM,OAAO,CAAC,WAAW,SAAS;CAChD,MAAM,OAAO,YAAY,YAAY,OAAO;CAC5C,MAAMC,SAAO,aAAM,WAAW,CAAC,OAAO,iBAAiB;EACrD,MAAM,EAAE,QAAS,GAAG,gBAAgB,GAAG;EACvC,MAAM,OAAO,UAAU,OAAO;AAC9B,aAAW,WAAW,YACpB,QAAO,OAAO,IAAI,WAAW,IAAI;AAEnC,yBAAuB,4BAAI,MAAM;GAAE,GAAG;GAAgB,KAAK;EAAc,EAAC;CAC3E,EAAC;AACF,QAAK,eAAe,YAAY;AAChC,QAAO;EAAE,GAAG;GAAY,OAAOA;CAAM;AACtC,GAAE,CAAE,EAAC;;;;AC5BN,SAAS,iBAAiB,MAAM;CAC9B,MAAM,gBAAgB,OAAO;CAC7B,MAAM,CAAC,yBAAyBC,wBAAsB,GAAG,mBAAmB,cAAc;CAC1F,MAAM,CAAC,wBAAwB,qBAAqB,GAAG,wBACrD,eACA;EAAE,eAAe,EAAE,SAAS,KAAM;EAAE,yBAAyB,IAAI;CAAO,EACzE;CACD,MAAM,qBAAqB,CAAC,UAAU;EACpC,MAAM,EAAE,OAAO,UAAU,GAAG;EAC5B,MAAM,MAAM,qBAAM,OAAO,KAAK;EAC9B,MAAM,UAAU,qBAAM,uBAAuB,IAAI,MAAM,CAAC;AACxD,yBAAuB,4BAAI,wBAAwB;GAAE;GAAO;GAAS,eAAe;GAAK;EAAU,EAAC;CACrG;AACD,oBAAmB,cAAc;CACjC,MAAM,uBAAuB,OAAO;CACpC,MAAM,qBAAqB,WAAW,qBAAqB;CAC3D,MAAM,iBAAiB,qBAAM,WAC3B,CAAC,OAAO,iBAAiB;EACvB,MAAM,EAAE,OAAO,UAAU,GAAG;EAC5B,MAAM,UAAU,qBAAqB,sBAAsB,MAAM;EACjE,MAAM,eAAe,gBAAgB,cAAc,QAAQ,cAAc;AACzE,yBAAuB,4BAAI,oBAAoB;GAAE,KAAK;GAAc;EAAU,EAAC;CAChF,EACF;AACD,gBAAe,cAAc;CAC7B,MAAM,iBAAiB,OAAO;CAC9B,MAAM,iBAAiB;CACvB,MAAM,yBAAyB,WAAW,eAAe;CACzD,MAAM,qBAAqB,qBAAM,WAC/B,CAAC,OAAO,iBAAiB;EACvB,MAAM,EAAE,OAAO,SAAU,GAAG,UAAU,GAAG;EACzC,MAAM,MAAM,qBAAM,OAAO,KAAK;EAC9B,MAAM,eAAe,gBAAgB,cAAc,IAAI;EACvD,MAAM,UAAU,qBAAqB,gBAAgB,MAAM;AAC3D,uBAAM,UAAU,MAAM;AACpB,WAAQ,QAAQ,IAAI,KAAK;IAAE;IAAK,GAAG;GAAU,EAAC;AAC9C,UAAO,WAAW,QAAQ,QAAQ,OAAO,IAAI;EAC9C,EAAC;AACF,yBAAuB,4BAAI,wBAAwB;IAAQ,iBAAiB;GAAM,KAAK;GAAc;EAAU,EAAC;CACjH,EACF;AACD,oBAAmB,cAAc;CACjC,SAASC,gBAAc,OAAO;EAC5B,MAAM,UAAU,qBAAqB,OAAO,sBAAsB,MAAM;EACxE,MAAM,WAAW,qBAAM,YAAY,MAAM;GACvC,MAAM,iBAAiB,QAAQ,cAAc;AAC7C,QAAK,eAAgB,QAAO,CAAE;GAC9B,MAAM,eAAe,MAAM,KAAK,eAAe,kBAAkB,GAAG,eAAe,GAAG,CAAC;GACvF,MAAM,QAAQ,MAAM,KAAK,QAAQ,QAAQ,QAAQ,CAAC;GAClD,MAAM,eAAe,MAAM,KACzB,CAAC,GAAG,MAAM,aAAa,QAAQ,EAAE,IAAI,QAAQ,GAAG,aAAa,QAAQ,EAAE,IAAI,QAAQ,CACpF;AACD,UAAO;EACR,GAAE,CAAC,QAAQ,eAAe,QAAQ,OAAQ,EAAC;AAC5C,SAAO;CACR;AACD,QAAO;EACL;GAAE,UAAU;GAAoB,MAAM;GAAgB,UAAU;EAAoB;EACpFA;EACAD;CACD;AACF;;;;ACtDD,IAAI,YAAY,CAAC,UAAU,UAAW;AACtC,IAAI,aAAa;CAAC;CAAW;CAAa;CAAa;AAAa;AACpE,IAAI,YAAY;CACd,aAAa;EAAC;EAAQ;EAAY;EAAa;CAAY;CAC3D,cAAc;EAAC;EAAQ;EAAY;EAAa;CAAa;CAC7D,eAAe;EAAC;EAAQ;EAAY;EAAa;CAAY;CAC7D,YAAY;EAAC;EAAQ;EAAY;EAAW;CAAY;AACzD;AACD,IAAI,cAAc;AAClB,IAAI,CAAC,YAAY,eAAe,sBAAsB,GAAG,iBAAiB,YAAY;AACtF,IAAI,CAAC,qBAAqB,kBAAkB,GAAG,mBAAmB,aAAa,CAC7E,qBACD,EAAC;AACF,IAAI,CAAC,gBAAgB,iBAAiB,GAAG,oBAAoB,YAAY;AACzE,IAAI,SAAS,aAAM,WACjB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,MACA,MAAM,GACN,MAAM,KACN,OAAO,GACP,cAAc,cACd,WAAW,OACX,wBAAwB,GACxB,eAAe,CAAC,GAAI,GACpB,OACA,gBAAgB,MAAM,CACrB,GACD,gBAAgB,MAAM,CACrB,GACD,WAAW,OACX,KACA,GAAG,aACJ,GAAG;CACJ,MAAM,YAAY,aAAM,uBAAuB,IAAI,MAAM;CACzD,MAAM,wBAAwB,aAAM,OAAO,EAAE;CAC7C,MAAM,eAAe,gBAAgB;CACrC,MAAM,oBAAoB,eAAe,mBAAmB;CAC5D,MAAM,CAAC,SAAS,CAAE,GAAE,UAAU,GAAG,qBAAqB;EACpD,MAAM;EACN,aAAa;EACb,UAAU,CAAC,WAAW;GACpB,MAAM,SAAS,CAAC,GAAG,UAAU,OAAQ;AACrC,UAAO,sBAAsB,UAAU,OAAO;AAC9C,iBAAc,OAAO;EACtB;CACF,EAAC;CACF,MAAM,4BAA4B,aAAM,OAAO,OAAO;CACtD,SAAS,iBAAiB,QAAQ;EAChC,MAAM,eAAe,qBAAqB,QAAQ,OAAO;AACzD,eAAa,QAAQ,aAAa;CACnC;CACD,SAAS,gBAAgB,QAAQ;AAC/B,eAAa,QAAQ,sBAAsB,QAAQ;CACpD;CACD,SAAS,iBAAiB;EACxB,MAAM,YAAY,0BAA0B,QAAQ,sBAAsB;EAC1E,MAAM,YAAY,OAAO,sBAAsB;EAC/C,MAAM,aAAa,cAAc;AACjC,MAAI,WAAY,eAAc,OAAO;CACtC;CACD,SAAS,aAAa,QAAQ,SAAS,EAAE,QAAQ,GAAG,EAAE,QAAQ,MAAO,GAAE;EACrE,MAAM,eAAe,gBAAgB,KAAK;EAC1C,MAAM,aAAa,WAAW,KAAK,OAAO,SAAS,OAAO,KAAK,GAAG,OAAO,KAAK,aAAa;EAC3F,MAAM,YAAY,MAAM,YAAY,CAAC,KAAK,GAAI,EAAC;AAC/C,YAAU,CAAC,aAAa,CAAE,MAAK;GAC7B,MAAM,aAAa,oBAAoB,YAAY,WAAW,QAAQ;AACtE,OAAI,yBAAyB,YAAY,wBAAwB,KAAK,EAAE;AACtE,0BAAsB,UAAU,WAAW,QAAQ,UAAU;IAC7D,MAAM,aAAa,OAAO,WAAW,KAAK,OAAO,WAAW;AAC5D,QAAI,cAAc,OAAQ,eAAc,WAAW;AACnD,WAAO,aAAa,aAAa;GAClC,MACC,QAAO;EAEV,EAAC;CACH;AACD,wBAAuB,4BACrB,gBACA;EACE,OAAO,MAAM;EACb;EACA;EACA;EACA;EACA;EACA,QAAQ,UAAU;EAClB;EACA;EACA;EACA,0BAA0B,4BAAI,WAAW,UAAU;GAAE,OAAO,MAAM;GAAe,0BAA0B,4BAAI,WAAW,MAAM;IAAE,OAAO,MAAM;IAAe,0BAA0B,4BACtL,mBACA;KACE,iBAAiB;KACjB,iBAAiB,WAAW,UAAU;KACtC,GAAG;KACH,KAAK;KACL,eAAe,qBAAqB,YAAY,eAAe,MAAM;AACnE,WAAK,SAAU,2BAA0B,UAAU;KACpD,EAAC;KACF;KACA;KACA;KACA,cAAc,gBAAgB,IAAI;KAClC,aAAa,gBAAgB,IAAI;KACjC,YAAY,gBAAgB,IAAI;KAChC,eAAe,OAAO,YAAY,aAAa,KAAK,GAAG,EAAE,QAAQ,KAAM,EAAC;KACxE,cAAc,OAAO,YAAY,aAAa,KAAK,OAAO,SAAS,GAAG,EAAE,QAAQ,KAAM,EAAC;KACvF,eAAe,CAAC,EAAE,OAAO,WAAW,eAAe,KAAK;AACtD,WAAK,UAAU;OACb,MAAM,YAAY,UAAU,SAAS,MAAM,IAAI;OAC/C,MAAM,YAAY,aAAa,MAAM,YAAY,WAAW,SAAS,MAAM,IAAI;OAC/E,MAAM,aAAa,YAAY,KAAK;OACpC,MAAM,UAAU,sBAAsB;OACtC,MAAM,SAAS,OAAO;OACtB,MAAM,kBAAkB,OAAO,aAAa;AAC5C,oBAAa,SAAS,iBAAiB,SAAS,EAAE,QAAQ,KAAM,EAAC;MAClE;KACF;IACF,EACF;GAAE,EAAC;EAAE,EAAC;CACR,EACF;AACF,EACF;AACD,OAAO,cAAc;AACrB,IAAI,CAAC,2BAA2B,4BAA4B,GAAG,oBAAoB,aAAa;CAC9F,WAAW;CACX,SAAS;CACT,MAAM;CACN,WAAW;AACZ,EAAC;AACF,IAAI,mBAAmB,aAAM,WAC3B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,KACA,KACA,KACA,UACA,cACA,aACA,YACA,cACA,GAAG,aACJ,GAAG;CACJ,MAAM,CAAC,QAAQ,UAAU,GAAG,aAAM,SAAS,KAAK;CAChD,MAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,UAAU,KAAK,CAAC;CAC7E,MAAM,UAAU,aAAM,YAAY,EAAE;CACpC,MAAM,YAAY,aAAa,IAAI;CACnC,MAAM,iBAAiB,cAAc;CACrC,MAAM,oBAAoB,mBAAmB,aAAa,kBAAkB;CAC5E,SAAS,oBAAoB,iBAAiB;EAC5C,MAAM,OAAO,QAAQ,WAAW,OAAO,uBAAuB;EAC9D,MAAM,QAAQ,CAAC,GAAG,KAAK,KAAM;EAC7B,MAAM,SAAS,oBAAoB,CAAC,KAAK,GAAI,IAAG,CAAC,KAAK,GAAI;EAC1D,MAAM,QAAQ,YAAY,OAAO,OAAO;AACxC,UAAQ,UAAU;AAClB,SAAO,MAAM,kBAAkB,KAAK,KAAK;CAC1C;AACD,wBAAuB,4BACrB,2BACA;EACE,OAAO,MAAM;EACb,WAAW,oBAAoB,SAAS;EACxC,SAAS,oBAAoB,UAAU;EACvC,WAAW,oBAAoB,IAAI;EACnC,MAAM;EACN,0BAA0B,4BACxB,YACA;GACE,KAAK;GACL,oBAAoB;GACpB,GAAG;GACH,KAAK;GACL,OAAO;IACL,GAAG,YAAY;KACd,mCAAmC;GACrC;GACD,cAAc,CAAC,UAAU;IACvB,MAAM,QAAQ,oBAAoB,MAAM,QAAQ;AAChD,mBAAe,MAAM;GACtB;GACD,aAAa,CAAC,UAAU;IACtB,MAAM,QAAQ,oBAAoB,MAAM,QAAQ;AAChD,kBAAc,MAAM;GACrB;GACD,YAAY,MAAM;AAChB,YAAQ,eAAe;AACvB,kBAAc;GACf;GACD,eAAe,CAAC,UAAU;IACxB,MAAM,iBAAiB,oBAAoB,cAAc;IACzD,MAAM,YAAY,UAAU,gBAAgB,SAAS,MAAM,IAAI;AAC/D,oBAAgB;KAAE;KAAO,WAAW,YAAY,KAAK;IAAG,EAAC;GAC1D;EACF,EACF;CACF,EACF;AACF,EACF;AACD,IAAI,iBAAiB,aAAM,WACzB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,KACA,KACA,UACA,cACA,aACA,YACA,cACA,GAAG,aACJ,GAAG;CACJ,MAAM,YAAY,aAAM,OAAO,KAAK;CACpC,MAAM,MAAM,gBAAgB,cAAc,UAAU;CACpD,MAAM,UAAU,aAAM,YAAY,EAAE;CACpC,MAAM,uBAAuB;CAC7B,SAAS,oBAAoB,iBAAiB;EAC5C,MAAM,OAAO,QAAQ,WAAW,UAAU,QAAQ,uBAAuB;EACzE,MAAM,QAAQ,CAAC,GAAG,KAAK,MAAO;EAC9B,MAAM,SAAS,sBAAsB,CAAC,KAAK,GAAI,IAAG,CAAC,KAAK,GAAI;EAC5D,MAAM,QAAQ,YAAY,OAAO,OAAO;AACxC,UAAQ,UAAU;AAClB,SAAO,MAAM,kBAAkB,KAAK,IAAI;CACzC;AACD,wBAAuB,4BACrB,2BACA;EACE,OAAO,MAAM;EACb,WAAW,sBAAsB,WAAW;EAC5C,SAAS,sBAAsB,QAAQ;EACvC,MAAM;EACN,WAAW,sBAAsB,IAAI;EACrC,0BAA0B,4BACxB,YACA;GACE,oBAAoB;GACpB,GAAG;GACH;GACA,OAAO;IACL,GAAG,YAAY;KACd,mCAAmC;GACrC;GACD,cAAc,CAAC,UAAU;IACvB,MAAM,QAAQ,oBAAoB,MAAM,QAAQ;AAChD,mBAAe,MAAM;GACtB;GACD,aAAa,CAAC,UAAU;IACtB,MAAM,QAAQ,oBAAoB,MAAM,QAAQ;AAChD,kBAAc,MAAM;GACrB;GACD,YAAY,MAAM;AAChB,YAAQ,eAAe;AACvB,kBAAc;GACf;GACD,eAAe,CAAC,UAAU;IACxB,MAAM,iBAAiB,sBAAsB,gBAAgB;IAC7D,MAAM,YAAY,UAAU,gBAAgB,SAAS,MAAM,IAAI;AAC/D,oBAAgB;KAAE;KAAO,WAAW,YAAY,KAAK;IAAG,EAAC;GAC1D;EACF,EACF;CACF,EACF;AACF,EACF;AACD,IAAI,aAAa,aAAM,WACrB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EACJ,eACA,cACA,aACA,YACA,eACA,cACA,cACA,GAAG,aACJ,GAAG;CACJ,MAAM,UAAU,iBAAiB,aAAa,cAAc;AAC5D,wBAAuB,4BACrB,UAAU,MACV;EACE,GAAG;EACH,KAAK;EACL,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,OAAI,MAAM,QAAQ,QAAQ;AACxB,kBAAc,MAAM;AACpB,UAAM,gBAAgB;GACvB,WAAU,MAAM,QAAQ,OAAO;AAC9B,iBAAa,MAAM;AACnB,UAAM,gBAAgB;GACvB,WAAU,UAAU,OAAO,WAAW,CAAC,SAAS,MAAM,IAAI,EAAE;AAC3D,kBAAc,MAAM;AACpB,UAAM,gBAAgB;GACvB;EACF,EAAC;EACF,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;GAClE,MAAM,SAAS,MAAM;AACrB,UAAO,kBAAkB,MAAM,UAAU;AACzC,SAAM,gBAAgB;AACtB,OAAI,QAAQ,OAAO,IAAI,OAAO,CAC5B,QAAO,OAAO;OAEd,cAAa,MAAM;EAEtB,EAAC;EACF,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;GAClE,MAAM,SAAS,MAAM;AACrB,OAAI,OAAO,kBAAkB,MAAM,UAAU,CAAE,aAAY,MAAM;EAClE,EAAC;EACF,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;GAC9D,MAAM,SAAS,MAAM;AACrB,OAAI,OAAO,kBAAkB,MAAM,UAAU,EAAE;AAC7C,WAAO,sBAAsB,MAAM,UAAU;AAC7C,eAAW,MAAM;GAClB;EACF,EAAC;CACH,EACF;AACF,EACF;AACD,IAAI,aAAa;AACjB,IAAI,cAAc,aAAM,WACtB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,cAAe,GAAG,YAAY,GAAG;CACzC,MAAM,UAAU,iBAAiB,YAAY,cAAc;AAC3D,wBAAuB,4BACrB,UAAU,MACV;EACE,iBAAiB,QAAQ,WAAW,UAAU;EAC9C,oBAAoB,QAAQ;EAC5B,GAAG;EACH,KAAK;CACN,EACF;AACF,EACF;AACD,YAAY,cAAc;AAC1B,IAAI,aAAa;AACjB,IAAI,cAAc,aAAM,WACtB,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,cAAe,GAAG,YAAY,GAAG;CACzC,MAAM,UAAU,iBAAiB,YAAY,cAAc;CAC3D,MAAM,cAAc,4BAA4B,YAAY,cAAc;CAC1E,MAAM,MAAM,aAAM,OAAO,KAAK;CAC9B,MAAM,eAAe,gBAAgB,cAAc,IAAI;CACvD,MAAM,cAAc,QAAQ,OAAO;CACnC,MAAM,cAAc,QAAQ,OAAO,IACjC,CAAC,UAAU,yBAAyB,OAAO,QAAQ,KAAK,QAAQ,IAAI,CACrE;CACD,MAAM,cAAc,cAAc,IAAI,KAAK,IAAI,GAAG,YAAY,GAAG;CACjE,MAAM,YAAY,MAAM,KAAK,IAAI,GAAG,YAAY;AAChD,wBAAuB,4BACrB,UAAU,MACV;EACE,oBAAoB,QAAQ;EAC5B,iBAAiB,QAAQ,WAAW,UAAU;EAC9C,GAAG;EACH,KAAK;EACL,OAAO;GACL,GAAG,MAAM;IACR,YAAY,YAAY,cAAc;IACtC,YAAY,UAAU,YAAY;EACpC;CACF,EACF;AACF,EACF;AACD,YAAY,cAAc;AAC1B,IAAI,aAAa;AACjB,IAAI,cAAc,aAAM,WACtB,CAAC,OAAO,iBAAiB;CACvB,MAAM,WAAW,cAAc,MAAM,cAAc;CACnD,MAAM,CAAC,OAAO,SAAS,GAAG,aAAM,SAAS,KAAK;CAC9C,MAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,SAAS,KAAK,CAAC;CAC5E,MAAM,QAAQ,aAAM,QAClB,MAAM,QAAQ,UAAU,CAAC,UAAU,CAAC,SAAS,KAAK,IAAI,YAAY,MAAM,GAAG,IAC3E,CAAC,UAAU,KAAM,EAClB;AACD,wBAAuB,4BAAI,iBAAiB;EAAE,GAAG;EAAO,KAAK;EAAc;CAAO,EAAC;AACpF,EACF;AACD,IAAI,kBAAkB,aAAM,WAC1B,CAAC,OAAO,iBAAiB;CACvB,MAAM,EAAE,eAAe,OAAO,KAAM,GAAG,YAAY,GAAG;CACtD,MAAM,UAAU,iBAAiB,YAAY,cAAc;CAC3D,MAAM,cAAc,4BAA4B,YAAY,cAAc;CAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,aAAM,SAAS,KAAK;CAC9C,MAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,SAAS,KAAK,CAAC;CAC5E,MAAM,gBAAgB,QAAQ,QAAQ,UAAU,MAAM,QAAQ,OAAO,GAAG;CACxE,MAAM,OAAO,QAAQ,MAAM;CAC3B,MAAM,QAAQ,QAAQ,OAAO;CAC7B,MAAM,UAAU,eAAe,IAAI,IAAI,yBAAyB,OAAO,QAAQ,KAAK,QAAQ,IAAI;CAChG,MAAM,QAAQ,SAAS,OAAO,QAAQ,OAAO,OAAO;CACpD,MAAM,kBAAkB,OAAO,YAAY;CAC3C,MAAM,sBAAsB,kBAAkB,uBAAuB,iBAAiB,SAAS,YAAY,UAAU,GAAG;AACxH,cAAM,UAAU,MAAM;AACpB,MAAI,OAAO;AACT,WAAQ,OAAO,IAAI,MAAM;AACzB,UAAO,MAAM;AACX,YAAQ,OAAO,OAAO,MAAM;GAC7B;EACF;CACF,GAAE,CAAC,OAAO,QAAQ,MAAO,EAAC;AAC3B,wBAAuB,6BACrB,QACA;EACE,OAAO;GACL,WAAW;GACX,UAAU;IACT,YAAY,aAAa,OAAO,QAAQ,MAAM,oBAAoB;EACpE;EACD,UAAU,iBACQ,4BAAI,WAAW,UAAU;GAAE,OAAO,MAAM;GAAe,0BAA0B,4BAC/F,UAAU,MACV;IACE,MAAM;IACN,cAAc,MAAM,iBAAiB;IACrC,iBAAiB,QAAQ;IACzB,iBAAiB;IACjB,iBAAiB,QAAQ;IACzB,oBAAoB,QAAQ;IAC5B,oBAAoB,QAAQ;IAC5B,iBAAiB,QAAQ,WAAW,UAAU;IAC9C,UAAU,QAAQ,gBAAgB,IAAI;IACtC,GAAG;IACH,KAAK;IACL,OAAO,eAAe,IAAI,EAAE,SAAS,OAAQ,IAAG,MAAM;IACtD,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,aAAQ,sBAAsB,UAAU;IACzC,EAAC;GACH,EACF;EAAE,EAAC,EACJ,iCAAiC,4BAC/B,mBACA;GACE,MAAM,SAAS,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,OAAO,SAAS,IAAI,OAAO,WAAW;GAC5F,MAAM,QAAQ;GACd;EACD,GACD,MACD,AACF;CACF,EACF;AACF,EACF;AACD,YAAY,cAAc;AAC1B,IAAI,oBAAoB;AACxB,IAAI,oBAAoB,aAAM,WAC5B,CAAC,EAAE,eAAe,MAAO,GAAG,OAAO,EAAE,iBAAiB;CACpD,MAAM,MAAM,aAAM,OAAO,KAAK;CAC9B,MAAM,eAAe,gBAAgB,KAAK,aAAa;CACvD,MAAM,YAAY,YAAY,MAAM;AACpC,cAAM,UAAU,MAAM;EACpB,MAAM,QAAQ,IAAI;AAClB,OAAK,MAAO;EACZ,MAAM,aAAa,OAAO,iBAAiB;EAC3C,MAAM,aAAa,OAAO,yBAAyB,YAAY,QAAQ;EACvE,MAAM,WAAW,WAAW;AAC5B,MAAI,cAAc,SAAS,UAAU;GACnC,MAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,SAAS,KAAM;AAClD,YAAS,KAAK,OAAO,MAAM;AAC3B,SAAM,cAAc,MAAM;EAC3B;CACF,GAAE,CAAC,WAAW,KAAM,EAAC;AACtB,wBAAuB,4BACrB,UAAU,OACV;EACE,OAAO,EAAE,SAAS,OAAQ;EAC1B,GAAG;EACH,KAAK;EACL,cAAc;CACf,EACF;AACF,EACF;AACD,kBAAkB,cAAc;AAChC,SAAS,oBAAoB,aAAa,CAAE,GAAE,WAAW,SAAS;CAChE,MAAM,aAAa,CAAC,GAAG,UAAW;AAClC,YAAW,WAAW;AACtB,QAAO,WAAW,KAAK,CAAC,GAAG,MAAM,IAAI,EAAE;AACxC;AACD,SAAS,yBAAyB,OAAO,KAAK,KAAK;CACjD,MAAM,WAAW,MAAM;CACvB,MAAM,iBAAiB,MAAM;CAC7B,MAAM,aAAa,kBAAkB,QAAQ;AAC7C,QAAO,MAAM,YAAY,CAAC,GAAG,GAAI,EAAC;AACnC;AACD,SAAS,SAAS,OAAO,aAAa;AACpC,KAAI,cAAc,EAChB,SAAQ,QAAQ,QAAQ,EAAE,MAAM;UACvB,gBAAgB,EACzB,QAAO,CAAC,WAAW,SAAU,EAAC;KAE9B,aAAY;AAEf;AACD,SAAS,qBAAqB,QAAQ,WAAW;AAC/C,KAAI,OAAO,WAAW,EAAG,QAAO;CAChC,MAAM,YAAY,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,QAAQ,UAAU,CAAC;CACpE,MAAM,kBAAkB,KAAK,IAAI,GAAG,UAAU;AAC9C,QAAO,UAAU,QAAQ,gBAAgB;AAC1C;AACD,SAAS,uBAAuB,OAAO,MAAM,WAAW;CACtD,MAAM,YAAY,QAAQ;CAC1B,MAAM,cAAc;CACpB,MAAM,SAAS,YAAY,CAAC,GAAG,WAAY,GAAE,CAAC,GAAG,SAAU,EAAC;AAC5D,SAAQ,YAAY,OAAO,KAAK,GAAG,aAAa;AACjD;AACD,SAAS,sBAAsB,QAAQ;AACrC,QAAO,OAAO,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,UAAU,OAAO,QAAQ,KAAK,MAAM;AAC5E;AACD,SAAS,yBAAyB,QAAQ,uBAAuB;AAC/D,KAAI,wBAAwB,GAAG;EAC7B,MAAM,qBAAqB,sBAAsB,OAAO;EACxD,MAAM,8BAA8B,KAAK,IAAI,GAAG,mBAAmB;AACnE,SAAO,+BAA+B;CACvC;AACD,QAAO;AACR;AACD,SAAS,YAAY,OAAO,QAAQ;AAClC,QAAO,CAAC,UAAU;AAChB,MAAI,MAAM,OAAO,MAAM,MAAM,OAAO,OAAO,OAAO,GAAI,QAAO,OAAO;EACpE,MAAM,SAAS,OAAO,KAAK,OAAO,OAAO,MAAM,KAAK,MAAM;AAC1D,SAAO,OAAO,KAAK,SAAS,QAAQ,MAAM;CAC3C;AACF;AACD,SAAS,gBAAgB,OAAO;AAC9B,SAAQ,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI;AAC5C;AACD,SAAS,WAAW,OAAO,cAAc;CACvC,MAAM,UAAU,KAAK,IAAI,IAAI,aAAa;AAC1C,QAAO,KAAK,MAAM,QAAQ,QAAQ,GAAG;AACtC;AACD,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,QAAQ"}