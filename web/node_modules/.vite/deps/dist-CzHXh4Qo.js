import { __toESM } from "./chunk-CSDACtG1.js";
import { require_react } from "./react-jwkZF-4H.js";

//#region node_modules/@radix-ui/react-use-effect-event/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs
var import_react = __toESM(require_react(), 1);
var useLayoutEffect2 = globalThis?.document ? import_react.useLayoutEffect : () => {};

//#endregion
//#region node_modules/@radix-ui/react-use-effect-event/dist/index.mjs
var useReactEffectEvent = import_react[" useEffectEvent ".trim().toString()];
var useReactInsertionEffect = import_react[" useInsertionEffect ".trim().toString()];

//#endregion
//# sourceMappingURL=dist-CzHXh4Qo.js.map