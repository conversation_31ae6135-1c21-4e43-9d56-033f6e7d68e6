{"clientTcpRtt": 14, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "EU", "asn": 24608, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "IT", "isEUCountry": "1", "region": "Sicily", "tlsClientCiphersSha1": "kXrN3VEKDdzz2cPKTQaKzpxVTxQ=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "/1cpBVO3OxB7HRKYrm/kct0A1fitpGhWcvxUh8+tYjE=", "tlsExportedAuthenticator": {"clientFinished": "56e2dbbfdec85d3fa5eb888cb9e45b674ff457dafba1b5578482ca2c2a403361658778500fe5b7a4bdcf33395f6a7d4b", "clientHandshake": "0d2f85ce6873538cd42496efdce08147bbfa618d5dcc60ba2fe37e7c558cedc8c61a774e934b12e3c891b8c97d28925e", "serverHandshake": "6cf9971d68615c19051b32b3aa6294b8909b28dc3b90ebc2a5aaffe8d80051c1fa0cac80f879d059d7e035af7a824dea", "serverFinished": "5416764d33e25d658c71bee0d938be3e17a4c7edc4cd633590dd0d4e6f53ef23023cfef2044ab17f36d72396b26e6fbb"}, "tlsClientHelloLength": "1605", "colo": "FCO", "timezone": "Europe/Rome", "longitude": "13.51237", "latitude": "38.07892", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "90011", "city": "Bagheria", "tlsVersion": "TLSv1.3", "regionCode": "82", "asOrganization": "WIND TRE S.P.A.", "tlsClientExtensionsSha1Le": "u4wtEMFQBY18l3BzHAvORm+KGRw=", "tlsClientExtensionsSha1": "1eY97BUYYO8vDaTfHQywB1pcNdM=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}